/* 评语编辑页面 - 恢复原样式 */
.edit-page {
  height: 100vh;
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  padding: 40rpx 32rpx;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 学生信息 - 恢复原样式 */
.student-info {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 48rpx 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
  flex-shrink: 0;
}

.student-name {
  font-size: 64rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #2C3E50 0%, #34495E 50%, #5470C6 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.1);
}

.edit-subtitle {
  font-size: 32rpx;
  color: #73C0DE;
  font-weight: 600;
  letter-spacing: 2rpx;
  opacity: 0.9;
}

/* 编辑容器 - 占满剩余空间 */
.edit-container {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 0;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  margin-bottom: 0;
  position: relative;
  flex-shrink: 0;
}

/* 头部装饰线 */
.edit-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 32rpx;
  right: 32rpx;
  height: 2rpx;
  background: linear-gradient(90deg, rgba(84, 112, 198, 0.2) 0%, rgba(115, 192, 222, 0.3) 50%, rgba(84, 112, 198, 0.2) 100%);
}

.label {
  font-size: 32rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #2C3E50 0%, #5470C6 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 1rpx;
}

.counter {
  font-size: 24rpx;
  color: #73C0DE;
  font-weight: 600;
  background: rgba(115, 192, 222, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(115, 192, 222, 0.2);
}

/* 文本框 - 占满剩余空间 */
.edit-textarea {
  width: 100%;
  flex: 1;
  min-height: 200rpx;
  padding: 24rpx 32rpx 32rpx;
  font-size: 30rpx;
  line-height: 1.7;
  color: #2C3E50;
  background: transparent;
  border: none;
  border-radius: 0;
  outline: none;
  resize: none;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

/* 文本框占位符样式 */
.edit-textarea::placeholder {
  color: rgba(115, 192, 222, 0.6);
  font-style: italic;
}

/* 底部按钮 - 恢复原样式 */
.bottom-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 24rpx;
  flex-shrink: 0;
}

.btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22rpx;
  transition: all 0.3s ease;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.btn-cancel {
  background: rgba(192, 196, 204, 0.2);
  color: #909399;
  border: 1rpx solid rgba(192, 196, 204, 0.3);
}

.btn-save {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.3);
}

.btn-save.disabled {
  background: rgba(192, 196, 204, 0.5);
  color: rgba(255, 255, 255, 0.6);
  box-shadow: none;
}

.btn:active {
  transform: scale(0.98);
}
