import React from 'react'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { ConfigProvider, theme, App as AntdApp } from 'antd'
import MainLayout from './layouts/MainLayout'
import Dashboard from './pages/Dashboard'
import AIConfig from './pages/AIConfig'
import DataManagement from './pages/DataManagement'
import Settings from './pages/Settings'
import Login from './pages/Login'
import { useAuthStore } from './stores/authStore'
import { useThemeStore } from './stores/themeStore'
import zhCN from 'antd/locale/zh_CN'
import './styles/theme.css'

const App: React.FC = () => {
  const { isDark } = useThemeStore()

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
          colorPrimary: '#3b82f6',
          borderRadius: 8,
          colorBgContainer: isDark ? '#1e293b' : '#ffffff',
          colorBgElevated: isDark ? '#334155' : '#ffffff',
          colorText: isDark ? '#f8fafc' : '#1e293b',
          colorTextSecondary: isDark ? '#e2e8f0' : '#64748b',
          colorBorder: isDark ? '#475569' : '#e2e8f0',
        }
      }}
    >
      <AntdApp>
        <BrowserRouter
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true
          }}
        >
          <AppContent />
        </BrowserRouter>
      </AntdApp>
    </ConfigProvider>
  )
}

const AppContent: React.FC = () => {
  const { isAuthenticated } = useAuthStore()

  if (!isAuthenticated) {
    return <Login />
  }

  return (
    <MainLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/ai-config" element={<AIConfig />} />
        <Route path="/data-management" element={<DataManagement />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/login" element={<Login />} />
      </Routes>
    </MainLayout>
  )
}

export default App