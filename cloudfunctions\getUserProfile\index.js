// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: process.env.ENV_ID || 'cloud1-4g85f8xlb8166ff1'
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    // 查询用户信息
    const result = await db.collection('user_profiles')
      .where({
        openid: wxContext.OPENID
      })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get()

    if (result.data && result.data.length > 0) {
      return {
        success: true,
        data: result.data[0].userInfo,
        openid: wxContext.OPENID
      }
    } else {
      return {
        success: false,
        error: '暂无用户信息',
        openid: wxContext.OPENID
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return {
      success: false,
      error: error.message,
      openid: wxContext.OPENID
    }
  }
}