// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: process.env.ENV_ID || 'cloud1-4g85f8xlb8166ff1'
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()

  try {
    console.log('获取用户资料，OpenID:', wxContext.OPENID)

    // 🔥 优先从users集合获取用户信息（包含真实姓名）
    try {
      const usersResult = await db.collection('users').doc(wxContext.OPENID).get()

      if (usersResult.data) {
        console.log('从users集合获取到用户信息:', usersResult.data)

        // 构建完整的用户信息
        const userInfo = {
          name: usersResult.data.name || '', // 真实姓名
          nickName: usersResult.data.nickName || '用户',
          avatarUrl: usersResult.data.avatarUrl || '',
          school: usersResult.data.school || '',
          subject: usersResult.data.subject || '',
          grade: usersResult.data.grade || '',
          phone: usersResult.data.phone || '',
          email: usersResult.data.email || '',
          updateTime: usersResult.data.updateTime
        }

        return {
          success: true,
          data: userInfo,
          openid: wxContext.OPENID,
          source: 'users'
        }
      }
    } catch (usersError) {
      console.warn('从users集合获取用户信息失败:', usersError)
    }

    // 如果users集合没有数据，从user_profiles集合获取
    const result = await db.collection('user_profiles')
      .where({
        openid: wxContext.OPENID
      })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get()

    if (result.data && result.data.length > 0) {
      console.log('从user_profiles集合获取到用户信息')
      return {
        success: true,
        data: result.data[0].userInfo,
        openid: wxContext.OPENID,
        source: 'user_profiles'
      }
    } else {
      return {
        success: false,
        error: '暂无用户信息',
        openid: wxContext.OPENID
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return {
      success: false,
      error: error.message,
      openid: wxContext.OPENID
    }
  }
}