/**
 * 批量生成评语云函数
 * 支持大批量、高稳定性的评语生成任务
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV  
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action, taskId, students, template, batchSize = 10, delay = 2000 } = event;
  
  try {
    switch (action) {
      case 'create':
        return await createBatchTask(students, template, context);
      case 'process':
        return await processBatchTask(taskId, batchSize, delay);
      case 'status':
        return await getBatchTaskStatus(taskId);
      case 'pause':
        return await pauseBatchTask(taskId);
      case 'resume':
        return await resumeBatchTask(taskId);
      case 'cancel':
        return await cancelBatchTask(taskId);
      default:
        throw new Error('无效的操作类型');
    }
  } catch (error) {
    console.error('[batchGenerate] 操作失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 创建批量任务
 */
async function createBatchTask(students, template, context) {
  const taskId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  const task = {
    _id: taskId,
    name: `批量生成任务_${new Date().toLocaleString()}`,
    creator: context.OPENID || 'system',
    students: students,
    template: template,
    totalCount: students.length,
    completedCount: 0,
    failedCount: 0,
    status: 'pending', // pending, running, paused, completed, failed, cancelled
    progress: 0,
    startTime: null,
    endTime: null,
    currentBatch: 0,
    failedStudents: [],
    results: [],
    logs: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  await db.collection('batch_tasks').add({
    data: task
  });
  
  // 添加日志
  await addTaskLog(taskId, '任务创建成功', 'info');
  
  return {
    success: true,
    taskId: taskId,
    message: '批量任务创建成功'
  };
}

/**
 * 处理批量任务
 */
async function processBatchTask(taskId, batchSize, delay) {
  // 获取任务信息
  const taskResult = await db.collection('batch_tasks').doc(taskId).get();
  if (!taskResult.data) {
    throw new Error('任务不存在');
  }
  
  const task = taskResult.data;
  
  if (task.status !== 'pending' && task.status !== 'running') {
    throw new Error(`任务状态不允许处理: ${task.status}`);
  }
  
  // 更新任务状态为运行中
  if (task.status === 'pending') {
    await db.collection('batch_tasks').doc(taskId).update({
      data: {
        status: 'running',
        startTime: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    });
    await addTaskLog(taskId, '任务开始执行', 'info');
  }
  
  // 获取待处理的学生列表
  const remainingStudents = task.students.slice(task.completedCount + task.failedCount);
  
  if (remainingStudents.length === 0) {
    // 任务完成
    await db.collection('batch_tasks').doc(taskId).update({
      data: {
        status: 'completed',
        endTime: new Date().toISOString(),
        progress: 100,
        updatedAt: new Date().toISOString()
      }
    });
    await addTaskLog(taskId, `任务完成，共生成${task.completedCount}条评语`, 'success');
    return {
      success: true,
      status: 'completed',
      message: '任务已完成'
    };
  }
  
  // 处理当前批次
  const currentBatch = remainingStudents.slice(0, batchSize);
  const batchResults = [];
  const batchFailures = [];
  
  for (let i = 0; i < currentBatch.length; i++) {
    const student = currentBatch[i];
    
    try {
      // 调用评语生成函数
      const result = await generateSingleComment(student, task.template);
      
      if (result.success) {
        batchResults.push({
          studentId: student.id,
          studentName: student.name,
          comment: result.comment,
          generatedAt: new Date().toISOString()
        });
        
        // 保存评语到数据库
        await saveCommentToDatabase(student, result.comment, task.template);
        
      } else {
        batchFailures.push({
          studentId: student.id,
          studentName: student.name,
          error: result.error,
          retryCount: 0
        });
      }
      
      // 批次内延迟，避免API限流
      if (i < currentBatch.length - 1) {
        await sleep(500);
      }
      
    } catch (error) {
      console.error(`[batchGenerate] 学生${student.name}评语生成失败:`, error);
      batchFailures.push({
        studentId: student.id,
        studentName: student.name,
        error: error.message,
        retryCount: 0
      });
    }
  }
  
  // 更新任务进度
  const newCompletedCount = task.completedCount + batchResults.length;
  const newFailedCount = task.failedCount + batchFailures.length;
  const newProgress = Math.round((newCompletedCount / task.totalCount) * 100);
  
  await db.collection('batch_tasks').doc(taskId).update({
    data: {
      completedCount: newCompletedCount,
      failedCount: newFailedCount,
      progress: newProgress,
      currentBatch: task.currentBatch + 1,
      results: _.push(batchResults),
      failedStudents: _.push(batchFailures),
      updatedAt: new Date().toISOString()
    }
  });
  
  // 添加日志
  if (batchResults.length > 0) {
    await addTaskLog(taskId, `批次${task.currentBatch + 1}完成，成功生成${batchResults.length}条评语`, 'success');
  }
  if (batchFailures.length > 0) {
    await addTaskLog(taskId, `批次${task.currentBatch + 1}有${batchFailures.length}个失败`, 'warning');
  }
  
  // 检查是否还有剩余任务
  const totalProcessed = newCompletedCount + newFailedCount;
  if (totalProcessed >= task.totalCount) {
    // 任务完成
    await db.collection('batch_tasks').doc(taskId).update({
      data: {
        status: 'completed',
        endTime: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    });
    await addTaskLog(taskId, `任务完成，成功${newCompletedCount}条，失败${newFailedCount}条`, 'info');
    
    return {
      success: true,
      status: 'completed',
      completed: newCompletedCount,
      failed: newFailedCount,
      message: '任务已完成'
    };
  }
  
  // 批次间延迟
  await sleep(delay);
  
  return {
    success: true,
    status: 'running',
    completed: newCompletedCount,
    failed: newFailedCount,
    progress: newProgress,
    message: `批次${task.currentBatch + 1}处理完成`
  };
}

/**
 * 生成单个学生评语
 */
async function generateSingleComment(student, template) {
  try {
    // 调用评语生成云函数
    const result = await cloud.callFunction({
      name: 'generateComment',
      data: {
        studentId: student.id,
        templateType: template,
        performanceMaterial: student.performanceMaterial || '',
        batchMode: true
      }
    });
    
    if (result.result && result.result.success) {
      return {
        success: true,
        comment: result.result.comment
      };
    } else {
      return {
        success: false,
        error: result.result?.error || '生成失败'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 保存评语到数据库
 */
async function saveCommentToDatabase(student, comment, template) {
  try {
    await db.collection('comments').add({
      data: {
        studentId: student.id,
        studentName: student.name,
        content: comment,
        template: template,
        source: 'batch_generate',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('[batchGenerate] 保存评语失败:', error);
    // 不抛出错误，避免影响批量处理
  }
}

/**
 * 获取任务状态
 */
async function getBatchTaskStatus(taskId) {
  const result = await db.collection('batch_tasks').doc(taskId).get();
  if (!result.data) {
    throw new Error('任务不存在');
  }
  
  return {
    success: true,
    task: result.data
  };
}

/**
 * 暂停任务
 */
async function pauseBatchTask(taskId) {
  await db.collection('batch_tasks').doc(taskId).update({
    data: {
      status: 'paused',
      updatedAt: new Date().toISOString()
    }
  });
  
  await addTaskLog(taskId, '任务已暂停', 'warning');
  
  return {
    success: true,
    message: '任务已暂停'
  };
}

/**
 * 恢复任务
 */
async function resumeBatchTask(taskId) {
  await db.collection('batch_tasks').doc(taskId).update({
    data: {
      status: 'running',
      updatedAt: new Date().toISOString()
    }
  });
  
  await addTaskLog(taskId, '任务已恢复', 'info');
  
  return {
    success: true,
    message: '任务已恢复'
  };
}

/**
 * 取消任务
 */
async function cancelBatchTask(taskId) {
  await db.collection('batch_tasks').doc(taskId).update({
    data: {
      status: 'cancelled',
      endTime: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  });
  
  await addTaskLog(taskId, '任务已取消', 'warning');
  
  return {
    success: true,
    message: '任务已取消'
  };
}

/**
 * 添加任务日志
 */
async function addTaskLog(taskId, message, type = 'info') {
  try {
    await db.collection('batch_tasks').doc(taskId).update({
      data: {
        logs: _.push([{
          time: new Date().toLocaleTimeString(),
          message: message,
          type: type,
          timestamp: new Date().toISOString()
        }]),
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('[batchGenerate] 添加日志失败:', error);
  }
}

/**
 * 延迟函数
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
