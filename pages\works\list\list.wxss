/* 我的作品页面样式 - 按照原型图设计 */
.morandi-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  padding: 40rpx 32rpx 200rpx;
  color: #333;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48rpx;
  padding-top: 20rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.header-action {
  font-size: 28rpx;
  cursor: pointer;
  padding: 12rpx 20rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  font-weight: 500;
}

.header-action.primary {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.2);
}

.header-action.secondary {
  color: #EE6666;
  background: rgba(238, 102, 102, 0.1);
  border: 1rpx solid rgba(238, 102, 102, 0.2);
}

.header-action:active {
  transform: scale(0.95);
}

.header-action.primary:active {
  box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.3);
}

.header-action.secondary:active {
  background: rgba(238, 102, 102, 0.15);
}

/* 统计概览 */
.stats-overview {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 48rpx;
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 搜索和筛选 */
.search-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.search-bar {
  position: relative;
  margin-bottom: 24rpx;
}

.search-input {
  width: 100%;
  padding: 20rpx 32rpx 20rpx 80rpx;
  border: 1rpx solid #E4E7ED;
  border-radius: 24rpx;
  font-size: 28rpx;
  background: white;
  outline: none;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  height: 80rpx;
  line-height: 1.2;
  display: flex;
  align-items: center;
}

.search-input:focus {
  border-color: #5470C6;
}

.search-icon {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
}

.filter-tabs {
  display: flex;
  gap: 16rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  background: #F8F9FA;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.filter-tab.active {
  background: #5470C6;
  color: white;
}

/* 作品列表 */
.works-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.work-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 32rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.work-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.work-info {
  flex: 1;
}

.work-student {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.work-meta {
  display: flex;
  align-items: center;
  gap: 24rpx;
  font-size: 24rpx;
  color: #666;
}

.work-score {
  background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.work-content {
  background: #F8F9FA;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.work-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  padding: 16rpx 24rpx;
  border: 1rpx solid #E4E7ED;
  border-radius: 16rpx;
  background: white;
  font-size: 24rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.action-btn:hover {
  border-color: #5470C6;
  color: #5470C6;
}

.action-btn.primary {
  background: #5470C6;
  color: white;
  border-color: #5470C6;
}

/* 质量标签 */
.quality-tag {
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.quality-excellent {
  background: #F6FFED;
  color: #52C41A;
  border: 1rpx solid #B7EB8F;
}

.quality-good {
  background: #E6F4FF;
  color: #1890FF;
  border: 1rpx solid #91D5FF;
}

.quality-normal {
  background: #FFF7E6;
  color: #FA8C16;
  border: 1rpx solid #FFD591;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  line-height: 1.4;
  display: block;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
}

/* 隐藏canvas */
.share-canvas {
  position: fixed;
  top: -4000rpx;
  left: 0;
}