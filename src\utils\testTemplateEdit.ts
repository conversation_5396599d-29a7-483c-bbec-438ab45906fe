/**
 * 测试模板编辑功能
 */

import { updatePromptTemplate, getPromptTemplates } from '../services/authApi'

export const testTemplateEdit = async (templateId: string, newContent: string) => {
  console.log('🧪 开始测试模板编辑功能...')
  
  try {
    // 1. 获取当前模板数据
    console.log('📥 1. 获取当前模板数据...')
    const templatesResult = await getPromptTemplates()
    const currentTemplate = templatesResult.data?.find((t: any) => t.id === templateId)
    
    if (!currentTemplate) {
      console.error('❌ 未找到指定模板:', templateId)
      return { success: false, error: '模板不存在' }
    }
    
    console.log('📋 当前模板:', currentTemplate)
    
    // 2. 准备更新数据
    const updateData = {
      id: templateId,
      name: currentTemplate.name,
      type: currentTemplate.type,
      content: newContent, // 修改内容
      description: currentTemplate.description + ' (已修改)',
      enabled: true
    }
    
    console.log('🔧 2. 准备更新数据:', updateData)
    
    // 3. 执行更新
    console.log('💾 3. 执行更新...')
    const updateResult = await updatePromptTemplate(updateData)
    console.log('📤 更新结果:', updateResult)
    
    // 4. 验证更新结果
    console.log('🔍 4. 验证更新结果...')
    const verifyResult = await getPromptTemplates()
    const updatedTemplate = verifyResult.data?.find((t: any) => t.id === templateId)
    
    console.log('📋 更新后模板:', updatedTemplate)
    
    const isContentUpdated = updatedTemplate?.content === newContent
    const isDescriptionUpdated = updatedTemplate?.description.includes('(已修改)')
    
    console.log('✅ 内容是否更新:', isContentUpdated)
    console.log('✅ 描述是否更新:', isDescriptionUpdated)
    
    return {
      success: true,
      updateResult,
      verification: {
        isContentUpdated,
        isDescriptionUpdated,
        originalTemplate: currentTemplate,
        updatedTemplate
      }
    }
    
  } catch (error) {
    console.error('❌ 测试编辑功能失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 添加到 window 对象以便在控制台调用
(window as any).testTemplateEdit = testTemplateEdit