.simple-loading {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.simple-loading--vertical {
  flex-direction: column;
}

.simple-loading-spinner {
  position: relative;
  display: inline-block;
}

.simple-loading-circular {
  width: 100%;
  height: 100%;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: simple-loading-rotate 1s linear infinite;
}

.simple-loading-dot {
  width: 100%;
  height: 100%;
  background: currentColor;
  border-radius: 50%;
  animation: simple-loading-pulse 1.2s ease-in-out infinite;
}

.simple-loading-text {
  margin-top: 8px;
  font-size: 14px;
  color: #969799;
}

@keyframes simple-loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes simple-loading-pulse {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}