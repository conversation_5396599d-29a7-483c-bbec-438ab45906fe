import React, { useState } from 'react'
import { Card, Button, Typography, Spin, Alert, Descriptions, Tag } from 'antd'
import { BugOutlined, ReloadOutlined } from '@ant-design/icons'
import cloudbaseService from '../utils/cloudbaseConfig'

const { Title, Text } = Typography

interface DebugResult {
  timestamp: string
  checks: Array<{
    name: string
    value: any
    description: string
  }>
}

const AIStatsDebugger: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [debugResult, setDebugResult] = useState<DebugResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  const runDebug = async () => {
    setLoading(true)
    setError(null)
    
    try {
      console.log('🔍 开始调试AI统计问题...')
      
      const result = await cloudbaseService.callFunction('dataQuery', {
        action: 'debugAICallsStats'
      })
      
      console.log('🔍 调试结果:', result)
      
      if (result.code === 200) {
        setDebugResult(result.data)
      } else {
        throw new Error(result.message || '调试失败')
      }
    } catch (err: any) {
      console.error('❌ 调试失败:', err)
      setError(err.message || '调试过程中发生错误')
    } finally {
      setLoading(false)
    }
  }

  const renderValue = (value: any) => {
    if (typeof value === 'object' && value !== null) {
      return (
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '8px', 
          borderRadius: '4px',
          fontSize: '12px',
          maxHeight: '200px',
          overflow: 'auto'
        }}>
          {JSON.stringify(value, null, 2)}
        </pre>
      )
    }
    
    if (typeof value === 'number') {
      return <Tag color="blue">{value}</Tag>
    }
    
    if (value === 'ERROR') {
      return <Tag color="red">ERROR</Tag>
    }
    
    return <Text code>{String(value)}</Text>
  }

  return (
    <Card 
      title={
        <div className="flex items-center space-x-2">
          <BugOutlined />
          <span>AI统计调试工具</span>
        </div>
      }
      extra={
        <Button 
          type="primary" 
          icon={<ReloadOutlined />}
          loading={loading}
          onClick={runDebug}
        >
          运行调试
        </Button>
      }
    >
      <div className="space-y-4">
        <Alert
          message="调试说明"
          description="此工具用于排查AI调用次数统计不准确的问题。点击'运行调试'按钮查看各个数据源的统计情况。"
          type="info"
          showIcon
        />

        {loading && (
          <div className="text-center py-8">
            <Spin size="large" />
            <div className="mt-2">正在调试统计数据...</div>
          </div>
        )}

        {error && (
          <Alert
            message="调试失败"
            description={error}
            type="error"
            showIcon
          />
        )}

        {debugResult && (
          <div className="space-y-4">
            <Title level={4}>调试结果</Title>
            <Text type="secondary">调试时间: {debugResult.timestamp}</Text>
            
            <div className="space-y-4">
              {debugResult.checks.map((check, index) => (
                <Card key={index} size="small" title={check.name}>
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="值">
                      {renderValue(check.value)}
                    </Descriptions.Item>
                    <Descriptions.Item label="说明">
                      {check.description}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              ))}
            </div>

            {/* 分析建议 */}
            <Card title="分析建议" size="small">
              <div className="space-y-2">
                <Text>
                  <strong>正常情况：</strong>comments集合的记录数应该等于实际的AI调用次数
                </Text>
                <Text>
                  <strong>如果数字不匹配：</strong>可能存在重复统计或数据源不一致的问题
                </Text>
                <Text>
                  <strong>解决方案：</strong>统一使用comments集合作为AI调用次数的唯一数据源
                </Text>
              </div>
            </Card>
          </div>
        )}
      </div>
    </Card>
  )
}

export default AIStatsDebugger
