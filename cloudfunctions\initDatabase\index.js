// 初始化数据库 - 创建所有必要的集合和初始数据
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  console.log('开始检查数据库状态...')

  try {
    const results = []

    // 首先检查现有集合
    console.log('检查现有数据库集合...')

    // 检查实际存在的集合和需要的集合
    const actualCollections = [
      'admins', 'ai_configs', 'ai_error_logs', 'ai_generation_logs', 'ai_usage',
      'classes', 'comments', 'files', 'logs', 'records', 'settings',
      'students', 'system_config'
    ]

    const neededCollections = [
      'admins', 'ai_configs', 'settings', 'logs', 'students', 'classes',
      'comments', 'records', 'files', 'ai_usage', 'ai_error_logs',
      'ai_generation_logs', 'system_config', 'notifications',
      'behavior_records', 'user_profiles', 'user_consent_records'
    ]

    const existingCollections = []
    const missingCollections = []

    for (const collectionName of neededCollections) {
      try {
        const count = await db.collection(collectionName).count()
        existingCollections.push({
          name: collectionName,
          count: count.total,
          status: 'exists'
        })
        console.log(`✅ ${collectionName}: ${count.total} 条记录`)
      } catch (error) {
        if (error.errCode === -502005) {
          missingCollections.push(collectionName)
          console.log(`❌ ${collectionName}: 不存在`)
        } else {
          console.log(`⚠️ ${collectionName}: 检查失败 - ${error.message}`)
        }
      }
    }

    results.push(`现有集合: ${existingCollections.length} 个`)
    results.push(`缺失集合: ${missingCollections.length} 个`)
    
    // 创建缺失的集合（改进版）
    for (const collectionName of missingCollections) {
      try {
        console.log(`🔄 开始创建集合: ${collectionName}`)

        // 使用更稳定的方式创建集合
        const initData = this.getInitialDataForCollection(collectionName)

        const addResult = await db.collection(collectionName).add({
          data: initData
        })

        console.log(`✅ 创建 ${collectionName} 集合成功，文档ID: ${addResult._id}`)
        results.push(`${collectionName} 集合创建成功`)

        // 对于某些集合，保留初始数据；对于其他集合，删除临时数据
        if (this.shouldKeepInitialData(collectionName)) {
          console.log(`📝 保留 ${collectionName} 集合的初始数据`)
        } else {
          // 删除临时文档
          try {
            await db.collection(collectionName).doc(addResult._id).remove()
            console.log(`🗑️ 删除 ${collectionName} 集合的临时文档`)
          } catch (removeError) {
            console.warn(`⚠️ 删除 ${collectionName} 临时文档失败:`, removeError.message)
          }
        }

      } catch (error) {
        console.error(`❌ 创建 ${collectionName} 集合失败:`, error)
        results.push(`${collectionName} 集合创建失败: ${error.message}`)

        // 尝试备用创建方法
        try {
          console.log(`🔄 尝试备用方法创建 ${collectionName}`)
          await this.createCollectionAlternative(db, collectionName)
          console.log(`✅ 备用方法创建 ${collectionName} 成功`)
          results.push(`${collectionName} 集合创建成功（备用方法）`)
        } catch (altError) {
          console.error(`❌ 备用方法也失败:`, altError)
          results.push(`${collectionName} 集合创建完全失败`)
        }
      }
    }

    
    // 8. 创建默认管理员账号
    try {
      const adminCount = await db.collection('admins').count()
      if (adminCount.total === 0) {
        await db.collection('admins').add({
          data: {
            username: 'admin',
            password: 'admin123', // 生产环境应该加密
            email: '<EMAIL>',
            role: 'super_admin',
            permissions: ['*'],
            status: 'active',
            profile: {
              name: '超级管理员',
              avatar: '',
              phone: '',
              department: '系统管理部'
            },
            createdAt: db.serverDate(),
            updatedAt: db.serverDate()
          }
        })
        console.log('✅ 创建默认管理员账号成功')
        results.push('默认管理员账号创建成功 (用户名: admin, 密码: admin123)')
      } else {
        console.log('ℹ️ 管理员账号已存在')
        results.push('管理员账号已存在')
      }
    } catch (error) {
      console.error('创建默认管理员失败:', error)
      results.push('创建默认管理员失败: ' + error.message)
    }
    
    // 9. 创建默认AI模型配置
    try {
      const modelCount = await db.collection('ai_configs').count()
      if (modelCount.total === 0) {
        await db.collection('ai_configs').add({
          data: {
            name: '豆包AI（默认）',
            provider: 'doubao',
            model: 'ep-20241201193454-8xqzx',
            config: {
              apiKey: '', // 需要在管理后台配置
              baseURL: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
              maxTokens: 2000,
              temperature: 0.7
            },
            status: 'inactive', // 默认为非激活状态，需要配置API密钥后激活
            inputPrice: 0.001, // 每1000tokens价格
            outputPrice: 0.002,
            usage: 0,
            totalCost: 0,
            createTime: db.serverDate(),
            updateTime: db.serverDate(),
            createTimestamp: Date.now(),
            updateTimestamp: Date.now(),
            isDefault: true,
            description: '豆包AI模型，需要在管理后台配置API密钥后激活使用'
          }
        })
        console.log('✅ 创建默认AI模型配置成功')
        results.push('默认AI模型配置创建成功（需要在管理后台配置API密钥）')
      } else {
        console.log('ℹ️ AI模型配置已存在')
        results.push('AI模型配置已存在')
      }
    } catch (error) {
      console.error('创建默认AI模型配置失败:', error)
      results.push('创建默认AI模型配置失败: ' + error.message)
    }
    
    console.log('数据库检查完成!')

    return {
      code: 200,
      message: '数据库检查成功',
      data: {
        existingCollections: existingCollections,
        missingCollections: missingCollections,
        results: results,
        timestamp: new Date().toISOString(),
        environment: cloud.DYNAMIC_CURRENT_ENV
      }
    }
    
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return {
      code: 500,
      message: '数据库初始化失败',
      error: error.message
    }
  }
}

// 辅助方法：获取集合的初始数据
exports.getInitialDataForCollection = function(collectionName) {
  const now = new Date()
  const baseData = {
    _created_by: 'init_database',
    _created_at: now.toISOString(),
    _purpose: '初始化集合',
    _temp: true
  }

  switch (collectionName) {
    case 'behavior_records':
      return {
        ...baseData,
        teacherId: 'system',
        studentId: 'system',
        studentName: '系统初始化',
        behavior: '集合创建',
        score: 0,
        createTime: now,
        _temp: true
      }

    case 'user_profiles':
      return {
        ...baseData,
        openid: 'system',
        userInfo: {
          name: '系统初始化',
          nickName: '系统'
        },
        updateTime: now,
        _temp: true
      }

    case 'user_consent_records':
      return {
        ...baseData,
        openid: 'system',
        consentType: 'init',
        consentStatus: true,
        consentTime: now,
        _temp: true
      }

    default:
      return baseData
  }
}

// 辅助方法：判断是否保留初始数据
exports.shouldKeepInitialData = function(collectionName) {
  // 这些集合保留初始数据作为示例
  const keepDataCollections = []
  return keepDataCollections.includes(collectionName)
}

// 辅助方法：备用创建方法
exports.createCollectionAlternative = async function(db, collectionName) {
  // 尝试使用最简单的数据创建集合
  const simpleData = {
    _init: true,
    _timestamp: Date.now()
  }

  const result = await db.collection(collectionName).add({
    data: simpleData
  })

  // 立即删除
  await db.collection(collectionName).doc(result._id).remove()

  return result
}
