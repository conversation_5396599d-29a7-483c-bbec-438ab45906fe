/**
 * 数据接收服务
 * 处理从小程序接收的实时数据并更新本地状态
 * 增强版：支持数据桥接服务直接获取真实数据
 */

import { RealtimeMessage } from './realtimeService'

export interface UserActivityData {
  userId: string
  wechatName: string
  action: string
  timestamp: number
  details?: any
}

export interface TokensUpdateData {
  userId: string
  tokensUsed: number
  cost: number
  modelUsed: string
  timestamp: number
  requestId: string
}

export interface CommentGeneratedData {
  userId: string
  studentName: string
  commentText: string
  tokensUsed: number
  timestamp: number
  commentId: string
}

export interface SystemStatusData {
  activeUsers: number
  totalTokensToday: number
  totalCostToday: number
  systemLoad: number
  timestamp: number
}

export interface DataStore {
  users: Map<string, any>
  activities: UserActivityData[]
  tokensHistory: TokensUpdateData[]
  comments: CommentGeneratedData[]
  systemStatus: SystemStatusData | null
  statistics: {
    totalUsers: number
    totalTokensUsed: number
    totalCost: number
    totalComments: number
    lastUpdated: number
  }
}

export class DataReceiver {
  private store: DataStore
  private listeners: Map<string, ((data: any) => void)[]> = new Map()
  private maxHistorySize = 1000 // 最大历史记录数量

  constructor() {
    this.store = {
      users: new Map(),
      activities: [],
      tokensHistory: [],
      comments: [],
      systemStatus: null,
      statistics: {
        totalUsers: 0,
        totalTokensUsed: 0,
        totalCost: 0,
        totalComments: 0,
        lastUpdated: 0
      }
    }

    console.log('🚫 DataReceiver: 数据桥接服务已禁用，使用直接API调用')
  }

  /**
   * 禁用的数据桥接服务初始化
   */
  private async initDataBridge(): Promise<void> {
    console.log('🚫 DataReceiver: 数据桥接服务已禁用')
  }


  /**
   * 处理接收到的实时消息
   */
  handleMessage(message: RealtimeMessage): void {
    console.log('处理实时消息:', message)

    switch (message.type) {
      case 'user_activity':
        this.handleUserActivity(message.payload)
        break
      case 'tokens_update':
        this.handleTokensUpdate(message.payload)
        break
      case 'comment_generated':
        this.handleCommentGenerated(message.payload)
        break
      case 'system_status':
        this.handleSystemStatus(message.payload)
        break
      default:
        console.log('未知消息类型:', message.type)
    }

    this.updateStatistics()
    this.notifyListeners('data_updated', this.store)
  }

  /**
   * 处理用户活动数据
   */
  private handleUserActivity(data: UserActivityData): void {
    // 更新用户信息
    if (!this.store.users.has(data.userId)) {
      this.store.users.set(data.userId, {
        id: data.userId,
        wechatName: data.wechatName,
        lastActivity: data.timestamp,
        activityCount: 0,
        totalTokens: 0,
        totalCost: 0
      })
    }

    const user = this.store.users.get(data.userId)!
    user.lastActivity = data.timestamp
    user.activityCount++

    // 添加活动记录
    this.store.activities.unshift(data)
    
    // 限制活动记录数量
    if (this.store.activities.length > this.maxHistorySize) {
      this.store.activities = this.store.activities.slice(0, this.maxHistorySize)
    }

    this.notifyListeners('user_activity', data)
  }

  /**
   * 处理Tokens更新数据
   */
  private handleTokensUpdate(data: TokensUpdateData): void {
    // 更新用户Tokens使用情况
    if (this.store.users.has(data.userId)) {
      const user = this.store.users.get(data.userId)!
      user.totalTokens += data.tokensUsed
      user.totalCost += data.cost
    }

    // 添加Tokens历史记录
    this.store.tokensHistory.unshift(data)
    
    // 限制历史记录数量
    if (this.store.tokensHistory.length > this.maxHistorySize) {
      this.store.tokensHistory = this.store.tokensHistory.slice(0, this.maxHistorySize)
    }

    this.notifyListeners('tokens_update', data)
  }

  /**
   * 处理评语生成数据
   */
  private handleCommentGenerated(data: CommentGeneratedData): void {
    // 添加评语记录
    this.store.comments.unshift(data)
    
    // 限制评语记录数量
    if (this.store.comments.length > this.maxHistorySize) {
      this.store.comments = this.store.comments.slice(0, this.maxHistorySize)
    }

    // 更新用户评语统计
    if (this.store.users.has(data.userId)) {
      const user = this.store.users.get(data.userId)!
      user.commentCount = (user.commentCount || 0) + 1
    }

    this.notifyListeners('comment_generated', data)
  }

  /**
   * 处理系统状态数据
   */
  private handleSystemStatus(data: SystemStatusData): void {
    this.store.systemStatus = data
    this.notifyListeners('system_status', data)
  }

  /**
   * 更新统计数据
   */
  private updateStatistics(): void {
    const stats = this.store.statistics
    
    stats.totalUsers = this.store.users.size
    stats.totalTokensUsed = Array.from(this.store.users.values())
      .reduce((sum, user) => sum + (user.totalTokens || 0), 0)
    stats.totalCost = Array.from(this.store.users.values())
      .reduce((sum, user) => sum + (user.totalCost || 0), 0)
    stats.totalComments = this.store.comments.length
    stats.lastUpdated = Date.now()
  }

  /**
   * 获取用户列表
   */
  getUsers(): any[] {
    return Array.from(this.store.users.values())
      .sort((a, b) => b.lastActivity - a.lastActivity)
  }

  /**
   * 获取最近活动
   */
  getRecentActivities(limit: number = 10): UserActivityData[] {
    return this.store.activities.slice(0, limit)
  }

  /**
   * 获取Tokens使用历史
   */
  getTokensHistory(limit: number = 100): TokensUpdateData[] {
    return this.store.tokensHistory.slice(0, limit)
  }

  /**
   * 获取最近评语
   */
  getRecentComments(limit: number = 10): CommentGeneratedData[] {
    return this.store.comments.slice(0, limit)
  }

  /**
   * 获取系统状态
   */
  getSystemStatus(): SystemStatusData | null {
    return this.store.systemStatus
  }

  /**
   * 获取统计数据
   */
  getStatistics(): typeof this.store.statistics {
    return { ...this.store.statistics }
  }

  /**
   * 获取用户详情
   */
  getUserDetail(userId: string): any | null {
    return this.store.users.get(userId) || null
  }

  /**
   * 获取用户的Tokens使用历史
   */
  getUserTokensHistory(userId: string, limit: number = 50): TokensUpdateData[] {
    return this.store.tokensHistory
      .filter(record => record.userId === userId)
      .slice(0, limit)
  }

  /**
   * 获取用户的评语历史
   */
  getUserComments(userId: string, limit: number = 20): CommentGeneratedData[] {
    return this.store.comments
      .filter(comment => comment.userId === userId)
      .slice(0, limit)
  }

  /**
   * 监听数据变化
   */
  on(event: string, callback: (data: any) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  /**
   * 移除监听器
   */
  off(event: string, callback: (data: any) => void): void {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 通知监听器
   */
  private notifyListeners(event: string, data: any): void {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('监听器回调错误:', error)
        }
      })
    }
  }

  /**
   * 清空历史数据
   */
  clearHistory(): void {
    this.store.activities = []
    this.store.tokensHistory = []
    this.store.comments = []
    this.updateStatistics()
    this.notifyListeners('data_cleared', null)
  }

  /**
   * 完全重置所有数据（包括用户数据）
   */
  resetAllData(): void {
    this.store.users.clear()
    this.store.activities = []
    this.store.tokensHistory = []
    this.store.comments = []
    this.store.systemStatus = null
    this.store.statistics = {
      totalUsers: 0,
      totalTokensUsed: 0,
      totalCost: 0,
      totalComments: 0,
      lastUpdated: 0
    }
    this.notifyListeners('data_reset', null)
    console.log('🧹 DataReceiver: 所有数据已重置')
  }

  /**
   * 导出数据
   */
  exportData(): DataStore {
    return {
      users: new Map(this.store.users),
      activities: [...this.store.activities],
      tokensHistory: [...this.store.tokensHistory],
      comments: [...this.store.comments],
      systemStatus: this.store.systemStatus ? { ...this.store.systemStatus } : null,
      statistics: { ...this.store.statistics }
    }
  }
}

// 单例实例
let dataReceiverInstance: DataReceiver | null = null

/**
 * 获取数据接收器实例
 */
export const getDataReceiver = (): DataReceiver => {
  if (!dataReceiverInstance) {
    dataReceiverInstance = new DataReceiver()
  }
  return dataReceiverInstance
}
