/**
 * 简单的状态管理
 * 暂时提供基础功能，避免编译错误
 */

// 全局状态
let globalState = {
  userInfo: null,
  currentClass: null,
  classList: []
};

/**
 * 状态管理工具
 */
const storeUtils = {
  /**
   * 设置用户信息
   */
  setUserInfo(userInfo) {
    globalState.userInfo = userInfo;
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return globalState.userInfo;
  },

  /**
   * 设置当前班级
   */
  setCurrentClass(classInfo) {
    globalState.currentClass = classInfo;
  },

  /**
   * 获取当前班级
   */
  getCurrentClass() {
    return globalState.currentClass;
  },

  /**
   * 设置班级列表
   */
  setClassList(classList) {
    globalState.classList = classList;
  },

  /**
   * 获取班级列表
   */
  getClassList() {
    return globalState.classList;
  },

  /**
   * 清除所有状态
   */
  clear() {
    globalState = {
      userInfo: null,
      currentClass: null,
      classList: []
    };
  }
};

module.exports = {
  storeUtils
};
