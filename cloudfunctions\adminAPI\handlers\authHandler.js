/**
 * 认证模块处理器
 * 处理管理员登录、权限验证等认证相关操作
 */

const crypto = require('crypto')

// 密码加密
const hashPassword = (password) => {
  return crypto.createHash('sha256').update(password).digest('hex')
}

// 生成Token
const generateToken = (adminId) => {
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substr(2, 9)
  return `admin_${adminId}_${timestamp}_${randomStr}`
}

// 验证Token
const validateToken = async (token, db) => {
  if (!token || !token.startsWith('admin_')) {
    return null
  }
  
  // 从token中提取管理员ID
  const parts = token.split('_')
  if (parts.length < 4) return null
  
  const adminId = parts[1]
  const timestamp = parseInt(parts[2])
  
  // 检查token是否过期（7天）
  const now = Date.now()
  const sevenDays = 7 * 24 * 60 * 60 * 1000
  if (now - timestamp > sevenDays) {
    return null
  }
  
  // 查找管理员
  const adminResult = await db.collection('admins').doc(adminId).get()
  if (adminResult.data.length === 0) {
    return null
  }
  
  return adminResult.data[0]
}

module.exports = {
  /**
   * 管理员登录
   */
  async login(data, db, cloud) {
    const { username, password } = data
    
    if (!username || !password) {
      throw new Error('用户名和密码不能为空')
    }
    
    // 查找管理员
    const adminResult = await db.collection('admins').where({
      username: username,
      status: 'active'
    }).get()
    
    if (adminResult.data.length === 0) {
      throw new Error('用户名或密码错误')
    }
    
    const admin = adminResult.data[0]
    
    // 验证密码
    const hashedPassword = hashPassword(password)
    if (admin.password !== hashedPassword) {
      throw new Error('用户名或密码错误')
    }
    
    // 生成Token
    const token = generateToken(admin._id)
    
    // 更新最后登录时间
    await db.collection('admins').doc(admin._id).update({
      data: {
        lastLoginTime: db.serverDate(),
        lastLoginTimestamp: Date.now()
      }
    })
    
    // 返回登录结果
    return {
      token,
      user: {
        id: admin._id,
        username: admin.username,
        email: admin.email,
        role: admin.role,
        permissions: admin.permissions,
        status: admin.status,
        profile: admin.profile,
        createdAt: admin.createTime,
        updatedAt: admin.updateTime
      }
    }
  },

  /**
   * 获取用户信息
   */
  async getUserInfo(data, db, cloud, admin) {
    // admin参数由权限验证中间件提供
    return {
      id: admin._id,
      username: admin.username,
      email: admin.email,
      role: admin.role,
      permissions: admin.permissions,
      status: admin.status,
      profile: admin.profile,
      createdAt: admin.createTime,
      updatedAt: admin.updateTime
    }
  },

  /**
   * 刷新Token
   */
  async refreshToken(data, db, cloud, admin) {
    const newToken = generateToken(admin._id)
    
    // 更新活跃时间
    await db.collection('admins').doc(admin._id).update({
      data: {
        lastActiveTime: db.serverDate(),
        lastActiveTimestamp: Date.now()
      }
    })
    
    return {
      token: newToken,
      user: {
        id: admin._id,
        username: admin.username,
        email: admin.email,
        role: admin.role,
        permissions: admin.permissions,
        status: admin.status,
        profile: admin.profile
      }
    }
  },

  /**
   * 检查是否已有管理员
   */
  async checkAdminExists(data, db, cloud) {
    const adminCount = await db.collection('admins').count()
    return {
      hasAdmin: adminCount.total > 0,
      count: adminCount.total,
      exists: adminCount.total > 0  // 保持向后兼容
    }
  },

  /**
   * 初始化管理员账户
   */
  async initAdmin(data, db, cloud) {
    const { username, password, email, profile } = data
    
    if (!username || !password) {
      throw new Error('用户名和密码不能为空')
    }
    
    // 检查是否已有管理员
    const adminCount = await db.collection('admins').count()
    if (adminCount.total > 0) {
      throw new Error('系统已有管理员，无法重复初始化')
    }
    
    // 检查用户名是否已存在
    const existingAdmin = await db.collection('admins').where({
      username: username
    }).get()
    
    if (existingAdmin.data.length > 0) {
      throw new Error('用户名已存在')
    }
    
    // 创建管理员
    const hashedPassword = hashPassword(password)
    const now = new Date()
    
    const adminData = {
      username,
      password: hashedPassword,
      email: email || `${username}@shiyutong.com`,
      role: 'super_admin',
      permissions: ['*'], // 超级管理员拥有所有权限
      status: 'active',
      profile: {
        name: profile?.name || '超级管理员',
        avatar: profile?.avatar || '',
        phone: profile?.phone || '',
        department: profile?.department || '系统管理部'
      },
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      createTimestamp: Date.now(),
      updateTimestamp: Date.now(),
      lastLoginTime: null,
      lastActiveTime: null
    }
    
    const result = await db.collection('admins').add({
      data: adminData
    })
    
    return {
      id: result._id,
      username: adminData.username,
      email: adminData.email,
      role: adminData.role,
      status: adminData.status,
      message: '管理员账户初始化成功'
    }
  },

  /**
   * 退出登录
   */
  async logout(data, db, cloud, admin) {
    // 更新最后活跃时间
    await db.collection('admins').doc(admin._id).update({
      data: {
        lastActiveTime: db.serverDate(),
        lastActiveTimestamp: Date.now()
      }
    })
    
    return {
      message: '退出登录成功'
    }
  }
}