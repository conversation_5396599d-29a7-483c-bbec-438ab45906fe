/**
 * 增强版缓存管理器
 * 针对用户增长场景优化的缓存策略
 */

const { CacheManager } = require('./cacheManager.js');

class EnhancedCacheManager extends CacheManager {
  constructor() {
    super();
    
    // 增强配置
    this.config = {
      ...this.config,
      // 增加缓存容量
      maxSize: 500,
      // 延长缓存时间
      defaultTTL: 15 * 60 * 1000, // 15分钟
      // 启用压缩
      enableCompression: true,
      // 智能预加载
      enablePrefetch: true,
      // 分层缓存
      enableTieredCache: true
    };
    
    // 分层缓存配置
    this.tiers = {
      hot: { maxSize: 100, ttl: 30 * 60 * 1000 }, // 热数据：30分钟
      warm: { maxSize: 200, ttl: 15 * 60 * 1000 }, // 温数据：15分钟
      cold: { maxSize: 200, ttl: 5 * 60 * 1000 }   // 冷数据：5分钟
    };
    
    // 访问频率统计
    this.accessStats = new Map();
    
    // 预加载队列
    this.prefetchQueue = [];
    
    // 启动智能缓存管理
    this.startIntelligentCaching();
  }

  /**
   * 智能缓存设置
   */
  smartSet(key, value, options = {}) {
    const tier = this.determineTier(key);
    const ttl = options.ttl || this.tiers[tier].ttl;
    const persistent = options.persistent || tier === 'hot';
    
    // 更新访问统计
    this.updateAccessStats(key);
    
    // 压缩大数据
    const processedValue = this.config.enableCompression && this.shouldCompress(value) 
      ? this.compress(value) 
      : value;
    
    this.set(key, processedValue, ttl, persistent);
    
    // 触发预加载
    if (this.config.enablePrefetch) {
      this.triggerPrefetch(key);
    }
  }

  /**
   * 智能缓存获取
   */
  smartGet(key, options = {}) {
    // 更新访问统计
    this.updateAccessStats(key);
    
    const value = this.get(key, options.checkPersistent);
    
    if (value) {
      // 解压缩
      return this.isCompressed(value) ? this.decompress(value) : value;
    }
    
    // 缓存未命中，触发预加载
    if (this.config.enablePrefetch && options.enablePrefetch !== false) {
      this.addToPrefetchQueue(key, options.prefetchFunction);
    }
    
    return null;
  }

  /**
   * 确定数据层级
   */
  determineTier(key) {
    const stats = this.accessStats.get(key);
    
    if (!stats) {
      return 'cold'; // 新数据默认为冷数据
    }
    
    const { accessCount, lastAccess } = stats;
    const timeSinceLastAccess = Date.now() - lastAccess;
    
    // 热数据：高频访问且最近访问过
    if (accessCount >= 10 && timeSinceLastAccess < 5 * 60 * 1000) {
      return 'hot';
    }
    
    // 温数据：中等频率访问
    if (accessCount >= 3 && timeSinceLastAccess < 30 * 60 * 1000) {
      return 'warm';
    }
    
    // 冷数据：低频访问或很久没访问
    return 'cold';
  }

  /**
   * 更新访问统计
   */
  updateAccessStats(key) {
    const now = Date.now();
    const stats = this.accessStats.get(key) || { accessCount: 0, firstAccess: now, lastAccess: now };
    
    stats.accessCount++;
    stats.lastAccess = now;
    
    this.accessStats.set(key, stats);
  }

  /**
   * 判断是否需要压缩
   */
  shouldCompress(value) {
    if (typeof value !== 'object') return false;
    
    const serialized = JSON.stringify(value);
    return serialized.length > 1024; // 大于1KB的数据进行压缩
  }

  /**
   * 简单压缩（实际项目中可使用更好的压缩算法）
   */
  compress(value) {
    try {
      const serialized = JSON.stringify(value);
      // 这里使用简单的压缩标记，实际可以使用LZ-string等库
      return {
        __compressed: true,
        data: serialized,
        originalSize: serialized.length
      };
    } catch (error) {
      console.warn('压缩失败:', error);
      return value;
    }
  }

  /**
   * 解压缩
   */
  decompress(value) {
    if (!this.isCompressed(value)) return value;
    
    try {
      return JSON.parse(value.data);
    } catch (error) {
      console.warn('解压缩失败:', error);
      return value;
    }
  }

  /**
   * 判断是否为压缩数据
   */
  isCompressed(value) {
    return value && typeof value === 'object' && value.__compressed === true;
  }

  /**
   * 触发预加载
   */
  triggerPrefetch(key) {
    // 基于访问模式预测可能需要的数据
    const relatedKeys = this.predictRelatedKeys(key);
    
    relatedKeys.forEach(relatedKey => {
      if (!this.memoryCache.has(relatedKey)) {
        this.addToPrefetchQueue(relatedKey);
      }
    });
  }

  /**
   * 预测相关键
   */
  predictRelatedKeys(key) {
    const relatedKeys = [];
    
    // 基于键名模式预测
    if (key.startsWith('students_')) {
      relatedKeys.push('statistics', 'classes');
    } else if (key.startsWith('comments_')) {
      relatedKeys.push('students_list', 'templates');
    } else if (key === 'user_info') {
      relatedKeys.push('students_list', 'statistics');
    }
    
    return relatedKeys;
  }

  /**
   * 添加到预加载队列
   */
  addToPrefetchQueue(key, prefetchFunction) {
    if (this.prefetchQueue.find(item => item.key === key)) {
      return; // 已在队列中
    }
    
    this.prefetchQueue.push({
      key,
      prefetchFunction,
      addedAt: Date.now()
    });
  }

  /**
   * 启动智能缓存管理
   */
  startIntelligentCaching() {
    // 定期处理预加载队列
    setInterval(() => {
      this.processPrefetchQueue();
    }, 5000); // 每5秒处理一次
    
    // 定期清理访问统计
    setInterval(() => {
      this.cleanupAccessStats();
    }, 60000); // 每分钟清理一次
    
    // 定期优化缓存分布
    setInterval(() => {
      this.optimizeCacheDistribution();
    }, 300000); // 每5分钟优化一次
  }

  /**
   * 处理预加载队列
   */
  async processPrefetchQueue() {
    if (this.prefetchQueue.length === 0) return;
    
    // 每次最多处理3个预加载任务
    const tasksToProcess = this.prefetchQueue.splice(0, 3);
    
    for (const task of tasksToProcess) {
      try {
        if (task.prefetchFunction && typeof task.prefetchFunction === 'function') {
          const data = await task.prefetchFunction();
          if (data) {
            this.smartSet(task.key, data, { prefetch: true });
          }
        }
      } catch (error) {
        console.warn(`预加载失败 ${task.key}:`, error);
      }
    }
  }

  /**
   * 清理访问统计
   */
  cleanupAccessStats() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    
    for (const [key, stats] of this.accessStats.entries()) {
      if (now - stats.lastAccess > maxAge) {
        this.accessStats.delete(key);
      }
    }
  }

  /**
   * 优化缓存分布
   */
  optimizeCacheDistribution() {
    // 统计各层级缓存使用情况
    const tierStats = { hot: 0, warm: 0, cold: 0 };
    
    for (const key of this.memoryCache.keys()) {
      const tier = this.determineTier(key);
      tierStats[tier]++;
    }
    
    console.log('缓存分布统计:', tierStats);
    
    // 如果热数据过多，调整策略
    if (tierStats.hot > this.tiers.hot.maxSize) {
      this.rebalanceCache();
    }
  }

  /**
   * 重新平衡缓存
   */
  rebalanceCache() {
    const allKeys = Array.from(this.memoryCache.keys());
    
    // 按访问频率排序
    allKeys.sort((a, b) => {
      const statsA = this.accessStats.get(a) || { accessCount: 0 };
      const statsB = this.accessStats.get(b) || { accessCount: 0 };
      return statsB.accessCount - statsA.accessCount;
    });
    
    // 保留高频访问的数据，清理低频数据
    const keysToKeep = allKeys.slice(0, this.config.maxSize * 0.8);
    const keysToRemove = allKeys.slice(this.config.maxSize * 0.8);
    
    keysToRemove.forEach(key => {
      this.memoryCache.delete(key);
    });
    
    console.log(`缓存重平衡完成，保留 ${keysToKeep.length} 项，清理 ${keysToRemove.length} 项`);
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    const stats = {
      totalItems: this.memoryCache.size,
      maxSize: this.config.maxSize,
      hitRate: this.calculateHitRate(),
      tierDistribution: this.getTierDistribution(),
      prefetchQueueSize: this.prefetchQueue.length,
      accessStatsSize: this.accessStats.size
    };
    
    return stats;
  }

  /**
   * 计算缓存命中率
   */
  calculateHitRate() {
    // 简化实现，实际项目中需要更精确的统计
    const totalAccess = Array.from(this.accessStats.values())
      .reduce((sum, stats) => sum + stats.accessCount, 0);
    
    const cacheHits = this.memoryCache.size;
    
    return totalAccess > 0 ? Math.round((cacheHits / totalAccess) * 100) : 0;
  }

  /**
   * 获取层级分布
   */
  getTierDistribution() {
    const distribution = { hot: 0, warm: 0, cold: 0 };
    
    for (const key of this.memoryCache.keys()) {
      const tier = this.determineTier(key);
      distribution[tier]++;
    }
    
    return distribution;
  }
}

// 创建增强版缓存实例
const enhancedCache = new EnhancedCacheManager();

module.exports = {
  EnhancedCacheManager,
  enhancedCache
};
