/**
 * 添加学生云函数
 * 支持添加学生和同步班级信息
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('addStudent云函数调用:', event)
  
  try {
    const { OPENID } = cloud.getWXContext()
    const { action, student, className, studentInfo } = event
    
    if (!OPENID) {
      throw new Error('无法获取用户OpenID')
    }
    
    // 根据action执行不同操作
    if (action === 'syncClass') {
      return await syncClassInfo(className, studentInfo, OPENID)
    } else {
      return await addStudent(student, OPENID)
    }
    
  } catch (error) {
    console.error('addStudent云函数执行失败:', error)
    return {
      success: false,
      error: error.message || '操作失败'
    }
  }
}

/**
 * 添加学生
 */
async function addStudent(studentData, teacherId) {
  try {
    const currentTime = new Date()
    
    // 准备学生数据
    const student = {
      name: studentData.name,
      studentId: studentData.studentId || studentData.studentNumber || '',
      className: studentData.className || '',
      gender: studentData.gender || 'male',
      genderText: studentData.genderText || (studentData.gender === 'female' ? '女' : '男'),
      phone: studentData.phone || '',
      parentName: studentData.parentName || '',
      parentPhone: studentData.parentPhone || '',
      address: studentData.address || '',
      remark: studentData.remark || '',
      teacherId: teacherId,
      status: 'active',
      createTime: currentTime,
      updateTime: currentTime,
      createTimestamp: Date.now()
    }
    
    // 添加学生到数据库
    const result = await db.collection('students').add({
      data: student
    })
    
    console.log('学生添加成功:', result._id)
    
    // 如果有班级信息，同步到classes集合
    if (student.className && student.className.trim()) {
      await syncClassInfo(student.className, {
        name: student.name,
        studentId: student.studentId,
        gender: student.gender
      }, teacherId)
    }
    
    return {
      success: true,
      data: {
        _id: result._id,
        ...student
      },
      message: '学生添加成功'
    }
    
  } catch (error) {
    console.error('添加学生失败:', error)
    throw error
  }
}

/**
 * 同步班级信息
 */
async function syncClassInfo(className, studentInfo, teacherId) {
  try {
    console.log(`同步班级信息: ${className}`)
    
    // 检查班级是否已存在
    const classResult = await db.collection('classes').where({
      className: className,
      teacherId: teacherId
    }).get()
    
    const currentTime = new Date()
    
    if (classResult.data.length === 0) {
      // 班级不存在，创建新班级
      const classData = {
        className: className,
        teacherId: teacherId,
        studentCount: 1,
        students: [studentInfo],
        grade: extractGradeFromClassName(className),
        subject: '',
        description: `${className}班级`,
        status: 'active',
        createTime: currentTime,
        updateTime: currentTime,
        createTimestamp: Date.now()
      }
      
      const result = await db.collection('classes').add({
        data: classData
      })
      
      console.log(`新班级创建成功: ${className}`)
      return { success: true, action: 'created', classId: result._id }
      
    } else {
      // 班级已存在，更新学生信息
      const existingClass = classResult.data[0]
      const students = existingClass.students || []
      
      // 检查学生是否已在班级中
      const studentExists = students.some(s => 
        s.studentId === studentInfo.studentId || s.name === studentInfo.name
      )
      
      if (!studentExists) {
        // 添加新学生到班级
        students.push(studentInfo)
        
        await db.collection('classes').doc(existingClass._id).update({
          data: {
            students: students,
            studentCount: students.length,
            updateTime: currentTime
          }
        })
        
        console.log(`学生已添加到班级: ${className}`)
      } else {
        console.log(`学生已存在于班级: ${className}`)
      }
      
      return { success: true, action: 'updated', classId: existingClass._id }
    }
    
  } catch (error) {
    console.error('同步班级信息失败:', error)
    // 不抛出错误，避免影响学生添加
    return { success: false, error: error.message }
  }
}

/**
 * 从班级名称提取年级信息
 */
function extractGradeFromClassName(className) {
  const gradePatterns = [
    { pattern: /一年级|1年级/, grade: '一年级' },
    { pattern: /二年级|2年级/, grade: '二年级' },
    { pattern: /三年级|3年级/, grade: '三年级' },
    { pattern: /四年级|4年级/, grade: '四年级' },
    { pattern: /五年级|5年级/, grade: '五年级' },
    { pattern: /六年级|6年级/, grade: '六年级' },
    { pattern: /七年级|初一/, grade: '七年级' },
    { pattern: /八年级|初二/, grade: '八年级' },
    { pattern: /九年级|初三/, grade: '九年级' },
    { pattern: /高一/, grade: '高一' },
    { pattern: /高二/, grade: '高二' },
    { pattern: /高三/, grade: '高三' }
  ]
  
  for (const { pattern, grade } of gradePatterns) {
    if (pattern.test(className)) {
      return grade
    }
  }
  
  return '未知年级'
}
