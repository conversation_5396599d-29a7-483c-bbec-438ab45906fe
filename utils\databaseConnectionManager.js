/**
 * 数据库连接管理器
 * 提供连接池管理、健康检查、自动重连等功能
 */

class DatabaseConnectionManager {
  constructor() {
    this.connections = new Map();
    this.healthCheckInterval = null;
    this.reconnectAttempts = new Map();
    this.maxReconnectAttempts = 5;
    this.healthCheckFrequency = 30000; // 30秒
    this.connectionTimeout = 10000; // 10秒连接超时
    
    this.init();
  }

  /**
   * 初始化连接管理器
   */
  init() {
    // 只在微信小程序环境中初始化
    if (typeof wx !== 'undefined') {
      // 启动健康检查
      this.startHealthCheck();

      // 监听应用生命周期
      this.setupAppLifecycleHandlers();
    } else {
      console.log('非微信小程序环境，跳过数据库连接管理器初始化');
    }
  }

  /**
   * 获取数据库连接
   */
  async getConnection(collectionName = 'default') {
    try {
      // 检查是否已有可用连接
      if (this.connections.has(collectionName)) {
        const connection = this.connections.get(collectionName);
        
        // 验证连接是否仍然有效
        if (await this.validateConnection(connection)) {
          return connection;
        } else {
          this.connections.delete(collectionName);
        }
      }

      // 创建新连接
      const connection = await this.createConnection(collectionName);
      this.connections.set(collectionName, connection);
      
      // 重置重连计数
      this.reconnectAttempts.delete(collectionName);
      return connection;

    } catch (error) {
      console.error(`[DatabaseConnectionManager] 获取连接${collectionName}失败:`, error);
      
      // 尝试重连
      await this.handleConnectionFailure(collectionName, error);
      throw error;
    }
  }

  /**
   * 创建数据库连接
   */
  async createConnection(collectionName) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`连接${collectionName}超时`));
      }, this.connectionTimeout);

      try {
        // 检查是否在微信小程序环境中
        if (typeof wx === 'undefined') {
          clearTimeout(timeout);
          reject(new Error('非微信小程序环境'));
          return;
        }

        // 检查云开发是否可用
        if (!wx.cloud || typeof wx.cloud.database !== 'function') {
          clearTimeout(timeout);
          reject(new Error('微信云开发不可用'));
          return;
        }

        const db = wx.cloud.database();
        
        // 创建连接对象
        const connection = {
          db: db,
          collectionName: collectionName,
          createdAt: Date.now(),
          lastUsed: Date.now(),
          isHealthy: true,
          
          // 连接方法
          collection: (name) => db.collection(name || collectionName),
          
          // 健康检查方法
          ping: async () => {
            try {
              await db.collection('users').limit(1).get();
              connection.lastUsed = Date.now();
              connection.isHealthy = true;
              return true;
            } catch (error) {
              connection.isHealthy = false;
              throw error;
            }
          }
        };

        // 执行初始连接测试
        connection.ping()
          .then(() => {
            clearTimeout(timeout);
            resolve(connection);
          })
          .catch((error) => {
            clearTimeout(timeout);
            reject(new Error(`连接测试失败: ${error.message}`));
          });

      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  /**
   * 验证连接有效性
   */
  async validateConnection(connection) {
    try {
      if (!connection || !connection.isHealthy) {
        return false;
      }

      // 检查连接年龄（超过1小时的连接需要重新验证）
      const connectionAge = Date.now() - connection.createdAt;
      if (connectionAge > 60 * 60 * 1000) {
        await connection.ping();
      }

      return connection.isHealthy;
    } catch (error) {
      return false;
    }
  }

  /**
   * 处理连接失败
   */
  async handleConnectionFailure(collectionName, error) {
    const attempts = this.reconnectAttempts.get(collectionName) || 0;
    
    if (attempts < this.maxReconnectAttempts) {
      
      this.reconnectAttempts.set(collectionName, attempts + 1);
      
      // 递增延迟重连
      const delay = Math.min(1000 * Math.pow(2, attempts), 30000);
      await this.delay(delay);
      
      try {
        const connection = await this.createConnection(collectionName);
        this.connections.set(collectionName, connection);
        this.reconnectAttempts.delete(collectionName);
        return connection;
      } catch (reconnectError) {
        console.error(`[DatabaseConnectionManager] 重连${collectionName}失败:`, reconnectError);
        throw reconnectError;
      }
    } else {
      console.error(`[DatabaseConnectionManager] 连接${collectionName}重连次数已达上限`);
      this.reconnectAttempts.delete(collectionName);
      throw new Error(`连接${collectionName}重连失败，已达最大重试次数`);
    }
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    // 清除现有定时器
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      await this.checkAllConnections();
    }, this.healthCheckFrequency);
  }

  /**
   * 检查所有连接的健康状态
   */
  async checkAllConnections() {
    const connectionNames = Array.from(this.connections.keys());
    
    if (connectionNames.length === 0) {
      return;
    }
    for (const name of connectionNames) {
      try {
        const connection = this.connections.get(name);
        await connection.ping();
      } catch (error) {
        // 标记连接为不健康
        const connection = this.connections.get(name);
        if (connection) {
          connection.isHealthy = false;
        }
        
        // 尝试重新建立连接
        try {
          await this.handleConnectionFailure(name, error);
        } catch (reconnectError) {
          console.error(`[DatabaseConnectionManager] 连接${name}重连失败:`, reconnectError);
          // 移除失败的连接
          this.connections.delete(name);
        }
      }
    }
  }

  /**
   * 设置应用生命周期处理
   */
  setupAppLifecycleHandlers() {
    // 检查是否在微信小程序环境中
    if (typeof wx === 'undefined') {
      console.log('非微信环境，跳过应用生命周期监听');
      return;
    }

    // 应用隐藏时暂停健康检查
    wx.onAppHide(() => {
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }
    });

    // 应用显示时恢复健康检查
    wx.onAppShow(() => {
      this.startHealthCheck();
      
      // 立即执行一次健康检查
      setTimeout(() => {
        this.checkAllConnections();
      }, 1000);
    });
  }

  /**
   * 获取连接统计信息
   */
  getConnectionStats() {
    const stats = {
      totalConnections: this.connections.size,
      healthyConnections: 0,
      unhealthyConnections: 0,
      connections: []
    };

    for (const [name, connection] of this.connections) {
      const connectionInfo = {
        name: name,
        isHealthy: connection.isHealthy,
        createdAt: new Date(connection.createdAt).toLocaleString(),
        lastUsed: new Date(connection.lastUsed).toLocaleString(),
        age: Date.now() - connection.createdAt
      };

      stats.connections.push(connectionInfo);

      if (connection.isHealthy) {
        stats.healthyConnections++;
      } else {
        stats.unhealthyConnections++;
      }
    }

    return stats;
  }

  /**
   * 关闭所有连接
   */
  closeAllConnections() {
    // 停止健康检查
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // 清除所有连接
    this.connections.clear();
    this.reconnectAttempts.clear();
  }

  /**
   * 强制重连所有连接
   */
  async reconnectAll() {
    const connectionNames = Array.from(this.connections.keys());
    
    // 清除现有连接
    this.connections.clear();
    this.reconnectAttempts.clear();
    
    // 重新建立连接
    const reconnectPromises = connectionNames.map(async (name) => {
      try {
        await this.getConnection(name);
      } catch (error) {
        console.error(`[DatabaseConnectionManager] 重连${name}失败:`, error);
      }
    });

    await Promise.all(reconnectPromises);
  }

  /**
   * 延迟工具函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取特定集合的连接
   */
  async getCollectionConnection(collectionName) {
    const connection = await this.getConnection(collectionName);
    return connection.collection(collectionName);
  }

  /**
   * 执行数据库操作（带重试机制）
   */
  async executeWithRetry(collectionName, operation, maxRetries = 3) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const connection = await this.getConnection(collectionName);
        const result = await operation(connection);
        
        // 更新连接使用时间
        connection.lastUsed = Date.now();
        
        return result;
      } catch (error) {
        lastError = error;
        
        if (attempt < maxRetries) {
          // 连接可能有问题，移除并重试
          this.connections.delete(collectionName);
          await this.delay(1000 * attempt);
        }
      }
    }
    
    throw lastError;
  }
}

// 创建全局实例
const databaseConnectionManager = new DatabaseConnectionManager();

module.exports = {
  databaseConnectionManager,
  DatabaseConnectionManager
};