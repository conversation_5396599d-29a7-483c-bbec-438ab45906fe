/**
 * 数据库修复工具
 * 用于手动修复数据库集合问题
 */

const databaseRepairTool = {
  /**
   * 执行完整的数据库修复
   */
  async repairDatabase() {
    console.log('🔧 开始数据库修复...');
    
    try {
      // 显示修复进度
      wx.showLoading({
        title: '正在修复数据库...',
        mask: true
      });

      const results = {
        checked: [],
        created: [],
        failed: [],
        errors: []
      };

      // 必需的集合列表
      const requiredCollections = [
        'users',
        'students', 
        'records',
        'comments',
        'behavior_records',
        'user_profiles',
        'user_consent_records',
        'ai_configs',
        'settings'
      ];

      const db = wx.cloud.database();

      // 检查每个集合
      for (const collectionName of requiredCollections) {
        try {
          console.log(`🔍 检查集合: ${collectionName}`);
          
          // 尝试访问集合
          await db.collection(collectionName).limit(1).get();
          
          results.checked.push(collectionName);
          console.log(`✅ 集合 ${collectionName} 存在`);
          
        } catch (error) {
          if (error.errCode === -502005) {
            // 集合不存在，尝试创建
            console.log(`❌ 集合 ${collectionName} 不存在，尝试创建...`);
            
            try {
              await this.createCollection(db, collectionName);
              results.created.push(collectionName);
              console.log(`✅ 集合 ${collectionName} 创建成功`);
            } catch (createError) {
              console.error(`❌ 集合 ${collectionName} 创建失败:`, createError);
              results.failed.push(collectionName);
              results.errors.push({
                collection: collectionName,
                error: createError.message
              });
            }
          } else {
            // 其他错误
            console.error(`⚠️ 集合 ${collectionName} 检查异常:`, error);
            results.errors.push({
              collection: collectionName,
              error: error.message
            });
          }
        }
      }

      wx.hideLoading();

      // 显示修复结果
      this.showRepairResults(results);

      return results;

    } catch (error) {
      wx.hideLoading();
      console.error('🚨 数据库修复失败:', error);
      
      wx.showModal({
        title: '修复失败',
        content: `数据库修复过程中发生错误: ${error.message}`,
        showCancel: false,
        confirmText: '确定'
      });

      return null;
    }
  },

  /**
   * 创建单个集合
   */
  async createCollection(db, collectionName) {
    const initData = this.getInitialData(collectionName);
    
    // 添加临时文档来创建集合
    const addResult = await db.collection(collectionName).add({
      data: initData
    });

    // 立即删除临时文档（除非是需要保留的）
    if (!this.shouldKeepInitialData(collectionName)) {
      await db.collection(collectionName).doc(addResult._id).remove();
    }

    return addResult;
  },

  /**
   * 获取集合的初始数据
   */
  getInitialData(collectionName) {
    const baseData = {
      _temp: true,
      _created_by: 'repair_tool',
      _created_at: new Date().toISOString(),
      _purpose: '修复工具创建'
    };

    switch (collectionName) {
      case 'behavior_records':
        return {
          ...baseData,
          teacherId: 'repair_tool',
          studentId: 'repair_tool', 
          studentName: '修复工具测试',
          behavior: '集合创建',
          score: 0,
          createTime: new Date()
        };

      case 'user_profiles':
        return {
          ...baseData,
          openid: 'repair_tool',
          userInfo: {
            name: '修复工具',
            nickName: '修复'
          },
          updateTime: new Date()
        };

      case 'user_consent_records':
        return {
          ...baseData,
          openid: 'repair_tool',
          consentType: 'repair',
          consentStatus: true,
          consentTime: new Date()
        };

      case 'students':
        return {
          ...baseData,
          teacherId: 'repair_tool',
          name: '修复工具测试学生',
          class: '测试班级',
          studentId: 'repair_test_001'
        };

      case 'ai_configs':
        return {
          ...baseData,
          name: '修复工具配置',
          provider: 'test',
          status: 'inactive'
        };

      default:
        return baseData;
    }
  },

  /**
   * 判断是否保留初始数据
   */
  shouldKeepInitialData(collectionName) {
    // 暂时不保留任何初始数据
    return false;
  },

  /**
   * 显示修复结果
   */
  showRepairResults(results) {
    const { checked, created, failed, errors } = results;
    
    let message = '';
    let title = '数据库修复完成';
    
    if (created.length > 0) {
      message += `✅ 成功创建 ${created.length} 个集合:\n${created.join(', ')}\n\n`;
    }
    
    if (checked.length > 0) {
      message += `📋 检查通过 ${checked.length} 个集合\n\n`;
    }
    
    if (failed.length > 0) {
      message += `❌ 创建失败 ${failed.length} 个集合:\n${failed.join(', ')}\n\n`;
      title = '数据库修复部分成功';
    }
    
    if (errors.length > 0) {
      message += `⚠️ 发生 ${errors.length} 个错误`;
      title = '数据库修复有问题';
    }

    if (!message) {
      message = '所有数据库集合都正常，无需修复';
    }

    wx.showModal({
      title,
      content: message,
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 快速检查关键集合
   */
  async quickCheck() {
    try {
      wx.showLoading({
        title: '快速检查中...',
        mask: true
      });

      const db = wx.cloud.database();
      const keyCollections = ['behavior_records', 'students', 'user_profiles'];
      const results = [];

      for (const collection of keyCollections) {
        try {
          await db.collection(collection).limit(1).get();
          results.push(`✅ ${collection}: 正常`);
        } catch (error) {
          if (error.errCode === -502005) {
            results.push(`❌ ${collection}: 不存在`);
          } else {
            results.push(`⚠️ ${collection}: 异常`);
          }
        }
      }

      wx.hideLoading();

      wx.showModal({
        title: '快速检查结果',
        content: results.join('\n'),
        confirmText: '完整修复',
        cancelText: '关闭',
        success: (res) => {
          if (res.confirm) {
            this.repairDatabase();
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('快速检查失败:', error);
      wx.showToast({
        title: '检查失败',
        icon: 'none'
      });
    }
  },

  /**
   * 测试数据库连接
   */
  async testConnection() {
    try {
      wx.showLoading({
        title: '测试连接中...',
        mask: true
      });

      const db = wx.cloud.database();
      
      // 尝试获取数据库信息
      await db.collection('users').limit(1).get();
      
      wx.hideLoading();
      wx.showToast({
        title: '数据库连接正常',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      console.error('数据库连接测试失败:', error);
      
      let message = '数据库连接失败';
      if (error.errCode === -502005) {
        message = 'users集合不存在，需要修复数据库';
      } else {
        message = `连接错误: ${error.message}`;
      }

      wx.showModal({
        title: '连接测试失败',
        content: message,
        confirmText: '修复数据库',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.repairDatabase();
          }
        }
      });
    }
  }
};

module.exports = {
  databaseRepairTool
};
