<!--成长报告页面-->
<view class="growth-container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" color="#5470C6" size="24px" vertical>
    加载中...
  </van-loading>

  <!-- 主要内容 -->
  <view wx:else class="content">
    <!-- 头部统计卡片 -->
    <view class="stats-header">
      <view class="header-title">
        <text class="title">我的使用报告</text>
        <text class="subtitle">{{monthlyReport.currentMonth}}</text>
      </view>

      <!-- 右上角分享按钮 -->
      <view class="share-button" bindtap="shareGrowthReport">
        <van-icon name="share" size="20px" color="#5470C6" />
        <text class="share-text">分享报告</text>
      </view>
      
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{efficiencyStats.timeSaved}}</view>
          <view class="stat-label">节省小时</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{efficiencyStats.commentsGenerated}}</view>
          <view class="stat-label">生成评语</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{efficiencyStats.qualityScore}}</view>
          <view class="stat-label">质量评分</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{efficiencyStats.efficiencyImprovement}}%</view>
          <view class="stat-label">效率提升</view>
        </view>
      </view>
    </view>

    <!-- 效率趋势图 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title">
          <van-icon name="chart-trending-o" size="16px" color="#5470C6" />
          <text>本周效率趋势</text>
        </view>
      </view>
      
      <view class="trend-chart">
        <view class="chart-container">
          <view wx:for="{{timeTrendData}}" wx:key="day" class="trend-item">
            <view class="trend-bar" style="height: {{item.comments * 3}}px;"></view>
            <view class="trend-label">{{item.day}}</view>
            <view class="trend-value">{{item.comments}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 质量分析 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title">
          <van-icon name="star-o" size="16px" color="#5470C6" />
          <text>质量分析</text>
        </view>
        <view class="quality-trend {{qualityAnalysis.trend}}">
          <van-icon name="{{qualityAnalysis.trend === 'up' ? 'arrow-up' : 'arrow-down'}}" size="12px" />
          <text>{{qualityAnalysis.improvement}}%</text>
        </view>
      </view>
      
      <view class="quality-content">
        <view class="quality-score">
          <view class="score-circle">
            <view class="score-number">{{qualityAnalysis.averageScore}}</view>
            <view class="score-total">/10</view>
          </view>
          <view class="score-desc">平均质量分</view>
        </view>
        
        <view class="suggestions">
          <view class="suggestions-title">改进建议</view>
          <view wx:for="{{qualityAnalysis.suggestions}}" wx:key="index" class="suggestion-item">
            <van-icon name="bulb-o" size="12px" color="#FFA940" />
            <text>{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 专业能力雷达图 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title">
          <van-icon name="medal-o" size="16px" color="#5470C6" />
          <text>专业能力分析</text>
        </view>
      </view>
      
      <view class="ability-radar">
        <view class="radar-item">
          <view class="ability-name">客观性</view>
          <view class="ability-bar">
            <view class="ability-progress" style="width: {{abilityRadar.objectivity}}%;"></view>
          </view>
          <view class="ability-score">{{abilityRadar.objectivity}}</view>
        </view>
        
        <view class="radar-item">
          <view class="ability-name">个性化</view>
          <view class="ability-bar">
            <view class="ability-progress" style="width: {{abilityRadar.personalization}}%;"></view>
          </view>
          <view class="ability-score">{{abilityRadar.personalization}}</view>
        </view>
        
        <view class="radar-item">
          <view class="ability-name">专业度</view>
          <view class="ability-bar">
            <view class="ability-progress" style="width: {{abilityRadar.professionalism}}%;"></view>
          </view>
          <view class="ability-score">{{abilityRadar.professionalism}}</view>
        </view>
        
        <view class="radar-item">
          <view class="ability-name">效率</view>
          <view class="ability-bar">
            <view class="ability-progress" style="width: {{abilityRadar.efficiency}}%;"></view>
          </view>
          <view class="ability-score">{{abilityRadar.efficiency}}</view>
        </view>
        
        <view class="radar-item">
          <view class="ability-name">创新性</view>
          <view class="ability-bar">
            <view class="ability-progress" style="width: {{abilityRadar.innovation}}%;"></view>
          </view>
          <view class="ability-score">{{abilityRadar.innovation}}</view>
        </view>
      </view>
    </view>

    <!-- 成就展示 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title">
          <van-icon name="trophy-o" size="16px" color="#5470C6" />
          <text>我的成就</text>
        </view>
      </view>
      
      <view class="achievements-grid">
        <view wx:for="{{achievements}}" wx:key="id" class="achievement-item {{(item.unlocked || item.isUnlocked) ? 'unlocked' : 'locked'}}">
          <view class="achievement-icon">{{item.icon}}</view>
          <view class="achievement-info">
            <view class="achievement-name">{{item.name}}</view>
            <view class="achievement-desc">{{item.description}}</view>
            <view wx:if="{{!(item.unlocked || item.isUnlocked) && item.progress !== undefined}}" class="achievement-progress">
              <van-progress percentage="{{item.progress}}" stroke-width="4" color="#5470C6" />
              <text class="progress-text">{{item.current || 0}}/{{item.target || 0}} ({{item.progress}}%)</text>
            </view>
            <view wx:if="{{item.unlocked || item.isUnlocked}}" class="achievement-date">{{item.unlockedAt}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 月度亮点 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title">
          <van-icon name="fire-o" size="16px" color="#5470C6" />
          <text>本月亮点</text>
        </view>
      </view>
      
      <view class="highlights">
        <view wx:for="{{monthlyReport.highlights}}" wx:key="index" class="highlight-item">
          <van-icon name="passed" size="14px" color="#52C41A" />
          <text>{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <van-button type="default" size="large" bind:click="exportReport" custom-class="export-btn">
        <van-icon name="down" size="16px" />
        导出报告
      </van-button>
    </view>
  </view>

  <!-- 隐藏的Canvas用于生成分享图片 -->
  <canvas canvas-id="growthShareCanvas" style="position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1334px;"></canvas>
</view>
