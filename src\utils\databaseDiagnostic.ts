/**
 * 数据库诊断工具
 * 用于检查数据库结构和数据情况
 */

import { cloudbaseService } from '../services/cloudWebSDK'

export interface DatabaseDiagnosticResult {
  environment: string
  timestamp: string
  collections: {
    [key: string]: {
      exists: boolean
      count: number
      sampleFields: string[]
      hasData: boolean
      error?: string
    }
  }
  summary: {
    totalCollections: number
    existingCollections: number
    collectionsWithData: number
  }
}

class DatabaseDiagnostic {
  /**
   * 诊断数据库结构
   */
  async diagnoseDatabaseStructure(): Promise<DatabaseDiagnosticResult | null> {
    try {
      console.log('🔍 开始诊断数据库结构...')
      
      const result = await cloudbaseService.callFunction('dataQuery', {
        action: 'diagnoseDatabaseStructure'
      })
      
      console.log('🔍 数据库诊断原始结果:', result)
      
      if (result.code === 200 && result.data) {
        return result.data as DatabaseDiagnosticResult
      } else {
        throw new Error(result.message || '诊断失败')
      }
    } catch (error) {
      console.error('❌ 数据库诊断失败:', error)
      return null
    }
  }

  /**
   * 打印诊断报告到控制台
   */
  async printDiagnosticReport(): Promise<void> {
    const result = await this.diagnoseDatabaseStructure()
    
    if (!result) {
      console.error('❌ 无法获取诊断结果')
      return
    }
    
    console.log('\n' + '='.repeat(60))
    console.log('📊 数据库诊断报告')
    console.log('='.repeat(60))
    console.log(`🌍 环境: ${result.environment}`)
    console.log(`⏰ 时间: ${result.timestamp}`)
    console.log(`📁 总集合数: ${result.summary.totalCollections}`)
    console.log(`✅ 存在的集合: ${result.summary.existingCollections}`)
    console.log(`📈 有数据的集合: ${result.summary.collectionsWithData}`)
    console.log('')
    
    // 详细集合信息
    for (const [collectionName, info] of Object.entries(result.collections)) {
      if (info.exists) {
        const status = info.hasData ? '✅ 有数据' : '⚠️  无数据'
        console.log(`📦 ${collectionName}: ${status} (${info.count} 条记录)`)
        if (info.sampleFields.length > 0) {
          console.log(`   字段: ${info.sampleFields.join(', ')}`)
        }
      } else {
        console.log(`❌ ${collectionName}: 不存在 - ${info.error}`)
      }
    }
    
    console.log('\n' + '='.repeat(60))
    
    // 给出建议
    this.provideSuggestions(result)
  }

  /**
   * 根据诊断结果提供建议
   */
  private provideSuggestions(result: DatabaseDiagnosticResult): void {
    console.log('🔧 建议和解决方案:')
    console.log('')
    
    const { collections } = result
    
    // 检查关键集合
    if (!collections.users?.hasData) {
      console.log('⚠️  users 集合无数据 - 可能是小程序还没有用户注册或登录')
    }
    
    if (!collections.students?.hasData) {
      console.log('⚠️  students 集合无数据 - 可能是老师还没有录入学生信息')
    }
    
    if (!collections.comments?.hasData) {
      console.log('⚠️  comments 集合无数据 - 可能是还没有生成过评语')
    }
    
    // 检查字段名是否正确
    if (collections.students?.hasData) {
      const studentFields = collections.students.sampleFields
      if (!studentFields.includes('name') && !studentFields.includes('studentName')) {
        console.log('⚠️  students 集合缺少 name/studentName 字段')
      }
    }
    
    if (collections.users?.hasData) {
      const userFields = collections.users.sampleFields
      if (!userFields.includes('openid')) {
        console.log('⚠️  users 集合缺少 openid 字段')
      }
    }
    
    console.log('')
    console.log('💡 如果数据都是0，建议：')
    console.log('1. 确保小程序端正常录入了学生数据')
    console.log('2. 确保小程序端生成过评语')
    console.log('3. 检查数据库权限设置')
    console.log('4. 确认云环境ID是否正确')
  }
}

// 导出单例
export const databaseDiagnostic = new DatabaseDiagnostic()
export default databaseDiagnostic