/**
 * 🗄️ 数据库集合检查脚本
 * 检查所有必需的数据库集合是否存在
 */

// 在微信开发者工具控制台中运行
(() => {
  console.log('🗄️ 开始检查数据库集合...\n');
  
  // 需要检查的集合列表
  const requiredCollections = [
    'users',                    // 用户信息
    'students',                 // 学生信息  
    'records',                  // 评语记录
    'templates',                // 评语模板
    'achievements',             // 成就系统
    'user_consent_records',     // 用户授权记录 ⚠️
    'consent_audit_logs',       // 授权审计日志 ⚠️
    'consent_error_logs',       // 授权错误日志 ⚠️
    'ai_config',               // AI配置
    'system_settings',         // 系统设置
    'usage_stats',             // 使用统计
    'cost_records'             // 费用记录
  ];

  const checkResults = [];
  let completedChecks = 0;
  
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('📊 数据库集合检查报告');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━');

  // 检查每个集合
  requiredCollections.forEach((collectionName, index) => {
    // 使用云开发数据库API检查集合
    wx.cloud.database().collection(collectionName).limit(1).get({
      success: (res) => {
        checkResults.push({
          name: collectionName,
          exists: true,
          recordCount: res.data.length >= 0 ? `至少${res.data.length}条` : '未知',
          status: '✅ 存在'
        });
        
        console.log(`✅ ${collectionName.padEnd(20)} - 集合存在`);
        
        completedChecks++;
        if (completedChecks === requiredCollections.length) {
          generateReport();
        }
      },
      fail: (error) => {
        const isNotFound = error.errMsg && error.errMsg.includes('collection not found');
        
        checkResults.push({
          name: collectionName,
          exists: false,
          recordCount: '0',
          status: isNotFound ? '❌ 不存在' : '⚠️ 检查失败',
          error: error.errMsg
        });
        
        if (isNotFound) {
          console.log(`❌ ${collectionName.padEnd(20)} - 集合不存在`);
        } else {
          console.log(`⚠️ ${collectionName.padEnd(20)} - 检查失败: ${error.errMsg}`);
        }
        
        completedChecks++;
        if (completedChecks === requiredCollections.length) {
          generateReport();
        }
      }
    });
  });

  // 生成最终报告
  function generateReport() {
    console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('📋 检查结果汇总');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━');

    const existingCollections = checkResults.filter(r => r.exists);
    const missingCollections = checkResults.filter(r => !r.exists);
    
    console.log(`✅ 存在的集合: ${existingCollections.length}/${requiredCollections.length}`);
    console.log(`❌ 缺失的集合: ${missingCollections.length}/${requiredCollections.length}`);
    
    if (missingCollections.length > 0) {
      console.log('\n🚨 需要创建的集合:');
      missingCollections.forEach(collection => {
        console.log(`   • ${collection.name}`);
      });
      
      console.log('\n💡 解决方案:');
      console.log('1. 在云开发控制台手动创建缺失的集合');
      console.log('2. 或者运行初始化脚本自动创建');
      
      // 生成创建集合的代码
      console.log('\n📝 快速创建代码:');
      missingCollections.forEach(collection => {
        console.log(`wx.cloud.database().collection('${collection.name}').add({data: {_init: true}});`);
      });
    }
    
    if (existingCollections.length === requiredCollections.length) {
      console.log('\n🎉 数据库检查通过！所有必需的集合都已存在。');
      console.log('✅ 可以进行最终功能测试了。');
    } else {
      console.log('\n⚠️ 请先创建缺失的数据库集合，然后再进行功能测试。');
    }
    
    console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('🏁 数据库检查完成');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━');
    
    return {
      total: requiredCollections.length,
      existing: existingCollections.length,
      missing: missingCollections.length,
      missingList: missingCollections.map(c => c.name),
      canProceed: missingCollections.length === 0
    };
  }
})();