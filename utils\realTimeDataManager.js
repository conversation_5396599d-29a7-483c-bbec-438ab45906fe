/**
 * 小程序端实时数据管理器
 * 基于云函数调用实现免费的数据实时同步
 * 
 * 核心功能：
 * 1. 与dataSyncBridge云函数通信
 * 2. 本地数据缓存管理
 * 3. 定时同步机制
 * 4. 离线数据处理
 */

class RealTimeDataManager {
  constructor() {
    this.isInitialized = false
    this.syncInterval = null
    this.syncFrequency = 30000 // 30秒同步一次
    this.lastSyncTime = 0
    this.localCache = new Map()
    this.eventListeners = new Map()
    
    console.log('🔄 RealTimeDataManager 初始化')
  }

  /**
   * 初始化实时数据管理器
   */
  async initialize() {
    if (this.isInitialized) {
      return
    }

    try {
      console.log('🚀 初始化实时数据管理器...')
      
      // 恢复本地缓存
      await this.restoreLocalCache()
      
      // 启动定时同步
      this.startAutoSync()
      
      // 监听小程序生命周期
      this.setupLifecycleListeners()
      
      this.isInitialized = true
      console.log('✅ 实时数据管理器初始化完成')
      
    } catch (error) {
      console.error('❌ 实时数据管理器初始化失败:', error)
      throw error
    }
  }

  /**
   * 获取数据 - 优先从缓存获取，必要时从云端同步
   */
  async getData(collection, filters = {}, forceRefresh = false) {
    try {
      const cacheKey = this.getCacheKey(collection, filters)
      
      // 如果不强制刷新且有缓存，先返回缓存数据
      if (!forceRefresh && this.localCache.has(cacheKey)) {
        const cached = this.localCache.get(cacheKey)
        console.log(`📦 从缓存获取数据: ${collection}`)
        
        // 异步更新缓存
        this.refreshDataInBackground(collection, filters)
        
        return {
          success: true,
          data: cached.data,
          fromCache: true,
          timestamp: cached.timestamp
        }
      }
      
      // 从云端获取数据
      return await this.fetchDataFromCloud(collection, filters)
      
    } catch (error) {
      console.error(`❌ 获取数据失败: ${collection}`, error)
      
      // 降级到缓存数据
      const cacheKey = this.getCacheKey(collection, filters)
      if (this.localCache.has(cacheKey)) {
        const cached = this.localCache.get(cacheKey)
        console.log(`🔄 降级使用缓存数据: ${collection}`)
        return {
          success: true,
          data: cached.data,
          fromCache: true,
          fallback: true,
          timestamp: cached.timestamp
        }
      }
      
      throw error
    }
  }

  /**
   * 更新数据
   */
  async updateData(collection, operation, docId, updateData) {
    try {
      console.log(`✏️ 更新数据: ${collection}.${operation}`)
      
      const result = await wx.cloud.callFunction({
        name: 'dataSyncBridge',
        data: {
          action: 'updateData',
          source: 'miniprogram',
          data: {
            collection,
            operation,
            docId,
            updateData
          }
        }
      })

      if (result.result && result.result.success) {
        // 更新本地缓存
        await this.updateLocalCache(collection, operation, docId, updateData)
        
        // 触发数据变更事件
        this.emitDataChange(collection, operation, docId, updateData)
        
        console.log(`✅ 数据更新成功: ${collection}.${operation}`)
        return result.result
      } else {
        throw new Error(result.result?.error || '数据更新失败')
      }
      
    } catch (error) {
      console.error(`❌ 数据更新失败: ${collection}.${operation}`, error)
      
      // 记录离线操作
      await this.recordOfflineOperation(collection, operation, docId, updateData)
      
      throw error
    }
  }

  /**
   * 从云端获取数据
   */
  async fetchDataFromCloud(collection, filters = {}) {
    try {
      console.log(`☁️ 从云端获取数据: ${collection}`)
      
      const result = await wx.cloud.callFunction({
        name: 'dataSyncBridge',
        data: {
          action: 'getData',
          source: 'miniprogram',
          collection,
          filters
        }
      })

      if (result.result && result.result.success) {
        const data = result.result.data
        
        // 更新本地缓存
        const cacheKey = this.getCacheKey(collection, filters)
        this.localCache.set(cacheKey, {
          data: data.items || [],
          timestamp: Date.now(),
          collection,
          filters
        })
        
        // 持久化缓存
        await this.persistCache()
        
        console.log(`✅ 云端数据获取成功: ${collection}, ${data.items?.length || 0} 条记录`)
        
        return {
          success: true,
          data: data.items || [],
          total: data.total || 0,
          hasMore: data.hasMore || false,
          fromCache: false,
          timestamp: Date.now()
        }
      } else {
        throw new Error(result.result?.error || '获取数据失败')
      }
      
    } catch (error) {
      console.error(`❌ 云端数据获取失败: ${collection}`, error)
      throw error
    }
  }

  /**
   * 后台刷新数据
   */
  async refreshDataInBackground(collection, filters = {}) {
    try {
      await this.fetchDataFromCloud(collection, filters)
    } catch (error) {
      console.warn(`⚠️ 后台数据刷新失败: ${collection}`, error)
    }
  }

  /**
   * 启动自动同步
   */
  startAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
    }

    this.syncInterval = setInterval(async () => {
      try {
        await this.checkForUpdates()
      } catch (error) {
        console.warn('⚠️ 自动同步检查失败:', error)
      }
    }, this.syncFrequency)

    console.log(`🔄 自动同步已启动，间隔: ${this.syncFrequency}ms`)
  }

  /**
   * 停止自动同步
   */
  stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
      this.syncInterval = null
      console.log('⏹️ 自动同步已停止')
    }
  }

  /**
   * 检查更新
   */
  async checkForUpdates() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'dataSyncBridge',
        data: {
          action: 'getUpdates',
          source: 'miniprogram',
          data: {
            since: this.lastSyncTime
          }
        }
      })

      if (result.result && result.result.success) {
        const { hasChanges, changes, lastSyncTime } = result.result.data
        
        if (hasChanges && changes.length > 0) {
          console.log(`🔄 发现 ${changes.length} 个数据更新`)
          
          // 处理数据变更
          await this.processDataChanges(changes)
          
          // 更新同步时间
          this.lastSyncTime = lastSyncTime
          wx.setStorageSync('lastSyncTime', this.lastSyncTime)
        }
      }
      
    } catch (error) {
      console.warn('⚠️ 检查更新失败:', error)
    }
  }

  /**
   * 处理数据变更
   */
  async processDataChanges(changes) {
    for (const change of changes) {
      try {
        const { collection, operation, docId } = change
        
        // 清除相关缓存
        this.clearCacheByCollection(collection)
        
        // 触发数据变更事件
        this.emitDataChange(collection, operation, docId, change.data)
        
      } catch (error) {
        console.warn('⚠️ 处理数据变更失败:', error)
      }
    }
  }

  /**
   * 生成缓存键
   */
  getCacheKey(collection, filters) {
    return `${collection}_${JSON.stringify(filters)}`
  }

  /**
   * 清除指定集合的缓存
   */
  clearCacheByCollection(collection) {
    const keysToDelete = []
    for (const [key, value] of this.localCache.entries()) {
      if (value.collection === collection) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => {
      this.localCache.delete(key)
    })
    
    console.log(`🗑️ 清除缓存: ${collection}, ${keysToDelete.length} 个键`)
  }

  /**
   * 恢复本地缓存
   */
  async restoreLocalCache() {
    try {
      const cachedData = wx.getStorageSync('realTimeDataCache')
      const lastSyncTime = wx.getStorageSync('lastSyncTime')
      
      if (cachedData) {
        this.localCache = new Map(JSON.parse(cachedData))
        console.log(`📦 恢复本地缓存: ${this.localCache.size} 个项目`)
      }
      
      if (lastSyncTime) {
        this.lastSyncTime = lastSyncTime
        console.log(`⏰ 恢复同步时间: ${new Date(this.lastSyncTime).toLocaleString()}`)
      }
      
    } catch (error) {
      console.warn('⚠️ 恢复本地缓存失败:', error)
    }
  }

  /**
   * 持久化缓存
   */
  async persistCache() {
    try {
      const cacheData = JSON.stringify([...this.localCache.entries()])
      wx.setStorageSync('realTimeDataCache', cacheData)
    } catch (error) {
      console.warn('⚠️ 持久化缓存失败:', error)
    }
  }

  /**
   * 更新本地缓存
   */
  async updateLocalCache(collection, operation, docId, updateData) {
    // 清除相关缓存，强制下次重新获取
    this.clearCacheByCollection(collection)
    await this.persistCache()
  }

  /**
   * 记录离线操作
   */
  async recordOfflineOperation(collection, operation, docId, updateData) {
    try {
      const offlineOps = wx.getStorageSync('offlineOperations') || []
      offlineOps.push({
        collection,
        operation,
        docId,
        updateData,
        timestamp: Date.now()
      })
      wx.setStorageSync('offlineOperations', offlineOps)
      console.log('📝 记录离线操作')
    } catch (error) {
      console.warn('⚠️ 记录离线操作失败:', error)
    }
  }

  /**
   * 设置生命周期监听
   */
  setupLifecycleListeners() {
    // 小程序显示时检查更新
    wx.onAppShow(() => {
      console.log('📱 小程序显示，检查数据更新')
      this.checkForUpdates()
    })

    // 小程序隐藏时停止同步
    wx.onAppHide(() => {
      console.log('📱 小程序隐藏，暂停同步')
      // 不完全停止，保持低频同步
    })
  }

  /**
   * 添加数据变更监听器
   */
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  /**
   * 移除数据变更监听器
   */
  removeEventListener(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 触发数据变更事件
   */
  emitDataChange(collection, operation, docId, data) {
    const event = `dataChange:${collection}`
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback({ collection, operation, docId, data })
        } catch (error) {
          console.warn('⚠️ 事件回调执行失败:', error)
        }
      })
    }
  }

  /**
   * 获取管理器状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      cacheSize: this.localCache.size,
      lastSyncTime: this.lastSyncTime,
      syncFrequency: this.syncFrequency,
      isAutoSyncRunning: !!this.syncInterval
    }
  }

  /**
   * 清理资源
   */
  destroy() {
    this.stopAutoSync()
    this.localCache.clear()
    this.eventListeners.clear()
    this.isInitialized = false
    console.log('🗑️ RealTimeDataManager 已销毁')
  }
}

// 创建单例实例
const realTimeDataManager = new RealTimeDataManager()

// 导出
module.exports = realTimeDataManager
