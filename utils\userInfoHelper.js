/**
 * 用户信息管理工具
 * 提供用户信息的获取、保存、验证等功能
 */

const { DEFAULT_USER_INFO, STORAGE_KEYS, COMPOUND_SURNAMES, REGEX } = require('./constants.js');

/**
 * 获取用户信息
 * @param {boolean} withDefaults 是否使用默认值填充
 * @returns {Object} 用户信息对象
 */
function getUserInfo(withDefaults = true) {
  try {
    const storedUserInfo = wx.getStorageSync(STORAGE_KEYS.USER_INFO);
    
    if (!storedUserInfo) {
      return withDefaults ? { ...DEFAULT_USER_INFO } : null;
    }
    
    // 合并默认值和存储的值
    return withDefaults ? { ...DEFAULT_USER_INFO, ...storedUserInfo } : storedUserInfo;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return withDefaults ? { ...DEFAULT_USER_INFO } : null;
  }
}

/**
 * 保存用户信息
 * @param {Object} userInfo 用户信息对象
 * @returns {boolean} 保存是否成功
 */
function saveUserInfo(userInfo) {
  try {
    // 验证用户信息格式
    if (!userInfo || typeof userInfo !== 'object') {
      throw new Error('用户信息格式无效');
    }
    
    // 合并现有信息
    const currentInfo = getUserInfo(true);
    const updatedInfo = {
      ...currentInfo,
      ...userInfo,
      updateTime: new Date().toISOString()
    };
    
    wx.setStorageSync(STORAGE_KEYS.USER_INFO, updatedInfo);
    return true;
  } catch (error) {
    console.error('保存用户信息失败:', error);
    return false;
  }
}

/**
 * 清除用户信息
 */
function clearUserInfo() {
  try {
    wx.removeStorageSync(STORAGE_KEYS.USER_INFO);
    wx.removeStorageSync(STORAGE_KEYS.TOKEN);
    return true;
  } catch (error) {
    console.error('清除用户信息失败:', error);
    return false;
  }
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
function isUserLoggedIn() {
  try {
    const userInfo = wx.getStorageSync(STORAGE_KEYS.USER_INFO);
    const token = wx.getStorageSync(STORAGE_KEYS.TOKEN);
    return !!(userInfo && token);
  } catch (error) {
    console.error('检查登录状态失败:', error);
    return false;
  }
}

/**
 * 为头像解析姓名（智能提取显示字符）
 * @param {string} name 姓名
 * @returns {string} 头像显示字符
 */
function parseNameForAvatar(name) {
  if (!name || typeof name !== 'string') {
    return '用';
  }

  const trimmedName = name.trim();

  // 1. 检查复姓，取第一个字
  for (let surname of COMPOUND_SURNAMES) {
    if (trimmedName.startsWith(surname)) {
      return surname.charAt(0); // 复姓取第一个字
    }
  }

  // 2. 中文名称处理
  if (REGEX.CHINESE.test(trimmedName)) {
    // 如果包含中文，取第一个中文字符
    const chineseMatch = trimmedName.match(REGEX.CHINESE);
    return chineseMatch ? chineseMatch[0] : trimmedName.charAt(0);
  }

  // 3. 英文名或昵称处理
  if (REGEX.ENGLISH.test(trimmedName)) {
    // 纯英文，取首字母大写
    return trimmedName.charAt(0).toUpperCase();
  }

  // 4. 昵称特殊处理
  if (trimmedName.startsWith('小') && trimmedName.length > 1) {
    return '小'; // 小妮子 -> 小
  }
  if (trimmedName.startsWith('老') && trimmedName.length > 1) {
    return '老'; // 老王 -> 老
  }
  if (trimmedName.startsWith('阿') && trimmedName.length > 1) {
    return '阿'; // 阿强 -> 阿
  }

  // 5. 默认取第一个字符
  return trimmedName.charAt(0) || '用';
}

/**
 * 获取头像文字（智能解析姓名）
 * @param {Object} userInfo 用户信息对象
 * @returns {string} 头像文字
 */
function getAvatarText(userInfo) {
  try {
    const name = userInfo.name || userInfo.nickName || '用户';
    return parseNameForAvatar(name);
  } catch (error) {
    console.error('解析头像文字失败:', error);
    return '用';
  }
}

/**
 * 验证用户信息字段
 * @param {string} field 字段名
 * @param {string} value 字段值
 * @returns {Object} 验证结果 {valid: boolean, message: string}
 */
function validateUserField(field, value) {
  if (!value || typeof value !== 'string') {
    return { valid: false, message: `${field}不能为空` };
  }

  const trimmedValue = value.trim();
  
  switch (field) {
    case 'name':
      if (trimmedValue.length < 1) {
        return { valid: false, message: '姓名不能为空' };
      }
      if (trimmedValue.length > 20) {
        return { valid: false, message: '姓名长度不能超过20个字符' };
      }
      break;
      
    case 'phone':
      if (trimmedValue && !REGEX.PHONE.test(trimmedValue)) {
        return { valid: false, message: '手机号格式不正确' };
      }
      break;
      
    case 'email':
      if (trimmedValue && !REGEX.EMAIL.test(trimmedValue)) {
        return { valid: false, message: '邮箱格式不正确' };
      }
      break;
      
    case 'school':
      if (trimmedValue.length > 50) {
        return { valid: false, message: '学校名称长度不能超过50个字符' };
      }
      break;
      
    case 'subject':
      if (trimmedValue.length > 20) {
        return { valid: false, message: '学科名称长度不能超过20个字符' };
      }
      break;
  }

  return { valid: true, message: '' };
}

/**
 * 更新用户头像文字
 * @param {Object} userInfo 用户信息对象
 * @returns {Object} 更新后的用户信息
 */
function updateAvatarText(userInfo) {
  const updatedInfo = { ...userInfo };
  updatedInfo.avatarText = getAvatarText(updatedInfo);
  return updatedInfo;
}

/**
 * 格式化用户显示名称
 * @param {Object} userInfo 用户信息对象
 * @returns {string} 显示名称
 */
function getDisplayName(userInfo) {
  if (!userInfo) return '用户';
  
  // 优先使用真实姓名，其次使用微信昵称
  return userInfo.name || userInfo.nickName || '用户';
}

/**
 * 检查用户信息是否完整
 * @param {Object} userInfo 用户信息对象
 * @returns {Object} 检查结果 {complete: boolean, missing: string[]}
 */
function checkUserInfoCompleteness(userInfo) {
  const requiredFields = ['name', 'school'];
  const missing = [];
  
  for (const field of requiredFields) {
    if (!userInfo[field] || userInfo[field].trim() === '') {
      missing.push(field);
    }
  }
  
  return {
    complete: missing.length === 0,
    missing: missing
  };
}

module.exports = {
  getUserInfo,
  saveUserInfo,
  clearUserInfo,
  isUserLoggedIn,
  parseNameForAvatar,
  getAvatarText,
  validateUserField,
  updateAvatarText,
  getDisplayName,
  checkUserInfoCompleteness
};
