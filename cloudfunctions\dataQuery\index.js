// 数据查询云函数
// 专门用于管理后台查询小程序数据库

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 通用响应格式
const createResponse = (code = 200, message = 'success', data = null) => {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

// 数据查询处理器
const dataHandlers = {
  // 获取总用户数
  async getTotalUsers() {
    try {
      const result = await db.collection('users').count()
      return result.total || 0
    } catch (error) {
      console.error('查询总用户数失败:', error)
      return 0
    }
  },

  // 获取活跃教师用户数
  async getActiveTeachers() {
    try {
      // 🔥 直接查询users集合（已确认存在）
      console.log('🔍 查询users集合总数...')
      const totalResult = await db.collection('users').count()
      console.log(`✅ users集合查询结果: ${totalResult.total} 个用户`)
      
      // 尝试查询有lastLoginTime的活跃用户
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      
      try {
        const activeResult = await db.collection('users')
          .where({
            lastLoginTime: db.command.gte(thirtyDaysAgo)
          })
          .count()
        
        // 如果有活跃用户，返回活跃用户数
        if (activeResult.total > 0) {
          console.log(`✅ 找到活跃用户: ${activeResult.total}`)
          return activeResult.total
        }
      } catch (activeError) {
        console.log('❌ lastLoginTime字段查询失败，使用总用户数')
      }
      
      // 🔥 如果没有活跃用户记录，返回总用户数作为估算
      console.log(`📊 使用总用户数作为活跃用户估算: ${totalResult.total}`)
      return totalResult.total || 0
    } catch (error) {
      console.error('❌ 查询活跃教师失败:', error)
      return 0
    }
  },

  // 获取评语总数
  async getTotalComments() {
    try {
      const result = await db.collection('comments').count()
      return result.total || 0
    } catch (error) {
      console.error('查询评语总数失败:', error)
      return 0
    }
  },

  // 获取今日评语数
  async getTodayComments() {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      const todayResult = await db.collection('comments')
        .where({
          createTime: db.command.gte(today)
        })
        .count()
      
      // 🔥 如果今天有评语，返回今天的数量
      if (todayResult.total > 0) {
        return todayResult.total
      }
      
      // 🔥 如果今天没有评语，返回最近7天的评语数作为参考
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      const recentResult = await db.collection('comments')
        .where({
          createTime: db.command.gte(sevenDaysAgo)
        })
        .count()
      
      console.log('今天没有评语，返回最近7天评语数:', recentResult.total)
      return recentResult.total || 0
    } catch (error) {
      console.error('查询今日评语数失败:', error)
      return 0
    }
  },

  // 获取AI调用总数
  async getTotalAICalls() {
    try {
      // 🔥 直接查询ai_usage集合（已确认存在）
      console.log('🔍 查询ai_usage集合...')
      const result = await db.collection('ai_usage').count()
      console.log(`✅ ai_usage集合查询结果: ${result.total} 次调用`)

      // 🔍 详细调试信息
      if (result.total > 0) {
        const sampleData = await db.collection('ai_usage').limit(3).get()
        console.log('📄 ai_usage样本数据:', JSON.stringify(sampleData.data, null, 2))
      }

      return result.total || 0
    } catch (error) {
      console.error('❌ 查询AI调用总数失败:', error)
      // 如果ai_usage查询失败，直接使用comments集合的数量（1:1对应）
      try {
        const commentsCount = await this.getTotalComments()
        console.log(`📊 基于评语数统计AI调用次数: ${commentsCount}`)
        return commentsCount // 每个评语对应一次AI调用，不再乘以2
      } catch (estimateError) {
        console.error('❌ 获取评语数量也失败:', estimateError)
        return 0
      }
    }
  },

  // 🔍 新增：调试AI调用次数统计问题
  async debugAICallsStats() {
    console.log('🔍 开始调试AI调用次数统计问题...')

    const results = {
      timestamp: new Date().toISOString(),
      checks: []
    }

    try {
      // 检查1: comments集合的记录数
      const commentsCount = await db.collection('comments').count()
      results.checks.push({
        name: 'comments集合记录数',
        value: commentsCount.total,
        description: '每个评语对应一次AI调用'
      })

      // 检查2: ai_usage集合的记录数
      try {
        const aiUsageCount = await db.collection('ai_usage').count()
        results.checks.push({
          name: 'ai_usage集合记录数',
          value: aiUsageCount.total,
          description: 'AI使用记录表'
        })
      } catch (error) {
        results.checks.push({
          name: 'ai_usage集合记录数',
          value: 'ERROR',
          description: `查询失败: ${error.message}`
        })
      }

      // 检查3: 最近的comments记录
      const recentComments = await db.collection('comments')
        .orderBy('createTime', 'desc')
        .limit(5)
        .get()

      results.checks.push({
        name: '最近5条评语记录',
        value: recentComments.data.map(c => ({
          id: c._id,
          teacherId: c.teacherId,
          teacherName: c.teacherName,
          createTime: c.createTime,
          studentName: c.studentName
        })),
        description: '最新的评语生成记录'
      })

      console.log('🔍 调试结果:', JSON.stringify(results, null, 2))
      return results

    } catch (error) {
      console.error('🔍 调试过程出错:', error)
      results.checks.push({
        name: '调试错误',
        value: error.message,
        description: '调试过程中发生的错误'
      })
      return results
    }
  },

    try {
      // 1. 检查ai_usage集合
      console.log('📊 检查ai_usage集合...')
      try {
        const aiUsageCount = await db.collection('ai_usage').count()
        const aiUsageSample = await db.collection('ai_usage').limit(5).get()

        results.checks.push({
          name: 'ai_usage集合',
          exists: true,
          count: aiUsageCount.total,
          sampleData: aiUsageSample.data,
          status: aiUsageCount.total > 0 ? 'HAS_DATA' : 'EMPTY'
        })

        console.log(`✅ ai_usage集合: ${aiUsageCount.total} 条记录`)
        if (aiUsageSample.data.length > 0) {
          console.log('📄 样本数据:', JSON.stringify(aiUsageSample.data[0], null, 2))
        }
      } catch (error) {
        results.checks.push({
          name: 'ai_usage集合',
          exists: false,
          error: error.message,
          status: 'NOT_EXISTS'
        })
        console.log('❌ ai_usage集合不存在或查询失败:', error.message)
      }

      // 2. 检查今日comments
      console.log('📊 检查今日comments...')
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      const todayCommentsCount = await db.collection('comments')
        .where({
          createTime: db.command.gte(today)
        })
        .count()

      results.checks.push({
        name: '今日comments',
        count: todayCommentsCount.total,
        estimatedAICalls: Math.round(todayCommentsCount.total * 1.2),
        status: todayCommentsCount.total > 0 ? 'HAS_DATA' : 'EMPTY'
      })

      console.log(`✅ 今日comments: ${todayCommentsCount.total} 条`)
      console.log(`📊 估算AI调用次数: ${Math.round(todayCommentsCount.total * 1.2)} 次`)

      // 3. 检查其他AI相关集合
      const aiCollections = ['aiUsage', 'usage_stats', 'ai_logs', 'ai_error_logs']

      for (const collection of aiCollections) {
        try {
          const count = await db.collection(collection).count()
          results.checks.push({
            name: collection,
            exists: true,
            count: count.total,
            status: count.total > 0 ? 'HAS_DATA' : 'EMPTY'
          })
          console.log(`✅ ${collection}: ${count.total} 条记录`)
        } catch (error) {
          results.checks.push({
            name: collection,
            exists: false,
            error: error.message,
            status: 'NOT_EXISTS'
          })
        }
      }

      return results

    } catch (error) {
      console.error('❌ 调试过程出错:', error)
      return {
        success: false,
        error: error.message,
        data: results
      }
    }
  },

  // 🔒 安全修复：禁用AI数据全局清理功能
  async fixAICallsDisplay() {
    console.log('🚫 AI统计数据修复功能已禁用')

    const fixResults = {
      timestamp: new Date().toISOString(),
      steps: [
        {
          step: 1,
          name: '安全策略检查',
          issue: '该功能涉及全局数据删除',
          action: '禁用该功能以保护用户数据',
          status: 'blocked',
          result: '功能已被安全策略阻止'
        }
      ],
      summary: {
        totalIssuesFound: 1,
        totalIssuesFixed: 0,
        remainingIssues: 1
      }
    }

    try {
      // 🔒 安全策略：不允许全局数据删除操作
      console.log('🔒 安全策略: 禁止全局AI使用数据删除操作')
      console.log('💡 建议: 如需清理数据，请使用带用户ID过滤的安全方法')

      const recommendations = [
        '🔄 建议刷新管理后台页面以清除缓存',
        '📱 建议在小程序设置页面使用"清理本地缓存"功能',
        '🗂️ 建议在管理后台使用"清理浏览器缓存"功能',
        '⚠️ 全局数据删除功能已被禁用以保护用户数据安全'
      ]

      fixResults.summary.remainingIssues = fixResults.summary.totalIssuesFound - fixResults.summary.totalIssuesFixed

      console.log('🎯 安全策略执行完成:')
      console.log(`   发现问题: ${fixResults.summary.totalIssuesFound} 个`)
      console.log(`   已修复: ${fixResults.summary.totalIssuesFixed} 个`)
      console.log(`   剩余问题: ${fixResults.summary.remainingIssues} 个`)

      return {
        success: false,
        message: '功能已被安全策略禁用',
        data: fixResults,
        recommendations,
        error: 'GLOBAL_DELETE_OPERATIONS_DISABLED'
      }

    } catch (error) {
      console.error('❌ 修复过程出错:', error)
      return {
        success: false,
        error: error.message,
        data: fixResults
      }
    }
  },

  // 🔒 安全修复：移除危险的全局删除功能
  // 该功能会删除所有用户的AI使用记录，违反数据隔离原则
  // 如需清理特定用户数据，请使用带用户ID过滤的安全方法
  async forceDeleteAIUsage() {
    console.log('🚫 危险操作已禁用：全局AI数据删除功能不再可用')
    return {
      success: false,
      message: '该功能已禁用：全局删除操作违反数据安全原则',
      error: 'FORBIDDEN_GLOBAL_DELETE_OPERATION'
    }
  },

  // 🔥 新增：获取tokens总消耗统计
  async getTotalTokensUsage() {
    try {
      const result = await db.collection('comments')
        .aggregate()
        .group({
          _id: null,
          totalTokens: $.sum({
            $cond: {
              if: { $gt: ['$tokensUsed', 0] },
              then: '$tokensUsed',
              else: { $multiply: [{ $strLenCP: '$content' }, 1.5] } // 基于内容长度估算
            }
          }),
          totalComments: $.sum(1),
          avgTokensPerComment: $.avg({
            $cond: {
              if: { $gt: ['$tokensUsed', 0] },
              then: '$tokensUsed',
              else: { $multiply: [{ $strLenCP: '$content' }, 1.5] }
            }
          })
        })
        .end()

      if (result.list && result.list.length > 0) {
        return {
          totalTokens: Math.round(result.list[0].totalTokens || 0),
          totalComments: result.list[0].totalComments || 0,
          avgTokensPerComment: Math.round(result.list[0].avgTokensPerComment || 0)
        }
      }

      return {
        totalTokens: 0,
        totalComments: 0,
        avgTokensPerComment: 0
      }
    } catch (error) {
      console.error('查询tokens总消耗失败:', error)
      return {
        totalTokens: 0,
        totalComments: 0,
        avgTokensPerComment: 0
      }
    }
  },

  // 获取学生总数
  async getTotalStudents() {
    try {
      // 🔥 直接查询students集合（已确认存在）
      console.log('🔍 查询students集合...')
      const result = await db.collection('students').count()
      console.log(`✅ students集合查询结果: ${result.total} 个学生`)
      return result.total || 0
    } catch (error) {
      console.error('❌ 查询学生总数失败:', error)
      return 0
    }
  },

  // 检查集合是否存在的辅助方法
  async checkCollectionExists(collectionName) {
    try {
      const result = await db.collection(collectionName).limit(1).get()
      return true
    } catch (error) {
      return false
    }
  },

  // 获取教师使用统计
  async getTeacherUsage() {
    try {
      console.log('开始获取教师使用统计...')

      // 获取所有用户的活动统计 - 从comments表统计，使用实际存在的字段
      const usageStats = await db.collection('comments')
        .aggregate()
        .group({
          _id: '$teacherId',
          usageCount: $.sum(1),
          lastActivity: $.max('$createTime'),
          firstActivity: $.min('$createTime'),
          // 基于评语内容长度估算tokens消耗
          totalTokens: $.sum({
            $multiply: [{ $strLenCP: '$content' }, 1.5]
          })
        })
        .lookup({
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'userInfo'
        })
        .addFields({
          avgTokensPerUse: {
            $cond: {
              if: { $gt: ['$usageCount', 0] },
              then: { $divide: ['$totalTokens', '$usageCount'] },
              else: 0
            }
          }
        })
        .sort({
          usageCount: -1
        })
        .limit(50)
        .end()

      console.log('教师使用统计聚合查询结果:', usageStats)

      const teacherStats = usageStats.list.map((stat, index) => ({
        id: stat._id,
        openid: stat._id,
        wechatName: stat.userInfo[0]?.nickName || stat.userInfo[0]?.nickname || `微信用户${index + 1}`,
        realName: stat.userInfo[0]?.nickName || `教师${index + 1}`,
        usageCount: stat.usageCount,
        totalTokens: Math.round(stat.totalTokens || 0),
        avgTokensPerUse: Math.round(stat.avgTokensPerUse || 0),
        lastActivity: stat.lastActivity,
        firstActivity: stat.firstActivity,
        status: (Date.now() - new Date(stat.lastActivity).getTime()) < 24 * 60 * 60 * 1000 ? 'active' : 'inactive'
      }))

      console.log('处理后的教师统计数据:', teacherStats)

      return {
        list: teacherStats,
        total: teacherStats.length
      }
    } catch (error) {
      console.error('获取教师使用统计失败:', error)
      return {
        list: [],
        total: 0
      }
    }
  },

  // 获取仪表板统计数据
  async getDashboardStats() {
    try {
      const [totalUsers, todayComments, aiCalls, studentTotal, tokensStats] = await Promise.all([
        this.getActiveTeachers(),
        this.getTodayComments(),
        this.getTotalAICalls(),
        this.getTotalStudents(),
        this.getTotalTokensUsage() // 🔥 添加tokens统计
      ])
      
      return {
        totalUsers,
        todayComments,
        aiCalls,
        totalStudents: studentTotal, // 🔥 修复字段名匹配
        totalTokens: tokensStats.totalTokens, // 🔥 添加tokens总数
        avgTokensPerComment: tokensStats.avgTokensPerComment, // 🔥 添加平均tokens
        satisfaction: 4.8,
        lastUpdated: new Date().toISOString()
      }
    } catch (error) {
      console.error('获取仪表板统计失败:', error)
      return {
        totalUsers: 0,
        todayComments: 0,
        aiCalls: 0,
        totalStudents: 0, // 🔥 修复字段名匹配
        totalTokens: 0, // 🔥 添加默认值
        avgTokensPerComment: 0, // 🔥 添加默认值
        satisfaction: 0,
        lastUpdated: new Date().toISOString()
      }
    }
  },

  // 获取最近活动记录
  async getRecentActivities(limit = 10) {
    try {
      const commentsResult = await db.collection('comments')
        .orderBy('createTime', 'desc')
        .limit(limit)
        .get()
      
      return commentsResult.data.map(comment => ({
        id: comment._id,
        userId: comment.openid || comment.userId || 'unknown',
        userName: comment.teacherName || comment.userName || '未知用户',
        action: `为学生"${comment.studentName || '未知学生'}"生成评语`,
        actionType: 'comment_generate',
        timestamp: comment.createTime || new Date().toISOString(),
        metadata: {
          studentName: comment.studentName,
          subject: comment.subject || '未知科目',
          contentLength: comment.content ? comment.content.length : 0
        }
      }))
    } catch (error) {
      console.error('查询最近活动失败:', error)
      return []
    }
  },

  // 获取学生数据
  async getStudents(params = {}) {
    try {
      const { page = 1, limit = 20, keyword = '' } = params
      const skip = (page - 1) * limit
      
      let query = db.collection('students')
      
      if (keyword) {
        query = query.where({
          name: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        })
      }
      
      const countResult = await query.count()
      const dataResult = await query
        .orderBy('updateTime', 'desc')
        .skip(skip)
        .limit(limit)
        .get()
      
      const students = dataResult.data.map(student => ({
        id: student._id,
        name: student.name || student.studentName,
        class: student.class || student.className,
        teacher: student.teacher || student.teacherName,
        commentsCount: student.commentsCount || 0,
        lastUpdate: student.updateTime || student.lastUpdate || new Date().toISOString(),
        status: student.status || 'active'
      }))
      
      return {
        list: students,
        total: countResult.total,
        page,
        limit
      }
    } catch (error) {
      console.error('查询学生数据失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  },

  // 获取评语记录
  async getComments(params = {}) {
    try {
      const { page = 1, limit = 20, teacherId = '', studentId = '' } = params
      const skip = (page - 1) * limit
      
      let query = db.collection('comments')
      
      const whereConditions = {}
      if (teacherId) whereConditions.openid = teacherId
      if (studentId) whereConditions.studentId = studentId
      
      if (Object.keys(whereConditions).length > 0) {
        query = query.where(whereConditions)
      }
      
      const countResult = await query.count()
      const dataResult = await query
        .orderBy('createTime', 'desc')
        .skip(skip)
        .limit(limit)
        .get()
      
      // 获取所有唯一的教师ID，用于批量查询教师信息
      const teacherIds = [...new Set(dataResult.data.map(comment => comment.openid || comment.teacherId).filter(Boolean))]

      // 批量查询教师信息
      let teacherMap = {}
      if (teacherIds.length > 0) {
        try {
          const teachersResult = await db.collection('users').where({
            _id: _.in(teacherIds)
          }).get()

          teachersResult.data.forEach(teacher => {
            teacherMap[teacher._id] = teacher.nickName || teacher.nickname || '未知教师'
          })
          console.log('✅ 获取教师信息成功:', teacherMap)
        } catch (error) {
          console.error('❌ 获取教师信息失败:', error)
        }
      }

      const comments = dataResult.data.map(comment => {
        const teacherId = comment.openid || comment.teacherId
        const teacherName = comment.teacherName || teacherMap[teacherId] || '未知教师'
        
        // 🔥 修复tokens统计：如果没有tokensUsed字段，基于内容长度估算
        const estimatedTokens = comment.tokensUsed || comment.tokens || 
          (comment.content ? Math.ceil(comment.content.length * 1.5) : 50) // 每个字符估算1.5个token

        return {
          id: comment._id,
          studentId: comment.studentId,
          studentName: comment.studentName,
          teacherId: teacherId,
          teacherName: teacherName,
          teacher: teacherName, // 添加 teacher 字段用于前端显示
          className: comment.className, // 添加 className 字段
          class: comment.className, // 兼容 class 字段
          content: comment.content,
          type: comment.type || comment.commentType || '温暖鼓励型', // 添加 type 字段，提供默认值
          aiModel: comment.aiModel || comment.model || 'doubao',
          tokensUsed: estimatedTokens, // 🔥 使用估算的tokens
          createTime: comment.createTime,
          subject: comment.subject,
          status: comment.status || 'success' // 添加状态字段
        }
      })
      
      return {
        list: comments,
        total: countResult.total,
        page,
        limit
      }
    } catch (error) {
      console.error('查询评语记录失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  },

  // 获取单个学生信息
  async getStudent(studentId) {
    try {
      if (!studentId) {
        throw new Error('学生ID不能为空')
      }
      
      const result = await db.collection('students').doc(studentId).get()
      
      if (!result.data) {
        return null
      }
      
      const student = result.data
      return {
        id: student._id,
        name: student.name || student.studentName,
        class: student.class || student.className,
        teacher: student.teacher || student.teacherName,
        commentsCount: student.commentsCount || 0,
        lastUpdate: student.updateTime || student.lastUpdate || new Date().toISOString(),
        status: student.status || 'active'
      }
    } catch (error) {
      console.error('查询学生信息失败:', error)
      return null
    }
  },

  // 获取单个评语信息
  async getComment(commentId) {
    try {
      if (!commentId) {
        throw new Error('评语ID不能为空')
      }
      
      const result = await db.collection('comments').doc(commentId).get()
      
      if (!result.data) {
        return null
      }
      
      const comment = result.data
      return {
        id: comment._id,
        studentId: comment.studentId,
        studentName: comment.studentName,
        teacherId: comment.openid || comment.teacherId,
        teacherName: comment.teacherName,
        content: comment.content,
        aiModel: comment.aiModel || comment.model || 'doubao',
        tokensUsed: comment.tokensUsed || comment.tokens || 0,
        createTime: comment.createTime,
        subject: comment.subject
      }
    } catch (error) {
      console.error('查询评语信息失败:', error)
      return null
    }
  },

  // 获取班级列表
  async getClasses(params = {}) {
    try {
      const { page = 1, limit = 20, teacherId = '' } = params
      const skip = (page - 1) * limit
      
      let query = db.collection('classes')
      
      if (teacherId) {
        query = query.where({ teacherId })
      }
      
      const countResult = await query.count()
      const dataResult = await query
        .orderBy('createTime', 'desc')
        .skip(skip)
        .limit(limit)
        .get()
      
      const classes = dataResult.data.map(classInfo => ({
        id: classInfo._id,
        _id: classInfo._id,
        name: classInfo.className || classInfo.name,
        className: classInfo.className || classInfo.name,
        grade: classInfo.grade,
        teacherId: classInfo.teacherId,
        teacherName: classInfo.teacherName,
        studentCount: classInfo.studentCount || 0,
        description: classInfo.description,
        createTime: classInfo.createTime,
        status: classInfo.status || 'active'
      }))
      
      return {
        list: classes,
        classes: classes, // 兼容不同的数据结构
        total: countResult.total,
        page,
        limit
      }
    } catch (error) {
      console.error('查询班级数据失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  },

  async testConnection() {
    try {
      const result = await db.collection('users').limit(1).get()
      
      return {
        success: true,
        message: '数据库连接正常（云函数环境）',
        collections: ['users', 'students', 'comments'],
        sampleData: result.data.length > 0 ? result.data[0] : null
      }
    } catch (error) {
      console.error('数据库连接测试失败:', error)
      return {
        success: false,
        message: error.message,
        collections: [],
        sampleData: null
      }
    }
  },

  async diagnoseDatabaseStructure() {
    // 扩展检查更多可能的集合名称
    const collections = [
      'users', 'user', 'userInfo',
      'students', 'student', 'studentInfo', 'studentList',
      'comments', 'comment', 'commentList', 'evaluations',
      'classes', 'class', 'classInfo',
      'ai_usage', 'aiUsage', 'usage_stats'
    ]
    const results = {}
    
    for (const collection of collections) {
      try {
        const countResult = await db.collection(collection).count()
        const sampleResult = await db.collection(collection).limit(2).get()
        
        results[collection] = {
          exists: true,
          count: countResult.total,
          sampleFields: sampleResult.data.length > 0 ? Object.keys(sampleResult.data[0]) : [],
          hasData: countResult.total > 0,
          sampleData: sampleResult.data.length > 0 ? sampleResult.data[0] : null
        }
        
        console.log(`📊 集合 ${collection}: ${countResult.total} 条记录`)
        if (sampleResult.data.length > 0) {
          console.log(`📋 ${collection} 字段:`, Object.keys(sampleResult.data[0]))
          console.log(`📄 样本数据:`, JSON.stringify(sampleResult.data[0], null, 2))
        }
      } catch (error) {
        results[collection] = {
          exists: false,
          error: error.message,
          count: 0,
          sampleFields: [],
          hasData: false,
          sampleData: null
        }
        console.log(`❌ 集合 ${collection} 不存在或查询失败: ${error.message}`)
      }
    }
    
    return {
      environment: cloud.DYNAMIC_CURRENT_ENV,
      timestamp: new Date().toISOString(),
      collections: results,
      summary: {
        totalCollections: Object.keys(results).length,
        existingCollections: Object.values(results).filter(r => r.exists).length,
        collectionsWithData: Object.values(results).filter(r => r.hasData).length
      }
    }
  }
}

// 主函数
exports.main = async (event, context) => {
  console.log('📥 DataQuery 收到请求:', event)
  
  try {
    const { action, params = {} } = event
    
    if (!action) {
      throw new Error('缺少action参数')
    }
    
    let result = null
    
    switch (action) {
      case 'getDashboardStats':
        result = await dataHandlers.getDashboardStats()
        break
        
      case 'getRecentActivities':
        result = await dataHandlers.getRecentActivities(params.limit)
        break
        
      case 'getStudents':
        result = await dataHandlers.getStudents(params)
        break
        
      case 'getComments':
        result = await dataHandlers.getComments(params)
        break
        
      case 'testConnection':
        result = await dataHandlers.testConnection()
        break
        
      case 'getStudent':
        result = await dataHandlers.getStudent(params.id)
        break
        
      case 'getComment':
        result = await dataHandlers.getComment(params.id)
        break
        
      case 'getClasses':
        result = await dataHandlers.getClasses(params)
        break

      case 'getTeacherUsage':
        result = await dataHandlers.getTeacherUsage()
        break

      case 'diagnoseDatabaseStructure':
        result = await dataHandlers.diagnoseDatabaseStructure()
        break

      case 'getTotalTokensUsage':
        result = await dataHandlers.getTotalTokensUsage()
        break

      case 'debugAICallsStats':
        result = await dataHandlers.debugAICallsStats()
        break

      case 'fixAICallsDisplay':
        result = await dataHandlers.fixAICallsDisplay()
        break

      case 'forceDeleteAIUsage':
        result = await dataHandlers.forceDeleteAIUsage()
        break

      default:
        throw new Error(`未知的action: ${action}`)
    }
    
    console.log('✅ DataQuery 处理成功:', action)
    return createResponse(200, 'success', result)
    
  } catch (error) {
    console.error('❌ DataQuery 处理失败:', error)
    return createResponse(500, error.message, null)
  }
}
