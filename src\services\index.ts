/**
 * 统一数据服务入口 - 架构优化版本
 * 
 * 架构说明：
 * 1. 数据查询：通过dataQuery云函数获取小程序数据
 * 2. 管理操作：通过adminAPI云函数进行管理后台操作  
 * 3. 认证体系：统一的权限验证和错误处理
 * 4. 错误降级：提供默认数据，确保前端稳定运行
 * 
 * 技术栈：
 * - 腾讯云开发Web SDK (数据连接)
 * - HTTP触发器 (管理操作)
 * - 云函数直接调用 (数据查询)
 */

import cloudbaseService from '../utils/cloudbaseConfig'
import { env } from '../utils/env'
import { errorHandler, handleApiError } from '../utils/errorHandler'

// 统一数据服务类
class UnifiedDataService {
  private adminApiUrl = env.API_BASE_URL

  constructor() {
    console.log('🎯 统一数据服务初始化')
    console.log('📊 数据查询：dataQuery云函数')
    console.log('🔧 管理操作：adminAPI HTTP触发器')
  }

  /**
   * 调用adminAPI进行管理操作 (使用正确的module.method格式)
   */
  async callAdminAPI(action: string, data: any = {}) {
    try {
      console.log(`🌐 调用adminAPI: ${action}`, data)
      
      const response = await fetch(this.adminApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WX-SOURCE': 'admin-dashboard'
        },
        body: JSON.stringify({
          action, // 确保使用module.method格式，如 'auth.login', 'data.getUsers'
          ...data,
          timestamp: Date.now(),
          requestId: `admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log(`✅ adminAPI调用成功: ${action}`, result)
      
      return result
    } catch (error) {
      console.error(`❌ adminAPI调用失败: ${action}`, error)
      throw error
    }
  }

  async getDashboardStats() {
    return handleApiError(
      'getDashboardStats',
      async () => {
        console.log('📊 获取仪表板统计数据 - 调用dataQuery云函数')
        const result = await cloudbaseService.callFunction('dataQuery', {
          action: 'getDashboardStats'
        })

        let dashboardData = {}
        if (result.code === 0) {
          console.log('✅ 仪表板数据获取成功:', result.data)
          dashboardData = result.data
        } else {
          throw new Error(result.message || '数据获取失败')
        }

        // 并行获取学生总数
        try {
          const studentResult = await cloudbaseService.callFunction('dataQuery', {
            action: 'getStudents',
            params: { limit: 1 } // 只需要总数
          })
          
          if (studentResult.code === 0) {
            dashboardData.studentTotal = studentResult.data.total || 0
            console.log('✅ 学生总数获取成功:', dashboardData.studentTotal)
          }
        } catch (studentError) {
          console.warn('获取学生总数失败，使用默认值:', studentError)
          dashboardData.studentTotal = 0
        }

        return dashboardData
      },
      { component: 'Dashboard' }
    )
  }

  async getRecentActivities(limit = 10) {
    return handleApiError(
      'getRecentActivities',
      async () => {
        console.log('📊 获取最近活动记录 - 调用dataQuery云函数')
        const result = await cloudbaseService.callFunction('dataQuery', {
          action: 'getRecentActivities',
          params: { limit }
        })

        if (result.code === 0) {
          console.log(`✅ 获取到 ${result.data.length} 条活动记录`)
          return result.data
        } else {
          throw new Error(result.message || '活动记录获取失败')
        }
      },
      { component: 'Dashboard' }
    )
  }

  async getSystemMetrics() {
    console.log('📊 获取系统性能指标 - 模拟数据')
    try {
      const now = new Date()
      const hour = now.getHours()
      
      const baseCpu = hour >= 9 && hour <= 17 ? 45 : 25
      const baseMemory = hour >= 9 && hour <= 17 ? 60 : 35
      
      const metrics = {
        cpu: Math.round(baseCpu + Math.random() * 20),
        memory: Math.round(baseMemory + Math.random() * 25),
        storage: Math.round(15 + Math.random() * 20),
        apiResponseTime: Math.round(80 + Math.random() * 40),
        activeConnections: Math.round(5 + Math.random() * 25),
        timestamp: new Date().toISOString()
      }
      
      console.log('✅ 系统指标获取成功:', metrics)
      return metrics
    } catch (error) {
      console.error('❌ 系统指标获取失败:', error)
      return {
        cpu: 0,
        memory: 0,
        storage: 0,
        apiResponseTime: 0,
        activeConnections: 0,
        timestamp: new Date().toISOString()
      }
    }
  }

  async healthCheck() {
    console.log('🔧 健康检查 - 简单响应测试')
    try {
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'dataQuery'
      }
    } catch (error) {
      console.error('❌ 健康检查失败:', error)
      throw error
    }
  }

  async testConnection() {
    console.log('🔧 测试数据库连接 - 调用dataQuery云函数')
    try {
      const result = await cloudbaseService.callFunction('dataQuery', {
        action: 'testConnection'
      })

      if (result.code === 0) {
        console.log('✅ 数据库连接测试成功:', result.data)
        return result.data
      } else {
        throw new Error(result.message || '连接测试失败')
      }
    } catch (error) {
      console.error('❌ 数据库连接测试失败:', error)
      return {
        success: false,
        message: error.message,
        collections: [],
        sampleData: null
      }
    }
  }

  async getStudents(params = {}) {
    console.log('📊 获取学生数据 - 调用dataQuery云函数')
    try {
      const result = await cloudbaseService.callFunction('dataQuery', {
        action: 'getStudents',
        params
      })

      if (result.code === 0) {
        console.log('✅ 学生数据获取成功:', result.data)
        return result.data
      } else {
        throw new Error(result.message || '学生数据获取失败')
      }
    } catch (error) {
      console.error('❌ 学生数据获取失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  }

  async getComments(params = {}) {
    console.log('📊 获取评语数据 - 调用dataQuery云函数')
    try {
      const result = await cloudbaseService.callFunction('dataQuery', {
        action: 'getComments',
        params
      })

      if (result.code === 0) {
        console.log('✅ 评语数据获取成功:', result.data)
        return result.data
      } else {
        throw new Error(result.message || '评语数据获取失败')
      }
    } catch (error) {
      console.error('❌ 评语数据获取失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  }
}

// 创建统一服务实例
const unifiedDataService = new UnifiedDataService()

import { adminDataService } from './AdminDataService'

// 主要数据服务 - 使用新的统一接口实现
export const dataService = adminDataService

// 兼容性导出，方便迁移
export const getDataBridgeService = () => adminDataService
export const getDirectDataService = () => adminDataService

// 保留原有的UnifiedDataService作为备用
export const legacyDataService = unifiedDataService

// 管理操作服务 (使用正确的module.method格式)
export const adminService = {
  // 认证相关
  login: (credentials: any) => unifiedDataService.callAdminAPI('auth.login', credentials),
  logout: () => unifiedDataService.callAdminAPI('auth.logout'),
  
  // 用户管理 
  getUsers: (params: any) => unifiedDataService.callAdminAPI('data.getUsers', params),
  createUser: (userData: any) => unifiedDataService.callAdminAPI('data.createUser', userData),
  
  // AI配置
  getAIConfig: () => unifiedDataService.callAdminAPI('ai.getConfig'),
  updateAIConfig: (config: any) => unifiedDataService.callAdminAPI('ai.updateConfig', config),
  
  // 系统管理
  getSystemInfo: () => unifiedDataService.callAdminAPI('system.getInfo'),
  clearCache: () => unifiedDataService.callAdminAPI('system.clearCache')
}

// 默认导出
export default dataService