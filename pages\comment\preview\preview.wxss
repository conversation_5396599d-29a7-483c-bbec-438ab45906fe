/* 评语预览页面 - 莫兰迪简约设计 */
.morandi-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  padding: 40rpx 32rpx;
  padding-bottom: 200rpx; /* 增加底部间距，避免被固定按钮遮挡 */
}

/* 页面标题 */
.page-header {
  margin-bottom: 32rpx;
}

.page-title {
  font-size: 64rpx;
  font-weight: 700;
  color: #2C3E50;
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.1);
}

.page-subtitle {
  font-size: 28rpx;
  color: #606266;
  font-weight: 400;
}

/* 生成信息卡片 */
.info-card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #606266;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 500;
}

/* 评语列表 */
.comment-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.comment-item {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.student-info {
  flex: 1;
}

.student-name {
  font-size: 32rpx;
  color: #2C3E50;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.student-class {
  font-size: 24rpx;
  color: #606266;
}

.comment-meta {
  font-size: 22rpx;
  color: #5470C6;
  font-weight: 500;
}

.comment-content {
  font-size: 28rpx;
  color: #2C3E50;
  line-height: 1.6;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 16rpx;
  border-left: 4rpx solid #5470C6;
}

.comment-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
}

.action-btn.secondary {
  background: rgba(84, 112, 198, 0.1);
  color: #5470C6;
  border: 1rpx solid rgba(84, 112, 198, 0.2);
}

.action-btn.danger {
  background: rgba(238, 102, 102, 0.1);
  color: #EE6666;
  border: 1rpx solid rgba(238, 102, 102, 0.2);
}

.action-btn:active {
  transform: scale(0.95);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #606266;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: #909399;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 32rpx 32rpx; /* 增加上下内边距 */
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(84, 112, 198, 0.1);
  box-shadow: 0 -4rpx 20rpx rgba(84, 112, 198, 0.1); /* 添加阴影增强层次感 */
  z-index: 100;
}

.action-row {
  display: flex;
  gap: 16rpx;
}

.back-btn {
  flex: 1;
  padding: 36rpx 32rpx; /* 增加垂直内边距，提升触摸体验 */
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(84, 112, 198, 0.3);
  text-align: center;
  transition: all 0.3s ease;
  min-height: 88rpx; /* 确保最小触摸高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn:active {
  background: rgba(84, 112, 198, 0.05);
  transform: scale(0.98);
}

.back-text {
  font-size: 32rpx;
  color: #5470C6;
  font-weight: 600;
}

.save-btn {
  flex: 2;
  padding: 36rpx 32rpx; /* 增加垂直内边距，提升触摸体验 */
  border-radius: 24rpx;
  background: linear-gradient(135deg, #91CC75 0%, #5CB85C 100%);
  box-shadow: 0 8rpx 32rpx rgba(145, 204, 117, 0.3);
  transition: all 0.3s ease;
  min-height: 88rpx; /* 确保最小触摸高度 */
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(145, 204, 117, 0.2);
}

.save-btn.loading {
  opacity: 0.8;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 32rpx;
  color: white;
  font-weight: 600;
}
