/**
 * AI服务集成
 * 支持豆包AI和其他大模型API
 */

class AIService {
  constructor() {
    this.config = {
      provider: 'doubao',
      apiKey: '',
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
      model: 'doubao-lite-4k',
      temperature: 0.7,
      maxTokens: 1000
    };
    
    this.loadConfig();
  }

  /**
   * 加载AI配置
   */
  loadConfig() {
    try {
      const savedConfig = wx.getStorageSync('aiConfig');
      if (savedConfig) {
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      console.error('加载AI配置失败:', error);
    }
  }

  /**
   * 保存AI配置
   */
  saveConfig() {
    try {
      wx.setStorageSync('aiConfig', this.config);
    } catch (error) {
      console.error('保存AI配置失败:', error);
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
  }

  /**
   * 生成评语
   */
  async generateComment(prompt, options = {}) {
    try {
      // 检查配置
      if (!this.config.apiKey) {
        throw new Error('请先配置AI API密钥');
      }

      // 获取动态模板内容
      let systemPrompt = '你是一位经验丰富的教师，擅长为学生写出温暖、具体、有针对性的评语。请根据学生的行为记录生成合适的评语。';
      
      try {
        // 获取模板缓存实例（已经是实例了）
        const templateCache = require('./templateCache');
        const templateType = options.style || 'warm'; // 默认使用温馨模板

        const template = await templateCache.getTemplate(templateType);
        if (template && template.content) {
          systemPrompt = template.content;

          // 检查是否包含【测试】标识
          if (template.content.includes('【测试】')) {

          }
        } else {

        }
      } catch (error) {
        console.error('[AI服务] 模板获取异常，使用硬编码模板:', error);
      }

      const requestData = {
        model: options.model || this.config.model,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: options.temperature || this.config.temperature,
        max_tokens: options.maxTokens || this.config.maxTokens,
        stream: false
      };

      // 发送请求到豆包API
      const response = await this.makeAPIRequest('/chat/completions', requestData);
      
      if (response.choices && response.choices.length > 0) {
        const content = response.choices[0].message.content;
        return {
          success: true,
          content: content.trim(),
          usage: response.usage
        };
      } else {
        throw new Error('AI响应格式错误');
      }

    } catch (error) {
      console.error('AI生成评语失败:', error);
      return {
        success: false,
        error: error.message || '生成失败'
      };
    }
  }

  /**
   * 发送API请求
   */
  async makeAPIRequest(endpoint, data) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.config.baseURL}${endpoint}`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        data: data,
        timeout: 30000,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`API请求失败: ${res.statusCode} ${res.data?.error?.message || ''}`));
          }
        },
        fail: (error) => {
          reject(new Error(`网络请求失败: ${error.errMsg}`));
        }
      });
    });
  }

  /**
   * 测试API连接
   */
  async testConnection() {
    try {
      const result = await this.generateComment('请回复"连接测试成功"', {
        maxTokens: 50
      });
      
      return {
        success: result.success,
        message: result.success ? '连接测试成功' : result.error
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '连接测试失败'
      };
    }
  }

  /**
   * 批量生成评语
   */
  async batchGenerateComments(prompts, options = {}) {
    const results = [];
    const batchSize = options.batchSize || 3; // 控制并发数
    
    for (let i = 0; i < prompts.length; i += batchSize) {
      const batch = prompts.slice(i, i + batchSize);
      const batchPromises = batch.map(prompt => this.generateComment(prompt, options));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // 批次间延迟，避免API限流
        if (i + batchSize < prompts.length) {
          await this.delay(1000);
        }
      } catch (error) {
        console.error(`批次 ${Math.floor(i / batchSize) + 1} 生成失败:`, error);
        // 为失败的批次添加错误结果
        batch.forEach(() => {
          results.push({
            success: false,
            error: '批量生成失败'
          });
        });
      }
    }
    
    return results;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 构建评语生成提示词
   */
  buildPrompt(studentData, recordsData, options = {}) {
    let prompt = `请为学生生成一份个性化评语。\n\n`;
    
    // 学生信息
    prompt += `学生信息：\n`;
    prompt += `姓名：${studentData.name}\n`;
    if (studentData.className) {
      prompt += `班级：${studentData.className}\n`;
    }
    
    // 时间范围
    if (options.startDate && options.endDate) {
      const startDate = new Date(options.startDate).toLocaleDateString();
      const endDate = new Date(options.endDate).toLocaleDateString();
      prompt += `评价时间段：${startDate} 至 ${endDate}\n`;
    }
    
    // 行为记录
    if (recordsData && recordsData.length > 0) {
      prompt += `\n行为记录：\n`;
      recordsData.forEach((record, index) => {
        const date = new Date(record.createTime).toLocaleDateString();
        prompt += `${index + 1}. [${date}] ${record.content}`;
        if (record.type) {
          prompt += ` (${record.type === 'positive' ? '积极表现' : record.type === 'negative' ? '需要改进' : '一般表现'})`;
        }
        prompt += `\n`;
      });
    } else {
      prompt += `\n暂无具体行为记录，请生成一份通用的鼓励性评语。\n`;
    }
    
    // 评语要求
    prompt += `\n评语要求：\n`;
    prompt += `- 风格：${this.getStyleDescription(options.style || 'warm')}\n`;
    prompt += `- 长度：${this.getLengthDescription(options.length || 'medium')}\n`;
    
    if (options.focus && options.focus.length > 0) {
      prompt += `- 重点关注：${options.focus.join('、')}\n`;
    }
    
    if (options.includeAdvice) {
      prompt += `- 包含具体的学习建议\n`;
    }
    
    if (options.includeEncouragement) {
      prompt += `- 包含鼓励和期望\n`;
    }
    
    if (options.customRequirement) {
      prompt += `- 特殊要求：${options.customRequirement}\n`;
    }
    
    prompt += `\n请生成一份温暖、具体、有针对性的学生评语。`;
    
    return prompt;
  }

  /**
   * 获取风格描述
   */
  getStyleDescription(style) {
    const styleMap = {
      'formal': '正式严谨，客观专业',
      'warm': '温暖亲切，充满关爱',
      'encouraging': '积极向上，充满鼓励',
      'detailed': '详细具体，深入分析'
    };
    return styleMap[style] || '温暖亲切';
  }

  /**
   * 获取长度描述
   */
  getLengthDescription(length) {
    const lengthMap = {
      'short': '简洁明了（50-80字）',
      'medium': '适中详细（100-150字）',
      'long': '详细全面（200-300字）'
    };
    return lengthMap[length] || '适中详细（100-150字）';
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * 检查配置是否完整
   */
  isConfigured() {
    return !!(this.config.apiKey && this.config.baseURL && this.config.model);
  }
}

// 创建全局实例
const aiService = new AIService();

module.exports = aiService;
