const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const $ = db.command.aggregate

/**
 * 获取系统使用统计数据
 * 包括教师使用数据、评语生成统计、AI Tokens消耗等
 */
exports.main = async (event, context) => {
  try {
    const { action } = event
    const now = Date.now()
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayStart = today.getTime()

    switch (action) {
      case 'getStats':
        return await getOverallStats(todayStart, now)
      
      case 'getTeacherUsage':
        return await getTeacherUsageStats()
      
      case 'getAIUsage':
        return await getAIUsageStats(todayStart, now)
      
      default:
        throw new Error('未知的操作类型')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    return {
      success: false,
      error: error.message,
      data: null
    }
  }
}

/**
 * 获取总体统计数据
 */
async function getOverallStats(todayStart, now) {
  try {
    // 获取用户统计
    const usersResult = await db.collection('users').count()
    const totalUsers = usersResult.total

    // 获取今日活跃用户 - 从comments表统计
    const activeUsersResult = await db.collection('comments')
      .where({
        createTime: db.command.gte(todayStart)
      })
      .field({ openid: true })
      .get()
    
    const activeUserIds = [...new Set(activeUsersResult.data.map(item => item.openid))]
    const activeUsers = activeUserIds.length

    // 获取今日评语生成数
    const todayCommentsResult = await db.collection('comments')
      .where({
        createTime: db.command.gte(todayStart)
      })
      .count()
    
    const todayComments = todayCommentsResult.total

    // 获取总评语数
    const totalCommentsResult = await db.collection('comments').count()
    const totalComments = totalCommentsResult.total

    // 获取AI调用统计 - 从comments表统计
    const aiCallsResult = await db.collection('comments')
      .where({
        createTime: db.command.gte(todayStart)
      })
      .count()
    
    const todayAICalls = aiCallsResult.total

    // 获取总Tokens消耗 - 从comments表统计
    const tokensResult = await db.collection('comments')
      .aggregate()
      .group({
        _id: null,
        totalTokens: $.sum('$tokensUsed')
      })
      .end()
    
    const totalTokens = tokensResult.list[0]?.totalTokens || 0

    // 🔥 获取真实学生总数 - 实际录入的每个学生
    // 方法一：直接统计students集合
    let totalStudents = 0
    try {
      const studentsResult = await db.collection('students').count()
      totalStudents = studentsResult.total
    } catch (studentError) {
      console.warn('students集合不存在，从评语中提取学生数')
      // 方法二：从评语中提取去重的学生
      const uniqueStudentsResult = await db.collection('comments')
        .aggregate()
        .group({
          _id: '$studentName'
        })
        .count('total')
        .end()
      totalStudents = uniqueStudentsResult.list[0]?.total || 0
    }

    return {
      success: true,
      data: {
        totalUsers,          // 真实教师数
        activeUsers,         // 今日活跃教师
        todayComments,       // 今日真实评语数
        totalComments,       // 历史总评语数
        todayAICalls,        // 今日真实AI调用
        totalTokens,         // 历史总Tokens
        totalStudents,       // 🔥 真实录入学生总数
        avgTokensPerCall: todayAICalls > 0 ? Math.round(totalTokens / todayAICalls) : 0,
        lastUpdated: now
      }
    }
  } catch (error) {
    console.error('获取总体统计失败:', error)
    throw error
  }
}

/**
 * 获取教师使用统计
 */
async function getTeacherUsageStats() {
  try {
    console.log('开始获取教师使用统计...')

    // 获取所有用户的活动统计 - 从comments表统计，使用正确的字段名
    const usageStats = await db.collection('comments')
      .aggregate()
      .group({
        _id: '$teacherId',  // 使用正确的字段名
        usageCount: $.sum(1),
        lastActivity: $.max('$createTime'),
        firstActivity: $.min('$createTime'),
        // 基于评语内容长度估算tokens消耗
        totalTokens: $.sum({
          $multiply: [{ $strLenCP: '$content' }, 1.5]
        })
      })
      .lookup({
        from: 'users',
        localField: '_id',
        foreignField: '_id',  // 使用正确的字段名
        as: 'userInfo'
      })
      .addFields({
        avgTokensPerUse: {
          $cond: {
            if: { $gt: ['$usageCount', 0] },
            then: { $divide: ['$totalTokens', '$usageCount'] },
            else: 0
          }
        }
      })
      .sort({
        usageCount: -1
      })
      .limit(50)
      .end()

    console.log('聚合查询结果:', usageStats)

    const teacherStats = usageStats.list.map(stat => ({
      id: stat._id,
      openid: stat._id,
      wechatName: stat.userInfo[0]?.nickName || stat.userInfo[0]?.nickname || '微信用户',
      realName: stat.userInfo[0]?.nickName || '教师',
      usageCount: stat.usageCount,
      totalTokens: Math.round(stat.totalTokens || 0),
      avgTokensPerUse: Math.round(stat.avgTokensPerUse || 0),
      lastActivity: stat.lastActivity,
      firstActivity: stat.firstActivity,
      status: (Date.now() - new Date(stat.lastActivity).getTime()) < 24 * 60 * 60 * 1000 ? 'active' : 'inactive'
    }))

    console.log('处理后的教师统计数据:', teacherStats)

    return {
      success: true,
      data: teacherStats
    }
  } catch (error) {
    console.error('获取教师使用统计失败:', error)
    return {
      success: false,
      error: error.message,
      data: []
    }
  }
}

/**
 * 获取AI使用统计
 */
async function getAIUsageStats(todayStart, now) {
  try {
    // 获取今日AI使用统计 - 从comments表统计
    const todayStats = await db.collection('comments')
      .where({
        createTime: db.command.gte(todayStart)
      })
      .aggregate()
      .group({
        _id: {
          hour: { $hour: { $toDate: '$createTime' } }
        },
        calls: $.sum(1),
        tokens: $.sum('$tokensUsed'),
        avgResponseTime: $.avg('$responseTime')
      })
      .sort({
        '_id.hour': 1
      })
      .end()

    // 获取最近7天的统计
    const weekAgo = now - 7 * 24 * 60 * 60 * 1000
    const weekStats = await db.collection('comments')
      .where({
        createTime: db.command.gte(weekAgo)
      })
      .aggregate()
      .group({
        _id: {
          day: { $dayOfYear: { $toDate: '$createTime' } }
        },
        calls: $.sum(1),
        tokens: $.sum('$tokensUsed')
      })
      .sort({
        '_id.day': 1
      })
      .end()

    // 获取模板使用统计
    const templateStats = await db.collection('comments')
      .aggregate()
      .group({
        _id: '$templateType',
        usage: $.sum(1),
        avgTokens: $.avg('$tokensUsed')
      })
      .sort({
        usage: -1
      })
      .end()

    return {
      success: true,
      data: {
        todayHourly: todayStats.list,
        weeklyTrend: weekStats.list,
        templateUsage: templateStats.list
      }
    }
  } catch (error) {
    console.error('获取AI使用统计失败:', error)
    throw error
  }
}