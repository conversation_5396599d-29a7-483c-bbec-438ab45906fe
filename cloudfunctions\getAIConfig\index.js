/**
 * 获取AI配置云函数
 * 统一从管理后台的ai_configs集合获取当前激活的AI模型配置
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();

  try {
    // 获取当前激活的AI模型配置
    const configResult = await db.collection('ai_configs')
      .where({
        status: 'active'
      })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get();

    if (configResult.data && configResult.data.length > 0) {
      const dbConfig = configResult.data[0];
      
      return {
        success: true,
        data: {
          id: dbConfig._id,
          name: dbConfig.name,
          provider: dbConfig.provider,
          model: dbConfig.model,
          config: {
            apiKey: dbConfig.config?.apiKey,
            baseURL: dbConfig.config?.baseURL || 'https://ark.cn-beijing.volces.com/api/v3',
            maxTokens: dbConfig.config?.maxTokens || 2000,
            temperature: dbConfig.config?.temperature || 0.7
          },
          usage: dbConfig.usage || 0,
          totalCost: dbConfig.totalCost || 0,
          inputPrice: dbConfig.inputPrice || 0,
          outputPrice: dbConfig.outputPrice || 0,
          createTime: dbConfig.createTime,
          updateTime: dbConfig.updateTime
        },
        openid: wxContext.OPENID
      };
    } else {
      return {
        success: false,
        error: '未找到激活的AI模型配置',
        message: '请在管理后台配置并激活AI模型',
        openid: wxContext.OPENID
      };
    }

  } catch (error) {
    console.error('获取AI配置失败:', error);
    return {
      success: false,
      error: error.message,
      openid: wxContext.OPENID
    };
  }
};