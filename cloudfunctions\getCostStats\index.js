/**
 * 获取AI费用统计云函数
 * 提供各种维度的费用统计数据
 */

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { action } = event
  
  try {
    switch (action) {
      case 'getTotalCost':
        return await getTotalCost()
      case 'getDailyCost':
        return await getDailyCost()
      case 'getTeacherCostStats':
        return await getTeacherCostStats()
      case 'getModelCostStats':
        return await getModelCostStats()
      case 'getCostTrend':
        return await getCostTrend(event.days || 7)
      default:
        return await getAllCostStats()
    }
  } catch (error) {
    console.error('获取费用统计失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取总费用统计
 */
async function getTotalCost() {
  const result = await db.collection('ai_usage')
    .aggregate()
    .group({
      _id: null,
      totalCost: _.sum('$cost'),
      totalInputCost: _.sum('$inputCost'),
      totalOutputCost: _.sum('$outputCost'),
      totalTokens: _.sum('$tokensUsed'),
      totalInputTokens: _.sum('$inputTokens'),
      totalOutputTokens: _.sum('$outputTokens'),
      totalCalls: _.sum(1)
    })
    .end()

  const data = result.list[0] || {
    totalCost: 0,
    totalInputCost: 0,
    totalOutputCost: 0,
    totalTokens: 0,
    totalInputTokens: 0,
    totalOutputTokens: 0,
    totalCalls: 0
  }

  return {
    success: true,
    data: {
      ...data,
      avgCostPerCall: data.totalCalls > 0 ? data.totalCost / data.totalCalls : 0,
      avgCostPerToken: data.totalTokens > 0 ? data.totalCost / data.totalTokens * 1000 : 0 // 每千tokens成本
    }
  }
}

/**
 * 获取今日费用统计
 */
async function getDailyCost() {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  const result = await db.collection('ai_usage')
    .where({
      createTime: _.gte(today)
    })
    .aggregate()
    .group({
      _id: null,
      todayCost: _.sum('$cost'),
      todayInputCost: _.sum('$inputCost'),
      todayOutputCost: _.sum('$outputCost'),
      todayTokens: _.sum('$tokensUsed'),
      todayCalls: _.sum(1)
    })
    .end()

  const data = result.list[0] || {
    todayCost: 0,
    todayInputCost: 0,
    todayOutputCost: 0,
    todayTokens: 0,
    todayCalls: 0
  }

  return {
    success: true,
    data
  }
}

/**
 * 获取教师费用统计
 */
async function getTeacherCostStats() {
  const result = await db.collection('ai_usage')
    .aggregate()
    .group({
      _id: '$teacherId',
      totalCost: _.sum('$cost'),
      totalTokens: _.sum('$tokensUsed'),
      totalCalls: _.sum(1),
      avgCostPerCall: _.avg('$cost'),
      lastUsed: _.max('$createTime')
    })
    .lookup({
      from: 'users',
      localField: '_id',
      foreignField: '_id',
      as: 'userInfo'
    })
    .addFields({
      teacherName: {
        $cond: {
          if: { $gt: [{ $size: '$userInfo' }, 0] },
          then: { $arrayElemAt: ['$userInfo.nickName', 0] },
          else: '未知教师'
        }
      }
    })
    .sort({
      totalCost: -1
    })
    .limit(50)
    .end()

  return {
    success: true,
    data: result.list || []
  }
}

/**
 * 获取模型费用统计
 */
async function getModelCostStats() {
  const result = await db.collection('ai_usage')
    .aggregate()
    .group({
      _id: '$aiModel',
      totalCost: _.sum('$cost'),
      totalTokens: _.sum('$tokensUsed'),
      totalCalls: _.sum(1),
      avgInputPrice: _.avg('$modelInputPrice'),
      avgOutputPrice: _.avg('$modelOutputPrice'),
      avgCostPerCall: _.avg('$cost')
    })
    .sort({
      totalCost: -1
    })
    .end()

  return {
    success: true,
    data: result.list || []
  }
}

/**
 * 获取费用趋势
 */
async function getCostTrend(days = 7) {
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)
  startDate.setHours(0, 0, 0, 0)

  const result = await db.collection('ai_usage')
    .where({
      createTime: _.gte(startDate)
    })
    .aggregate()
    .group({
      _id: {
        year: _.year('$createTime'),
        month: _.month('$createTime'),
        day: _.dayOfMonth('$createTime')
      },
      dailyCost: _.sum('$cost'),
      dailyTokens: _.sum('$tokensUsed'),
      dailyCalls: _.sum(1)
    })
    .sort({
      '_id.year': 1,
      '_id.month': 1,
      '_id.day': 1
    })
    .end()

  return {
    success: true,
    data: result.list || []
  }
}

/**
 * 获取综合费用统计
 */
async function getAllCostStats() {
  const [totalStats, dailyStats, teacherStats, modelStats, trendStats] = await Promise.all([
    getTotalCost(),
    getDailyCost(),
    getTeacherCostStats(),
    getModelCostStats(),
    getCostTrend(7)
  ])

  return {
    success: true,
    data: {
      total: totalStats.data,
      daily: dailyStats.data,
      teachers: teacherStats.data,
      models: modelStats.data,
      trend: trendStats.data
    }
  }
}
