/**
 * 前端费用计算工具
 * 用于在云函数费用数据不完整时进行实时计算
 */

// 模型定价配置（元/千tokens）
const MODEL_PRICING = {
  'doubao-pro-4k': { inputPrice: 0.00075, outputPrice: 0.0030 },
  'doubao-pro-32k': { inputPrice: 0.001, outputPrice: 0.002 },
  'doubao-seed-1-6-flash-250715': { inputPrice: 0.00007, outputPrice: 0.0003 },
  'default': { inputPrice: 0.001, outputPrice: 0.002 }
}

export interface CostCalculationResult {
  inputCost: number
  outputCost: number
  totalCost: number
  inputTokens: number
  outputTokens: number
}

export interface TokensUsageData {
  tokensUsed?: number
  inputTokens?: number
  outputTokens?: number
  aiModel?: string
  content?: string
}

export class CostCalculator {
  /**
   * 获取模型定价
   */
  static getModelPricing(model: string = 'default') {
    return MODEL_PRICING[model] || MODEL_PRICING['default']
  }

  /**
   * 估算输入输出tokens（如果没有分别记录）
   */
  static estimateTokens(totalTokens: number): { inputTokens: number; outputTokens: number } {
    // 假设输入输出比例为3:1
    const inputTokens = Math.ceil(totalTokens * 0.75)
    const outputTokens = Math.ceil(totalTokens * 0.25)
    return { inputTokens, outputTokens }
  }

  /**
   * 基于内容长度估算tokens
   */
  static estimateTokensFromContent(content: string): number {
    // 中文字符大约1.5个token，英文单词大约1个token
    const chineseChars = (content.match(/[\u4e00-\u9fa5]/g) || []).length
    const englishWords = (content.match(/[a-zA-Z]+/g) || []).length
    const otherChars = content.length - chineseChars - englishWords
    
    return Math.ceil(chineseChars * 1.5 + englishWords * 1 + otherChars * 0.5)
  }

  /**
   * 计算单次调用费用
   */
  static calculateSingleCost(data: TokensUsageData): CostCalculationResult {
    const model = data.aiModel || 'default'
    const pricing = this.getModelPricing(model)

    let inputTokens = data.inputTokens || 0
    let outputTokens = data.outputTokens || 0

    // 如果没有分别记录输入输出tokens
    if ((!inputTokens || !outputTokens) && data.tokensUsed) {
      const estimated = this.estimateTokens(data.tokensUsed)
      inputTokens = inputTokens || estimated.inputTokens
      outputTokens = outputTokens || estimated.outputTokens
    }

    // 如果还是没有tokens数据，尝试从内容估算
    if ((!inputTokens || !outputTokens) && data.content) {
      const estimatedTotal = this.estimateTokensFromContent(data.content)
      const estimated = this.estimateTokens(estimatedTotal)
      inputTokens = inputTokens || estimated.inputTokens
      outputTokens = outputTokens || estimated.outputTokens
    }

    // 计算费用（定价是按千tokens计算）
    const inputCost = (inputTokens / 1000) * pricing.inputPrice
    const outputCost = (outputTokens / 1000) * pricing.outputPrice
    const totalCost = inputCost + outputCost

    return {
      inputCost: Math.round(inputCost * 100000) / 100000,
      outputCost: Math.round(outputCost * 100000) / 100000,
      totalCost: Math.round(totalCost * 100000) / 100000,
      inputTokens,
      outputTokens
    }
  }

  /**
   * 批量计算费用
   */
  static calculateBatchCost(dataList: TokensUsageData[]): {
    totalCost: number
    totalInputCost: number
    totalOutputCost: number
    totalInputTokens: number
    totalOutputTokens: number
    totalTokens: number
    totalCalls: number
    avgCostPerCall: number
    avgCostPerToken: number
    details: CostCalculationResult[]
  } {
    const details = dataList.map(data => this.calculateSingleCost(data))
    
    const totalInputCost = details.reduce((sum, item) => sum + item.inputCost, 0)
    const totalOutputCost = details.reduce((sum, item) => sum + item.outputCost, 0)
    const totalCost = totalInputCost + totalOutputCost
    const totalInputTokens = details.reduce((sum, item) => sum + item.inputTokens, 0)
    const totalOutputTokens = details.reduce((sum, item) => sum + item.outputTokens, 0)
    const totalTokens = totalInputTokens + totalOutputTokens
    const totalCalls = dataList.length

    return {
      totalCost: Math.round(totalCost * 100000) / 100000,
      totalInputCost: Math.round(totalInputCost * 100000) / 100000,
      totalOutputCost: Math.round(totalOutputCost * 100000) / 100000,
      totalInputTokens,
      totalOutputTokens,
      totalTokens,
      totalCalls,
      avgCostPerCall: totalCalls > 0 ? Math.round((totalCost / totalCalls) * 1000000) / 1000000 : 0,
      avgCostPerToken: totalTokens > 0 ? Math.round((totalCost / totalTokens * 1000) * 1000000) / 1000000 : 0,
      details
    }
  }

  /**
   * 按教师分组计算费用
   */
  static calculateCostByTeacher(dataList: (TokensUsageData & { teacherId: string; teacherName?: string })[]): Array<{
    teacherId: string
    teacherName: string
    totalCost: number
    totalCalls: number
    totalTokens: number
    avgCostPerCall: number
    lastUsed?: string
  }> {
    const teacherMap = new Map()

    dataList.forEach(data => {
      const teacherId = data.teacherId
      const cost = this.calculateSingleCost(data)

      if (!teacherMap.has(teacherId)) {
        teacherMap.set(teacherId, {
          teacherId,
          teacherName: data.teacherName || '未知教师',
          totalCost: 0,
          totalCalls: 0,
          totalTokens: 0,
          costs: []
        })
      }

      const teacher = teacherMap.get(teacherId)
      teacher.totalCost += cost.totalCost
      teacher.totalCalls += 1
      teacher.totalTokens += cost.inputTokens + cost.outputTokens
      teacher.costs.push(cost)
    })

    return Array.from(teacherMap.values()).map(teacher => ({
      teacherId: teacher.teacherId,
      teacherName: teacher.teacherName,
      totalCost: Math.round(teacher.totalCost * 100000) / 100000,
      totalCalls: teacher.totalCalls,
      totalTokens: teacher.totalTokens,
      avgCostPerCall: teacher.totalCalls > 0 ? Math.round((teacher.totalCost / teacher.totalCalls) * 1000000) / 1000000 : 0
    })).sort((a, b) => b.totalCost - a.totalCost)
  }

  /**
   * 计算今日费用
   */
  static calculateTodayCost(dataList: (TokensUsageData & { createTime: number | string | Date })[]): {
    todayCost: number
    todayCalls: number
    todayTokens: number
  } {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayStart = today.getTime()

    const todayData = dataList.filter(data => {
      const createTime = new Date(data.createTime).getTime()
      return createTime >= todayStart
    })

    const result = this.calculateBatchCost(todayData)
    
    return {
      todayCost: result.totalCost,
      todayCalls: result.totalCalls,
      todayTokens: result.totalTokens
    }
  }
}

export default CostCalculator
