/**
 * 性能诊断工具
 * 帮助快速定位卡顿问题
 */

// 性能监控数据收集
export class PerformanceDiagnostic {
  private metrics: Map<string, number[]> = new Map()
  private observer: PerformanceObserver | null = null

  constructor() {
    this.initPerformanceObserver()
    this.monitorFrameRate()
    this.monitorMemoryUsage()
  }

  // 初始化性能观察器
  private initPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric(entry.name, entry.duration)
        }
      })
      
      this.observer.observe({ 
        entryTypes: ['measure', 'navigation', 'paint', 'largest-contentful-paint'] 
      })
    }
  }

  // 监控帧率
  private monitorFrameRate() {
    let lastTime = performance.now()
    let frames = 0
    
    const checkFrameRate = () => {
      frames++
      const currentTime = performance.now()
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frames * 1000) / (currentTime - lastTime))
        this.recordMetric('fps', fps)
        
        if (fps < 30) {
          console.warn(`🐌 低帧率警告: ${fps}fps`)
          this.diagnoseFrameDrops()
        }
        
        frames = 0
        lastTime = currentTime
      }
      
      requestAnimationFrame(checkFrameRate)
    }
    
    requestAnimationFrame(checkFrameRate)
  }

  // 监控内存使用
  private monitorMemoryUsage() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory
        const usedMB = Math.round(memory.usedJSHeapSize / 1048576)
        this.recordMetric('memory', usedMB)
        
        if (usedMB > 100) {
          console.warn(`🐌 内存使用过高: ${usedMB}MB`)
        }
      }, 5000)
    }
  }

  // 记录指标
  private recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    
    const values = this.metrics.get(name)!
    values.push(value)
    
    // 只保留最近50个数据点
    if (values.length > 50) {
      values.shift()
    }
  }

  // 诊断掉帧原因
  private diagnoseFrameDrops() {
    const longTasks = performance.getEntriesByType('longtask') || []
    if (longTasks.length > 0) {
      console.warn('🐌 检测到长任务:', longTasks)
    }
    
    // 检查DOM节点数量
    const domNodes = document.querySelectorAll('*').length
    if (domNodes > 3000) {
      console.warn(`🐌 DOM节点过多: ${domNodes}个`)
    }
    
    // 检查CSS复杂度
    const stylesheets = document.styleSheets.length
    if (stylesheets > 10) {
      console.warn(`🐌 样式表过多: ${stylesheets}个`)
    }
  }

  // 获取性能报告
  public getPerformanceReport() {
    const report: any = {}
    
    for (const [metric, values] of this.metrics) {
      if (values.length > 0) {
        const avg = values.reduce((a, b) => a + b, 0) / values.length
        const min = Math.min(...values)
        const max = Math.max(...values)
        
        report[metric] = {
          average: Math.round(avg * 100) / 100,
          min,
          max,
          latest: values[values.length - 1]
        }
      }
    }
    
    return report
  }

  // 快速诊断当前页面性能问题
  public quickDiagnose() {
    const issues: string[] = []
    const suggestions: string[] = []
    
    // 检查加载时间
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      const loadTime = navigation.loadEventEnd - navigation.fetchStart
      if (loadTime > 3000) {
        issues.push(`页面加载时间过长: ${Math.round(loadTime)}ms`)
        suggestions.push('考虑代码分割和懒加载')
      }
    }

    // 检查资源数量
    const resources = performance.getEntriesByType('resource')
    if (resources.length > 50) {
      issues.push(`HTTP请求过多: ${resources.length}个`)
      suggestions.push('合并资源文件，启用HTTP/2')
    }

    // 检查大型资源
    const largeResources = resources.filter(r => r.transferSize > 1024 * 1024) // >1MB
    if (largeResources.length > 0) {
      issues.push(`发现${largeResources.length}个大型资源文件`)
      suggestions.push('压缩图片和字体文件')
    }

    // 检查重绘重排
    const paintEntries = performance.getEntriesByType('paint')
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    if (fcp && fcp.startTime > 2000) {
      issues.push(`首次内容绘制延迟: ${Math.round(fcp.startTime)}ms`)
      suggestions.push('优化关键渲染路径')
    }

    return { issues, suggestions }
  }

  // 销毁监控器
  public destroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }
}

// 网络性能检测
export const checkNetworkPerformance = async () => {
  const startTime = performance.now()
  
  try {

    await fetch('/favicon.ico?t=' + Date.now(), { 
      method: 'HEAD',
      cache: 'no-cache'
    })
    
    const endTime = performance.now()
    const latency = endTime - startTime
    
    if (latency > 1000) {
      console.warn(`🐌 网络延迟过高: ${Math.round(latency)}ms`)
      return 'slow'
    } else if (latency > 300) {
      return 'medium'
    } else {
      return 'fast'
    }
  } catch (error) {
    console.error('网络性能检测失败:', error)
    return 'unknown'
  }
}

// 检测设备性能等级
export const detectDevicePerformance = () => {
  const canvas = document.createElement('canvas')
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
  
  let deviceLevel = 'low'
  
  // 检查硬件加速
  if (gl) {
    const renderer = gl.getParameter(gl.RENDERER) || ''
    if (renderer.includes('NVIDIA') || renderer.includes('AMD') || renderer.includes('Intel Iris')) {
      deviceLevel = 'high'
    } else if (renderer.includes('Intel')) {
      deviceLevel = 'medium'
    }
  }
  
  // 检查内存
  if ('memory' in performance) {
    const memory = (performance as any).memory
    const totalMB = memory.jsHeapSizeLimit / 1048576
    if (totalMB > 1000) {
      deviceLevel = 'high'
    } else if (totalMB > 500) {
      deviceLevel = deviceLevel === 'low' ? 'medium' : deviceLevel
    }
  }
  
  // 检查CPU核心数
  if ('hardwareConcurrency' in navigator) {
    const cores = navigator.hardwareConcurrency
    if (cores > 4) {
      deviceLevel = deviceLevel === 'low' ? 'medium' : 'high'
    }
  }
  
  return deviceLevel
}

// 自动性能优化建议
export const getOptimizationSuggestions = () => {
  const deviceLevel = detectDevicePerformance()
  const suggestions: string[] = []
  
  switch (deviceLevel) {
    case 'low':
      suggestions.push('禁用动画效果')
      suggestions.push('减少同屏显示的数据量')
      suggestions.push('使用虚拟滚动')
      suggestions.push('延迟加载非关键内容')
      break
      
    case 'medium':
      suggestions.push('减少重绘重排')
      suggestions.push('优化图片尺寸')
      suggestions.push('使用CSS硬件加速')
      break
      
    case 'high':
      suggestions.push('可以启用高级动画效果')
      suggestions.push('增加预加载策略')
      suggestions.push('使用Service Worker缓存')
      break
  }
  
  return { deviceLevel, suggestions }
}

export default PerformanceDiagnostic