# 评语灵感君

<div align="center">

![Logo](admin/public/favicon.svg)

**AI智能评语生成系统 - 小程序 + 管理后台**

[![微信小程序](https://img.shields.io/badge/微信小程序-支持-green.svg)](https://developers.weixin.qq.com/miniprogram/dev/framework/)
[![React](https://img.shields.io/badge/React-18.x-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue.svg)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-5.x-646CFF.svg)](https://vitejs.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.x-38B2AC.svg)](https://tailwindcss.com/)
[![Ant Design](https://img.shields.io/badge/Ant%20Design-5.x-1890FF.svg)](https://ant.design/)

[小程序体验码](#小程序体验) | [管理后台演示](http://localhost:8083) | [技术文档](./TECHNICAL_GUIDE.md)

</div>

## 📱 项目结构

本项目包含两个子项目：

### 🎯 微信小程序端
- **位置**: `/miniprogram/`
- **功能**: AI智能评语生成，学生信息管理
- **技术栈**: 微信小程序原生开发 + 云开发

### 🖥️ 管理后台端
- **位置**: `/admin/`
- **功能**: 系统管理，数据统计，AI配置
- **技术栈**: React 18 + TypeScript + Vite + Ant Design

## ✨ 核心特性

### 小程序端特性
- 🤖 **AI智能生成** - 多模型支持，个性化评语
- 👥 **学生管理** - 完整的学生信息录入和管理
- 📊 **数据统计** - 使用情况和效果分析
- 🔄 **实时同步** - 云端数据实时同步

### 管理后台特性
- 🎨 **现代化设计** - 渐变色彩 + 玻璃拟态效果
- 🌙 **暗黑模式** - 完整的暗黑主题支持
- 📱 **响应式布局** - 完美适配桌面端和移动端
- 🚀 **实时数据** - WebSocket实时数据通信
- 🤖 **AI配置管理** - 多AI模型配置与参数调优
- 📊 **数据可视化** - 丰富的图表和统计展示
- 📁 **Excel支持** - 完整的导入导出功能
- ⚡ **高性能优化** - Vite构建 + 代码分割优化

## 🛠️ 技术栈

### 核心框架
- **React 18** - 现代化React开发
- **TypeScript** - 类型安全
- **Vite** - 极速构建工具

### UI框架
- **Ant Design 5.x** - 企业级UI组件
- **Tailwind CSS** - 原子化CSS框架
- **自定义组件** - 渐变卡片、统计组件等

### 状态管理
- **React Hooks** - 轻量级状态管理
- **Context API** - 全局状态共享

### 工具库
- **XLSX** - Excel文件处理
- **Day.js** - 日期时间处理
- **React Router** - 路由管理

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产构建
```bash
npm run preview
```

### 本地开发
```bash
npm run dev
```
访问 http://localhost:3000

### 代码检查
```bash
# 类型检查
npm run type-check

# 代码格式化
npm run format

# 代码质量检查
npm run lint
npm run lint:fix
```

### 构建部署
```bash
# 生产构建
npm run build

# 预览构建结果
npm run preview

# 部署到云开发
npm run deploy
```

## 🏗️ 项目结构

```
admin-new/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 公共组件
│   │   ├── ErrorBoundary.tsx
│   │   └── LoadingSpinner.tsx
│   ├── hooks/             # 自定义Hook
│   ├── layouts/           # 布局组件
│   ├── pages/             # 页面组件
│   │   ├── Login/         # 登录页面
│   │   ├── Dashboard/     # 数据大屏
│   │   ├── AIConfig/      # AI配置
│   │   ├── TemplateEditor/ # 模板编辑器
│   │   ├── UsageMonitor/  # 使用监控
│   │   └── Settings/      # 系统设置
│   ├── services/          # API服务
│   │   ├── apiClient.ts   # HTTP客户端
│   │   └── authApi.ts     # 认证服务
│   ├── stores/            # 状态管理
│   │   ├── authStore.ts   # 认证状态
│   │   └── themeStore.ts  # 主题状态
│   ├── styles/            # 样式文件
│   │   ├── design-system.css # 设计系统
│   │   └── components.css # 组件样式
│   ├── types/             # 类型定义
│   │   └── index.ts       # 全局类型
│   ├── utils/             # 工具函数
│   ├── App.tsx            # 主应用
│   └── main.tsx           # 应用入口
├── .env.example           # 环境变量模板
├── .eslintrc.json         # ESLint配置
├── .prettierrc            # Prettier配置
├── index.html             # HTML模板
├── package.json           # 项目配置
├── postcss.config.js      # PostCSS配置
├── tailwind.config.js     # Tailwind配置
├── tsconfig.json          # TypeScript配置
└── vite.config.ts         # Vite配置
```

## 🎨 设计系统

### 色彩系统
- **主色调**: 科技蓝 (#3b82f6)
- **功能色**: 成功绿、警告橙、错误红、信息蓝
- **中性色**: 9级灰度色阶
- **深色模式**: 完整的暗色主题支持

### 字体系统
- **标题字体**: SF Pro Display, PingFang SC
- **正文字体**: SF Pro Text, PingFang SC
- **等宽字体**: SF Mono, Monaco

### 空间系统
- **基础单位**: 4px
- **间距规范**: 4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px, 96px

### 组件规范
- **按钮**: 4种变体 (primary, secondary, ghost, danger)
- **表单**: 统一的输入框、选择器、复选框样式
- **卡片**: 多种卡片样式和交互状态
- **模态框**: 标准化的对话框组件

## 📱 响应式设计

### 断点系统
- **xs**: < 640px (手机竖屏)
- **sm**: 640px+ (手机横屏)
- **md**: 768px+ (平板竖屏)
- **lg**: 1024px+ (平板横屏/小笔记本)
- **xl**: 1280px+ (桌面端)
- **2xl**: 1536px+ (大屏桌面)

### 适配策略
- 移动优先的响应式设计
- 弹性网格系统
- 自适应图像和媒体
- 触摸友好的交互区域

## 🔧 功能特性

### 核心功能
- **数据大屏**: 实时监控和可视化图表
- **AI配置管理**: 多服务商支持和参数调优
- **模板编辑器**: 可视化模板编辑和变量系统
- **使用监控**: 详细的API调用统计和成本分析
- **系统设置**: 用户管理和系统配置

### 技术特性
- **类型安全**: 完整的TypeScript类型定义
- **状态管理**: Zustand + TanStack Query
- **错误边界**: 完善的错误处理机制
- **加载状态**: 多种加载状态组件
- **主题系统**: 支持亮色/暗色模式切换
- **国际化**: 预留多语言支持
- **PWA支持**: 渐进式Web应用特性

### 性能优化
- **代码分割**: 路由级和组件级懒加载
- **资源优化**: 自动压缩和缓存策略
- **请求优化**: 自动重试和并发控制
- **缓存管理**: 智能缓存和失效策略

## 🔒 安全特性

### 认证授权
- JWT Token认证
- 自动Token刷新
- 权限控制和路由守卫
- 会话超时管理

### 数据安全
- API请求加密
- 敏感信息脱敏
- XSS和CSRF防护
- 安全的本地存储

## 📊 监控与分析

### 性能监控
- Web Vitals指标收集
- 页面加载时间统计
- 资源加载分析
- 用户体验指标

### 错误监控
- 全局错误捕获
- 错误边界保护
- 错误信息上报
- 实时错误告警

## 🚀 部署指南

### 构建优化
```bash
# 生产环境构建
NODE_ENV=production npm run build

# 启用Gzip压缩
VITE_ENABLE_COMPRESSION=true npm run build

# Bundle分析
VITE_BUNDLE_ANALYZER=true npm run build
```

### 云开发部署
```bash
# 安装云开发CLI
npm install -g @cloudbase/cli

# 登录云开发
tcb login

# 部署到静态托管
npm run deploy
```

### Docker部署
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🤝 开发规范

### 代码规范
- 使用TypeScript严格模式
- 遵循ESLint和Prettier规则
- 组件命名采用PascalCase
- 文件命名采用camelCase

### Git规范
- 使用语义化提交信息
- 分支命名: feature/xxx, bugfix/xxx, hotfix/xxx
- 提交前自动代码检查
- PR模板和代码审查

### 测试规范
- 单元测试覆盖率 > 80%
- 集成测试覆盖核心流程
- E2E测试覆盖关键用户路径
- 性能测试和压力测试

## 📝 更新日志

### v2.0.0 (2025-01-27)
- 🎉 全新重构，采用现代化技术栈
- ✨ 实现完整的设计系统
- 🔒 增强安全机制和错误处理
- 📱 优化响应式设计和移动端体验
- 🚀 提升性能和开发体验
- 📊 完善监控和分析能力

## 🆘 故障排除

### 常见问题
1. **构建失败**: 检查Node.js版本和依赖安装
2. **类型错误**: 运行`npm run type-check`检查
3. **样式问题**: 确保Tailwind CSS配置正确
4. **API连接失败**: 检查环境变量配置

### 性能问题
1. **加载慢**: 检查网络连接和CDN配置
2. **内存泄漏**: 使用React DevTools分析
3. **打包过大**: 启用Bundle分析器检查

## 📞 技术支持

- 项目地址: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 技术文档: [项目Wiki]
- 联系方式: [技术支持邮箱]

## 🚀 快速开始

### 小程序端开发
```bash
# 进入小程序目录
cd miniprogram

# 使用微信开发者工具打开项目
# 配置云开发环境
# 上传并部署云函数
```

### 管理后台开发
```bash
# 进入管理后台目录
cd admin

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 📱 小程序体验

### 功能特点
- 🤖 **AI智能评语生成** - 多模型支持，个性化定制
- 👥 **学生信息管理** - 完整的学生档案系统
- 📝 **行为记录跟踪** - 详细的行为数据记录
- 📊 **数据统计分析** - 全面的使用情况分析
- 🔒 **隐私安全保护** - 严格的数据安全机制

### 使用流程
1. **微信扫码登录** - 快速安全登录
2. **添加学生信息** - 建立学生档案
3. **记录学生行为** - 实时记录表现
4. **生成AI评语** - 一键生成个性化评语

## 🛠️ 技术架构

### 小程序端技术栈
- **前端框架**: 微信小程序原生开发
- **后端服务**: 微信云开发 (CloudBase)
- **数据库**: 云数据库
- **AI服务**: 多模型API集成
- **存储**: 云存储

### 管理后台技术栈
- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite 5.x
- **UI框架**: Ant Design 5.x + Tailwind CSS
- **状态管理**: React Hooks + Context API
- **数据可视化**: ECharts + Recharts
- **工具库**: XLSX, Day.js, React Router

## 📞 技术支持

- **项目地址**: [GitHub Repository](https://github.com/chanwarmsun/wechat)
- **问题反馈**: [GitHub Issues](https://github.com/chanwarmsun/wechat/issues)
- **技术文档**: [项目Wiki](https://github.com/chanwarmsun/wechat/wiki)

---

© 2025 评语灵感君 - AI智能评语生成系统 | 让每一句评语都充满温度
