/* 记录管理页面样式 */
.record-list-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 头部操作区 */
.header-section {
  background: white;
  padding: 32rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 页面标题 */
.page-title {
  flex: 1;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  display: block;
  margin-bottom: 8rpx;
}

.title-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.2;
}

/* 头部操作按钮 */
.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e8e9ea;
  transition: all 0.3s ease;
  position: relative;
  min-width: 120rpx;
  justify-content: center;
}

.action-item:active {
  transform: translateY(1rpx);
  background: #f0f1f5;
}

.filter-action:active {
  border-color: #4080FF;
  background: rgba(64, 128, 255, 0.05);
}

.export-action:active {
  border-color: #52C873;
  background: rgba(82, 200, 115, 0.05);
}

.item-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
}

/* 筛选状态指示点 */
.filter-dot {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 12rpx;
  height: 12rpx;
  background: #FF5247;
  border-radius: 50%;
  border: 2rpx solid white;
}

.search-container {
  margin-bottom: 16rpx;
}

.search-input {
  background: #f8f9fa !important;
  border-radius: 20rpx !important;
}

.filter-container {
  display: flex;
  gap: 16rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
}

/* 统计信息 */
.stats-section {
  padding: 0 24rpx 24rpx;
}

.stats-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #4080FF;
  margin-bottom: 6rpx;
  line-height: 1.2;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  line-height: 1.2;
}

/* 记录列表 */
.record-list-container {
  padding: 0 24rpx;
}

.record-list {
  padding: 0;
}

/* 普通record-item样式，不在滑动容器中时使用 */
.record-item {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}



.record-item:active {
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.06);
}

/* 记录头部 */
.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.student-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.student-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4080FF, #6B9FFF);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.avatar-text {
  font-size: 22rpx;
  font-weight: 600;
  color: white;
}

.student-details {
  flex: 1;
}

.student-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.student-class {
  font-size: 24rpx;
  color: #666;
}

.record-score {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 600;
}

.record-score.positive {
  background: rgba(82, 200, 115, 0.1);
  color: #52C873;
}

.record-score.negative {
  background: rgba(255, 82, 71, 0.1);
  color: #FF5247;
}

.record-score.neutral {
  background: rgba(255, 170, 51, 0.1);
  color: #FFAA33;
}

.record-score.academic {
  background: rgba(139, 92, 246, 0.1);
  color: #8B5CF6;
}

.score-text {
  font-size: 26rpx;
}

/* 记录内容 */
.record-content {
  margin-bottom: 16rpx;
}

.behavior-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.behavior-type {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.behavior-type.positive { background: #52C873; }
.behavior-type.negative { background: #FF5247; }
.behavior-type.neutral { background: #FFAA33; }
.behavior-type.academic { background: #8B5CF6; }

.type-text {
  color: white;
  font-weight: 500;
}

.behavior-action {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.record-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 12rpx;
}

/* 记录底部 */
.record-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.record-time {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.time-text {
  font-size: 24rpx;
  color: #999;
}

.record-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 44rpx;
  height: 44rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1rpx solid transparent;
}

.edit-btn {
  background: rgba(64, 128, 255, 0.08);
  border-color: rgba(64, 128, 255, 0.15);
}

.edit-btn:active {
  background: rgba(64, 128, 255, 0.15);
  transform: scale(0.95);
}

.delete-btn {
  background: rgba(255, 82, 71, 0.08);
  border-color: rgba(255, 82, 71, 0.15);
}

.delete-btn:active {
  background: rgba(255, 82, 71, 0.15);
  transform: scale(0.95);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
  background: white;
  border-radius: 16rpx;
  margin: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
}

.empty-icon {
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
  line-height: 1.3;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 32rpx;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

.empty-action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 32rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.empty-action-btn.primary {
  background: linear-gradient(135deg, #4080FF, #6B9FFF);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(64, 128, 255, 0.3);
}

.empty-action-btn.primary:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(64, 128, 255, 0.4);
}

.action-text {
  font-size: 28rpx;
}

/* 悬浮操作按钮 */
.fab-container {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 100;
}

.fab-button {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4080FF, #6B9FFF);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(64, 128, 255, 0.4);
  transition: all 0.3s ease;
}

.fab-button:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 16rpx rgba(64, 128, 255, 0.5);
}

