// 用户微信信息云同步工具
const cloud = require('../../cloudService');

class UserSyncManager {
  constructor() {
    this.db = wx.cloud.database();
  }

  // 同步微信用户信息到云端
  async syncWxUserInfo() {
    try {
      // 获取微信用户信息
      const userProfile = await wx.getUserProfile({
        desc: '用于同步用户头像和昵称'
      });

      // 获取当前用户的OpenID
      const { data: { openid } } = await cloud.callFunction({
        name: 'getUserOpenId'
      });

      // 准备用户信息
      const userInfo = {
        nickName: userProfile.userInfo.nickName,
        avatarUrl: userProfile.userInfo.avatarUrl,
        gender: userProfile.userInfo.gender,
        city: userProfile.userInfo.city,
        province: userProfile.userInfo.province,
        country: userProfile.userInfo.country,
        wxSyncAt: new Date(),
        updatedAt: new Date()
      };

      // 同步到云端数据库
      await this.db.collection('users').doc(openid).update({
        data: userInfo
      });

      // 更新本地存储
      const currentUser = wx.getStorageSync('userInfo') || {};
      wx.setStorageSync('userInfo', { ...currentUser, ...userInfo });

      return {
        success: true,
        message: '用户信息同步成功',
        data: userInfo
      };
    } catch (error) {
      console.error('同步用户信息失败:', error);
      return {
        success: false,
        message: error.errMsg || '同步失败'
      };
    }
  }

  // 延迟同步机制（用户首次授权后执行）
  async delaySyncWxUserInfo() {
    setTimeout(async () => {
      await this.syncWxUserInfo();
    }, 2000);
  }

  // 强制同步（解决用户修改微信信息后不同步的问题）
  async forceSyncWxUserInfo() {
    try {
      const { data: { openid } } = await cloud.callFunction({
        name: 'getUserOpenId'
      });

      // 获取微信用户信息
      const userProfile = await wx.getUserProfile({
        desc: '更新用户头像和昵称'
      });

      const updatedInfo = {
        nickName: userProfile.userInfo.nickName,
        avatarUrl: userProfile.userInfo.avatarUrl,
        gender: userProfile.userInfo.gender,
        city: userProfile.userInfo.city,
        province: userProfile.userInfo.province,
        country: userProfile.userInfo.country,
        wxSyncAt: new Date(),
        updatedAt: new Date()
      };

      await this.db.collection('users').doc(openid).update({
        data: updatedInfo
      });

      return { success: true, message: '强制同步成功' };
    } catch (error) {
      console.error('强制同步失败:', error);
      return { success: false, message: error.errMsg || '同步失败' };
    }
  }
}

module.exports = new UserSyncManager();