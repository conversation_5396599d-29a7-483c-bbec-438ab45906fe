# 用户姓名显示修复方案

## 问题分析

### 原问题
- 管理后台显示的教师姓名不是用户在小程序中设置的真实姓名
- 只显示微信昵称或默认的"教师X"格式

### 根本原因
1. **数据存储分离**：用户在小程序设置中录入的真实姓名存储在 `user_profiles` 集合
2. **查询不匹配**：管理后台查询的是 `users` 集合，无法获取到真实姓名
3. **字段优先级**：没有正确的字段优先级逻辑（真实姓名 > 微信昵称）

## 修复方案

### 1. 修改云函数 `saveUserProfile`
**文件**：`cloudfunctions/saveUserProfile/index.js`

**修改内容**：
- 同时更新 `users` 集合和 `user_profiles` 集合
- 确保用户的真实姓名存储在 `users.name` 字段中
- 如果用户不存在，自动创建用户记录

**关键代码**：
```javascript
// 🔥 关键修复：同时更新users集合中的用户信息
const usersResult = await db.collection('users').doc(wxContext.OPENID).update({
  data: updateData
})
```

### 2. 修改云函数 `getUserProfile`
**文件**：`cloudfunctions/getUserProfile/index.js`

**修改内容**：
- 优先从 `users` 集合获取用户信息
- 如果 `users` 集合没有数据，再从 `user_profiles` 集合获取
- 确保返回完整的用户信息结构

**关键代码**：
```javascript
// 🔥 优先从users集合获取用户信息（包含真实姓名）
const usersResult = await db.collection('users').doc(wxContext.OPENID).get()
```

### 3. 修改管理后台查询逻辑
**文件**：`cloudfunctions/dataQuery/index.js`

**修改内容**：
- 在教师统计查询中，优先使用 `name` 字段（真实姓名）
- 设置正确的字段优先级：`name` > `nickName` > `nickname`

**关键代码**：
```javascript
// 优先使用真实姓名
wechatName: stat.userInfo[0]?.name || stat.userInfo[0]?.nickName || stat.userInfo[0]?.nickname || `微信用户${index + 1}`,
realName: stat.userInfo[0]?.name || stat.userInfo[0]?.nickName || `教师${index + 1}`,
```

## 数据流程

### 用户设置姓名流程
1. 用户在小程序设置页面输入真实姓名
2. 调用 `saveUserProfile` 云函数
3. 同时更新 `users` 集合和 `user_profiles` 集合
4. `users.name` 字段存储真实姓名

### 管理后台显示流程
1. 管理后台调用 `dataQuery` 云函数
2. 从 `comments` 集合按 `teacherId` 分组统计
3. 通过 `lookup` 关联 `users` 集合获取用户信息
4. 优先显示 `users.name`（真实姓名），其次显示 `users.nickName`（微信昵称）

## 字段优先级

### 显示优先级
1. **`users.name`** - 用户在小程序中设置的真实姓名（最高优先级）
2. **`users.nickName`** - 微信昵称
3. **`users.nickname`** - 兼容字段
4. **默认值** - "微信用户X" 或 "教师X"

### 数据库字段说明
- **`users` 集合**：主要用户信息存储，管理后台查询的数据源
  - `name`：用户设置的真实姓名
  - `nickName`：微信昵称
  - `avatarUrl`：头像URL
  - `school`、`subject`、`grade` 等：其他用户信息

- **`user_profiles` 集合**：用户资料备份，用于数据恢复
  - `userInfo`：完整的用户信息对象
  - `updateTime`：更新时间
  - `platform`：平台标识

## 测试验证

### 测试步骤
1. 用户在小程序设置中修改真实姓名
2. 检查 `users` 集合中的 `name` 字段是否更新
3. 在管理后台查看教师统计，确认显示真实姓名
4. 测试暗黑模式下的显示效果

### 预期结果
- 如果用户设置了真实姓名，管理后台显示真实姓名
- 如果用户没有设置真实姓名，显示微信昵称
- 暗黑模式下教师姓名显示为白色

## 兼容性说明

### 向后兼容
- 现有的 `user_profiles` 集合数据保持不变
- 新的修改会同时更新两个集合
- 查询逻辑有降级机制，确保数据可用性

### 数据迁移
- 不需要手动数据迁移
- 用户下次修改信息时会自动同步到 `users` 集合
- 可以通过批量脚本迁移现有数据（可选）

## 注意事项

1. **云函数部署**：修改后需要重新部署相关云函数
2. **权限检查**：确保云函数有 `users` 集合的读写权限
3. **错误处理**：增加了完善的错误处理和降级机制
4. **日志记录**：添加了详细的日志记录便于调试
