# 用户姓名显示修复方案

## 问题分析

### 原问题
- 管理后台显示的教师姓名不是用户在小程序中设置的真实姓名
- 只显示微信昵称或默认的"教师X"格式

### 根本原因
1. **字段缺失**：`users` 集合中没有 `name` 字段存储真实姓名
2. **数据存储分离**：用户在小程序设置中录入的真实姓名只存储在 `user_profiles` 集合
3. **查询不匹配**：管理后台查询的是 `users` 集合，无法获取到真实姓名
4. **字段优先级**：没有正确的字段优先级逻辑（真实姓名 > 微信昵称）

## 修复方案

### 1. 修改数据库结构
**问题**：`users` 集合缺少 `name` 字段
**解决**：
- 修改用户注册逻辑，新用户自动包含 `name` 字段
- 创建数据迁移脚本，为现有用户添加 `name` 字段

### 2. 修改云函数 `saveUserProfile`
**文件**：`cloudfunctions/saveUserProfile/index.js`

**修改内容**：
- 同时更新 `users` 集合和 `user_profiles` 集合
- 确保用户的真实姓名存储在 `users.name` 字段中
- 如果用户不存在，自动创建包含完整字段的用户记录

**关键代码**：
```javascript
// 检查用户是否存在，不存在则创建包含name字段的完整记录
await db.collection('users').doc(wxContext.OPENID).set({
  data: {
    openid: wxContext.OPENID,
    nickName: userInfo.nickName || '微信用户',
    name: userInfo.name || '', // 添加真实姓名字段
    // ... 其他字段
  }
})
```

### 3. 修改用户注册逻辑
**文件**：`cloudfunctions/login/index.js`

**修改内容**：
- 新用户注册时自动包含 `name` 字段
- 添加其他必要的用户信息字段

**关键代码**：
```javascript
const userData = {
  // ... 原有字段
  name: '', // 添加真实姓名字段，初始为空
  school: '', // 添加学校字段
  subject: '', // 添加学科字段
  // ... 其他字段
}
```

### 2. 修改云函数 `getUserProfile`
**文件**：`cloudfunctions/getUserProfile/index.js`

**修改内容**：
- 优先从 `users` 集合获取用户信息
- 如果 `users` 集合没有数据，再从 `user_profiles` 集合获取
- 确保返回完整的用户信息结构

**关键代码**：
```javascript
// 🔥 优先从users集合获取用户信息（包含真实姓名）
const usersResult = await db.collection('users').doc(wxContext.OPENID).get()
```

### 3. 修改管理后台查询逻辑
**文件**：`cloudfunctions/dataQuery/index.js`

**修改内容**：
- 在教师统计查询中，优先使用 `name` 字段（真实姓名）
- 设置正确的字段优先级：`name` > `nickName` > `nickname`

**关键代码**：
```javascript
// 优先使用真实姓名
wechatName: stat.userInfo[0]?.name || stat.userInfo[0]?.nickName || stat.userInfo[0]?.nickname || `微信用户${index + 1}`,
realName: stat.userInfo[0]?.name || stat.userInfo[0]?.nickName || `教师${index + 1}`,
```

## 数据流程

### 用户设置姓名流程
1. 用户在小程序设置页面输入真实姓名
2. 调用 `saveUserProfile` 云函数
3. 同时更新 `users` 集合和 `user_profiles` 集合
4. `users.name` 字段存储真实姓名

### 管理后台显示流程
1. 管理后台调用 `dataQuery` 云函数
2. 从 `comments` 集合按 `teacherId` 分组统计
3. 通过 `lookup` 关联 `users` 集合获取用户信息
4. 优先显示 `users.name`（真实姓名），其次显示 `users.nickName`（微信昵称）

## 字段优先级

### 显示优先级
1. **`users.name`** - 用户在小程序中设置的真实姓名（最高优先级）
2. **`users.nickName`** - 微信昵称
3. **`users.nickname`** - 兼容字段
4. **默认值** - "微信用户X" 或 "教师X"

### 数据库字段说明
- **`users` 集合**：主要用户信息存储，管理后台查询的数据源
  - `name`：用户设置的真实姓名
  - `nickName`：微信昵称
  - `avatarUrl`：头像URL
  - `school`、`subject`、`grade` 等：其他用户信息

- **`user_profiles` 集合**：用户资料备份，用于数据恢复
  - `userInfo`：完整的用户信息对象
  - `updateTime`：更新时间
  - `platform`：平台标识

## 测试验证

### 测试步骤
1. 用户在小程序设置中修改真实姓名
2. 检查 `users` 集合中的 `name` 字段是否更新
3. 在管理后台查看教师统计，确认显示真实姓名
4. 测试暗黑模式下的显示效果

### 预期结果
- 如果用户设置了真实姓名，管理后台显示真实姓名
- 如果用户没有设置真实姓名，显示微信昵称
- 暗黑模式下教师姓名显示为白色

## 兼容性说明

### 向后兼容
- 现有的 `user_profiles` 集合数据保持不变
- 新的修改会同时更新两个集合
- 查询逻辑有降级机制，确保数据可用性

### 数据迁移
- **必须进行数据迁移**：为现有用户添加 `name` 字段
- 提供了 `migrateUserData` 云函数进行批量迁移
- 迁移过程会自动从 `user_profiles` 集合同步真实姓名
- 用户下次修改信息时会自动更新到 `users` 集合

## 部署步骤

### 1. 部署云函数
需要重新部署以下云函数：
- `saveUserProfile` - 保存用户资料
- `getUserProfile` - 获取用户资料
- `login` - 用户登录注册
- `validateToken` - 令牌验证
- `migrateUserData` - 数据迁移（新增）

### 2. 执行数据迁移
```javascript
// 在小程序管理后台或云开发控制台执行
wx.cloud.callFunction({
  name: 'migrateUserData'
}).then(res => {
  console.log('数据迁移结果:', res.result)
})
```

### 3. 验证修复效果
1. 检查现有用户是否有 `name` 字段
2. 测试用户设置真实姓名功能
3. 验证管理后台显示效果

## 注意事项

1. **数据迁移必须先执行**：确保现有用户有 `name` 字段
2. **云函数部署顺序**：先部署 `migrateUserData`，执行迁移后再部署其他云函数
3. **权限检查**：确保云函数有 `users` 集合的读写权限
4. **错误处理**：增加了完善的错误处理和降级机制
5. **日志记录**：添加了详细的日志记录便于调试
6. **备份数据**：建议在迁移前备份 `users` 集合数据
