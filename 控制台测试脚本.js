// 🔧 管理后台AI模型显示问题诊断脚本
// 在管理后台的浏览器控制台中运行此脚本

console.log('🚀 开始AI模型显示问题诊断...');

// 测试云函数调用
async function testCloudFunction() {
    console.log('📡 测试云函数调用...');
    
    try {
        // 动态导入cloudbaseConfig
        const { default: cloudbaseService } = await import('./src/utils/cloudbaseConfig.ts');
        
        console.log('✅ cloudbaseService导入成功');
        
        // 调用getModels
        const result = await cloudbaseService.callFunction('adminAPI', {
            action: 'ai.getModels'
        });
        
        console.log('📊 云函数调用结果:', result);
        
        if (result.code === 200 && result.data) {
            console.log(`✅ 成功获取 ${result.data.length} 个AI模型:`);
            result.data.forEach((model, index) => {
                console.log(`${index + 1}. ${model.name} (${model.id}) - ${model.status}`);
            });
            return result.data;
        } else {
            console.error('❌ 云函数调用失败:', result);
            return null;
        }
    } catch (error) {
        console.error('❌ 测试云函数调用失败:', error);
        return null;
    }
}

// 检查本地存储
function checkLocalStorage() {
    console.log('📱 检查本地存储...');
    
    const aiModels = localStorage.getItem('aiModels');
    const lastSync = localStorage.getItem('aiModelsLastSync');
    
    if (aiModels) {
        const models = JSON.parse(aiModels);
        console.log(`📦 本地存储中有 ${models.length} 个AI模型:`);
        models.forEach((model, index) => {
            console.log(`${index + 1}. ${model.name} (${model.id}) - ${model.status}`);
        });
        
        if (lastSync) {
            const syncTime = new Date(parseInt(lastSync));
            console.log(`🕒 最后同步时间: ${syncTime.toLocaleString()}`);
        }
        
        return models;
    } else {
        console.log('📦 本地存储中没有AI模型数据');
        return [];
    }
}

// 检查React组件状态
function checkReactState() {
    console.log('⚛️ 检查React组件状态...');
    
    // 尝试找到React组件实例
    const reactRoot = document.querySelector('#root');
    if (reactRoot && reactRoot._reactInternalFiber) {
        console.log('✅ 找到React根组件');
        // 这里可以进一步检查组件状态，但需要React DevTools
    } else {
        console.log('⚠️ 无法直接访问React组件状态，建议使用React DevTools');
    }
}

// 模拟添加测试模型
async function addTestModel() {
    console.log('🧪 添加测试模型...');
    
    try {
        const { default: cloudbaseService } = await import('./src/utils/cloudbaseConfig.ts');
        
        const testModel = {
            action: 'ai.createModel',
            name: `测试模型_${Date.now()}`,
            provider: 'openai',
            model: 'gpt-3.5-turbo',
            config: {
                apiKey: 'sk-test123',
                baseUrl: 'https://api.openai.com/v1'
            },
            inputPrice: 0.001,
            outputPrice: 0.002
        };
        
        const result = await cloudbaseService.callFunction('adminAPI', testModel);
        
        if (result.code === 200) {
            console.log('✅ 测试模型创建成功:', result.data);
            return result.data;
        } else {
            console.error('❌ 测试模型创建失败:', result);
            return null;
        }
    } catch (error) {
        console.error('❌ 添加测试模型失败:', error);
        return null;
    }
}

// 综合诊断
async function runDiagnosis() {
    console.log('🔍 开始综合诊断...');
    console.log('='.repeat(50));
    
    // 1. 检查本地存储
    const localModels = checkLocalStorage();
    
    console.log('='.repeat(50));
    
    // 2. 测试云函数
    const cloudModels = await testCloudFunction();
    
    console.log('='.repeat(50));
    
    // 3. 对比结果
    console.log('📊 诊断结果对比:');
    console.log(`本地存储模型数量: ${localModels.length}`);
    console.log(`云函数返回模型数量: ${cloudModels ? cloudModels.length : 0}`);
    
    if (localModels.length !== (cloudModels ? cloudModels.length : 0)) {
        console.warn('⚠️ 本地存储和云函数返回的模型数量不一致！');
        
        if (cloudModels && cloudModels.length > localModels.length) {
            console.log('💡 建议: 云端有更多数据，可能需要刷新页面同步');
        } else if (localModels.length > (cloudModels ? cloudModels.length : 0)) {
            console.log('💡 建议: 本地有更多数据，可能云端同步失败');
        }
    } else {
        console.log('✅ 数据数量一致');
    }
    
    console.log('='.repeat(50));
    
    // 4. 检查React状态
    checkReactState();
    
    console.log('🎯 诊断完成！');
    
    return {
        localModels,
        cloudModels,
        consistent: localModels.length === (cloudModels ? cloudModels.length : 0)
    };
}

// 快速修复建议
function quickFix() {
    console.log('🔧 快速修复建议:');
    console.log('1. 刷新页面: location.reload()');
    console.log('2. 清空本地存储: localStorage.clear()');
    console.log('3. 手动同步: 点击页面上的"同步"按钮');
    console.log('4. 检查网络: 确保能访问云函数');
}

// 导出函数供控制台使用
window.aiModelDiagnosis = {
    runDiagnosis,
    testCloudFunction,
    checkLocalStorage,
    addTestModel,
    quickFix
};

console.log('✅ 诊断脚本加载完成！');
console.log('💡 使用方法:');
console.log('  - 运行完整诊断: aiModelDiagnosis.runDiagnosis()');
console.log('  - 测试云函数: aiModelDiagnosis.testCloudFunction()');
console.log('  - 检查本地存储: aiModelDiagnosis.checkLocalStorage()');
console.log('  - 添加测试模型: aiModelDiagnosis.addTestModel()');
console.log('  - 查看修复建议: aiModelDiagnosis.quickFix()');

// 自动运行诊断
console.log('🚀 自动运行诊断...');
runDiagnosis().then(result => {
    console.log('📋 诊断结果:', result);
    if (!result.consistent) {
        console.log('⚠️ 发现数据不一致问题！');
        quickFix();
    }
});
