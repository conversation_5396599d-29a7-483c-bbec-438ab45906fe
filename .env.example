# 评语灵感君小程序环境变量配置模板
# 复制此文件为 .env 并填入真实值

# ===== 基础配置 =====
NODE_ENV=production
VERSION=3.0.0

# ===== 微信小程序配置 =====
MINIPROGRAM_APP_ID=wx3de03090b8e8a734
MINIPROGRAM_APP_SECRET=your_app_secret_here

# ===== 云开发配置 =====
CLOUD_ENV_ID=cloud1-4g85f8xlb8166ff1
CLOUD_REGION=ap-shanghai

# ===== AI服务配置 =====
# 豆包AI API配置
DOUBAO_API_KEY=your_doubao_api_key_here
DOUBAO_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
DOUBAO_MODEL=doubao-pro-4k

# ===== 监控配置 =====
# Sentry错误监控
SENTRY_DSN_PROD=your_sentry_dsn_here
SENTRY_DSN_DEV=your_dev_sentry_dsn_here

# MTA统计分析
MTA_APP_ID_PROD=your_mta_app_id_here
MTA_APP_ID_DEV=your_dev_mta_app_id_here

# ===== 数据库配置 =====
DB_CONNECTION_TIMEOUT=10000
DB_MAX_CONNECTIONS=50

# ===== 安全配置 =====
# 加密密钥（生产环境必须设置）
ENCRYPTION_KEY=your_32_character_encryption_key
JWT_SECRET=your_jwt_secret_here

# API限流配置
RATE_LIMIT_MAX_REQUESTS=200
RATE_LIMIT_WINDOW_MS=60000

# ===== 性能配置 =====
# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# 并发控制
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=30000

# ===== 日志配置 =====
LOG_LEVEL=error
LOG_MAX_FILES=10
LOG_MAX_SIZE=10485760

# ===== 第三方服务配置 =====
# 如果使用其他AI服务
OPENAI_API_KEY=your_openai_key_here
BAIDU_API_KEY=your_baidu_key_here

# 短信服务（如果需要）
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key

# ===== 部署配置 =====
DEPLOY_ENVIRONMENT=production
DEPLOY_REGION=ap-shanghai
BACKUP_ENABLED=true

# ===== 功能开关 =====
ENABLE_AI_CACHE=true
ENABLE_OFFLINE_MODE=false
ENABLE_ADVANCED_ANALYTICS=false
ENABLE_BATCH_OPERATIONS=true

# ===== 告警配置 =====
ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK=https://your-webhook-url.com
ALERT_THRESHOLD_ERROR_RATE=0.05
ALERT_THRESHOLD_RESPONSE_TIME=3000
