// 设置功能数据库初始化云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 密码哈希函数
function hashPassword(password) {
  let hash = 0
  const salt = 'admin_salt_2024'
  const str = password + salt
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash
  }
  return hash.toString(16)
}

exports.main = async (event, context) => {
  console.log('🚀 开始初始化设置功能数据库...')
  
  try {
    const results = []
    
    // 1. 创建数据库表
    console.log('📋 创建数据库表...')
    results.push('📋 创建数据库表...')
    
    const collections = ['admin_users', 'system_settings', 'user_preferences', 'admin_logs']
    
    for (const collectionName of collections) {
      try {
        await db.createCollection(collectionName)
        const msg = `✅ ${collectionName} 表创建成功`
        console.log(msg)
        results.push(msg)
      } catch (err) {
        const msg = `⚠️ ${collectionName} 表可能已存在`
        console.log(msg)
        results.push(msg)
      }
    }

    // 2. 创建默认管理员账户
    console.log('👤 创建默认管理员账户...')
    results.push('👤 创建默认管理员账户...')
    
    const adminCheck = await db.collection('admin_users').where({
      username: 'admin'
    }).get()
    
    if (adminCheck.data.length === 0) {
      await db.collection('admin_users').add({
        data: {
          username: 'admin',
          password_hash: hashPassword('admin123'),
          role: 'super_admin',
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        }
      })
      const msg = '✅ 默认管理员账户创建成功'
      console.log(msg)
      results.push(msg)
    } else {
      const msg = '⚠️ 管理员账户已存在'
      console.log(msg)
      results.push(msg)
    }

    // 3. 创建默认系统设置
    console.log('⚙️ 创建默认系统设置...')
    results.push('⚙️ 创建默认系统设置...')
    
    const settings = [
      ['systemName', '评语灵感君管理后台', 'string'],
      ['version', 'v2.0.0', 'string'],
      ['maxUsers', 1000, 'number'],
      ['sessionTimeout', 30, 'number'],
      ['autoRefresh', false, 'boolean'],
      ['darkMode', false, 'boolean'],
      ['debugMode', false, 'boolean'],
      ['enableCache', true, 'boolean'],
      ['logLevel', 'info', 'string'],
      ['backupFrequency', 'daily', 'string']
    ]

    for (const [key, value, type] of settings) {
      const existing = await db.collection('system_settings').where({
        setting_key: key
      }).get()
      
      if (existing.data.length === 0) {
        await db.collection('system_settings').add({
          data: {
            setting_key: key,
            setting_value: value,
            setting_type: type,
            updated_by: 'system',
            updated_at: new Date(),
            created_at: new Date()
          }
        })
        const msg = `✅ 系统设置 ${key} 创建成功`
        console.log(msg)
        results.push(msg)
      } else {
        const msg = `⚠️ 系统设置 ${key} 已存在`
        console.log(msg)
        results.push(msg)
      }
    }

    // 4. 完成提示
    const finalMessages = [
      '',
      '🎉 设置功能数据库初始化完成！',
      '',
      '📋 已创建的表:',
      '  - admin_users: 管理员用户表',
      '  - system_settings: 系统设置表',
      '  - user_preferences: 用户偏好表',
      '  - admin_logs: 管理员操作日志表',
      '',
      '👤 默认管理员账户:',
      '  - 用户名: admin',
      '  - 密码: admin123',
      '  - 角色: super_admin',
      '',
      '🔧 下一步:',
      '  1. 部署 settingsAPI 云函数',
      '  2. 在管理后台测试登录功能',
      '  3. 验证设置保存和加载功能',
      '',
      '✅ 初始化完成！'
    ]

    results.push(...finalMessages)
    finalMessages.forEach(msg => console.log(msg))

    return {
      code: 200,
      message: '设置功能数据库初始化成功',
      data: {
        results: results,
        summary: {
          tablesCreated: collections.length,
          adminCreated: adminCheck.data.length === 0,
          settingsCreated: settings.length
        }
      }
    }

  } catch (error) {
    console.error('❌ 初始化失败:', error)
    return {
      code: 500,
      message: '设置功能数据库初始化失败',
      error: error.message,
      data: {
        solutions: [
          '1. 检查云开发环境是否正确',
          '2. 确认数据库权限设置',
          '3. 重新运行云函数',
          '4. 查看云函数日志获取详细错误信息'
        ]
      }
    }
  }
}
