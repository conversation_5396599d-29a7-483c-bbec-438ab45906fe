/**
 * 徽章触发器
 * 在用户执行关键操作时触发徽章检测
 */

const { achievementManager } = require('./achievementManager');

class AchievementTrigger {
  /**
   * 记录行为后触发检测
   */
  static async onRecordCreated() {
    try {

      await achievementManager.triggerAchievementCheck();
    } catch (error) {
      console.error('[徽章触发器] 检测失败:', error);
    }
  }

  /**
   * 评语生成后触发检测
   */
  static async onCommentGenerated(commentData) {
    try {

      await achievementManager.triggerAchievementCheck();
    } catch (error) {
      console.error('[徽章触发器] 检测失败:', error);
    }
  }

  /**
   * 学生添加后触发检测
   */
  static async onStudentAdded() {
    try {

      await achievementManager.triggerAchievementCheck();
    } catch (error) {
      console.error('[徽章触发器] 检测失败:', error);
    }
  }

  /**
   * 通用触发器
   */
  static async trigger(action = 'unknown') {
    try {

      const newAchievements = await achievementManager.triggerAchievementCheck();
      
      if (newAchievements.length > 0) {

      }
      
      return newAchievements;
    } catch (error) {
      console.error('[徽章触发器] 检测失败:', error);
      return [];
    }
  }
}

module.exports = {
  AchievementTrigger
};