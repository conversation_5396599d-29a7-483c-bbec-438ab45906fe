#!/usr/bin/env node

/**
 * 环境变量验证脚本 - 简化版
 */

const fs = require('fs')
const path = require('path')

const colors = {
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
  bold: (text) => `\x1b[1m${text}\x1b[0m`
}

// 简化的环境变量读取
const readEnvFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const env = {}
    
    content.split(/\r?\n/).forEach(line => {
      line = line.trim()
      if (line && !line.startsWith('#') && line.includes('=')) {
        const [key, ...valueParts] = line.split('=')
        const cleanKey = key.trim()
        const cleanValue = valueParts.join('=').trim()
        
        if (cleanKey && cleanValue !== '') {
          env[cleanKey] = cleanValue
        }
      }
    })
    
    return env
  } catch (error) {
    return null
  }
}

// 验证单个文件
const validateFile = (fileName) => {
  const filePath = path.join(__dirname, '..', fileName)
  const env = readEnvFile(filePath)
  
  if (!env) {
    if (fileName !== '.env.example') {
      console.log(colors.yellow(`⚠️  ${fileName} not found`))
    }
    return true // 不存在的文件不算错误
  }
  
  console.log(colors.bold(`📋 Checking ${fileName}:`))
  
  const errors = []
  const warnings = []
  const isExample = fileName === '.env.example'
  
  // 必需的环境变量
  const requiredKeys = ['VITE_API_BASE_URL', 'VITE_WECHAT_CLOUD_ENV_ID']
  
  requiredKeys.forEach(key => {
    if (!env[key]) {
      errors.push(`${key} is missing`)
    } else if (!isExample && (env[key].includes('your-') || env[key].length < 5)) {
      errors.push(`${key} needs to be configured with actual values`)
    }
  })
  
  // 显示结果
  if (errors.length > 0) {
    console.log(colors.red('  ❌ Errors:'))
    errors.forEach(error => console.log(colors.red(`    • ${error}`)))
  }
  
  if (warnings.length > 0) {
    console.log(colors.yellow('  ⚠️  Warnings:'))
    warnings.forEach(warning => console.log(colors.yellow(`    • ${warning}`)))
  }
  
  if (errors.length === 0 && warnings.length === 0) {
    console.log(colors.green('    ✅ All checks passed'))
  }
  
  console.log()
  
  return errors.length === 0
}

// 主函数
const main = () => {
  console.log(colors.bold(colors.cyan('🔧 Environment Configuration Validation\n')))
  
  const files = ['.env.local', '.env.development', '.env.production', '.env.example']
  let allValid = true
  
  files.forEach(fileName => {
    const isValid = validateFile(fileName)
    if (!isValid) {
      allValid = false
    }
  })
  
  // 检查是否存在本地配置
  if (!fs.existsSync(path.join(__dirname, '..', '.env.local'))) {
    console.log(colors.yellow('💡 Tip: Run "npm run env:setup" to create .env.local\n'))
  }
  
  // 总结
  if (allValid) {
    console.log(colors.green(colors.bold('✅ Environment validation passed!')))
    console.log(colors.cyan('🚀 You can now run: npm run dev'))
  } else {
    console.log(colors.red(colors.bold('❌ Some configuration issues found.')))
    console.log(colors.yellow('💡 The most important file is .env.local - if it passes, you can continue development.'))
  }
}

main()