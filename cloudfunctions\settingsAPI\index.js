// 设置管理API云函数
const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 密码加密函数
function hashPassword(password) {
  return crypto.createHash('sha256').update(password + 'admin_salt_2024').digest('hex')
}

// 验证管理员权限
async function verifyAdmin(openid) {
  try {
    const adminUser = await db.collection('admin_users').where({
      openid: openid,
      status: 'active'
    }).get()
    
    return adminUser.data.length > 0 ? adminUser.data[0] : null
  } catch (error) {
    console.error('验证管理员权限失败:', error)
    return null
  }
}

// 初始化默认管理员账户
async function initDefaultAdmin() {
  try {
    const existingAdmin = await db.collection('admin_users').where({
      username: 'admin'
    }).get()
    
    if (existingAdmin.data.length === 0) {
      await db.collection('admin_users').add({
        data: {
          username: 'admin',
          password_hash: hashPassword('admin123'),
          role: 'super_admin',
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        }
      })
      console.log('✅ 默认管理员账户已创建')
    }
  } catch (error) {
    console.error('初始化默认管理员失败:', error)
  }
}

// 初始化默认系统设置
async function initDefaultSettings() {
  try {
    const defaultSettings = [
      { key: 'systemName', value: '评语灵感君管理后台', type: 'string' },
      { key: 'version', value: 'v2.0.0', type: 'string' },
      { key: 'maxUsers', value: 1000, type: 'number' },
      { key: 'sessionTimeout', value: 30, type: 'number' },
      { key: 'autoRefresh', value: false, type: 'boolean' },
      { key: 'darkMode', value: false, type: 'boolean' },
      { key: 'debugMode', value: false, type: 'boolean' },
      { key: 'enableCache', value: true, type: 'boolean' },
      { key: 'logLevel', value: 'info', type: 'string' },
      { key: 'backupFrequency', value: 'daily', type: 'string' }
    ]
    
    for (const setting of defaultSettings) {
      const existing = await db.collection('system_settings').where({
        setting_key: setting.key
      }).get()
      
      if (existing.data.length === 0) {
        await db.collection('system_settings').add({
          data: {
            setting_key: setting.key,
            setting_value: setting.value,
            setting_type: setting.type,
            updated_by: 'system',
            updated_at: new Date(),
            created_at: new Date()
          }
        })
      }
    }
    console.log('✅ 默认系统设置已初始化')
  } catch (error) {
    console.error('初始化默认设置失败:', error)
  }
}

exports.main = async (event, context) => {
  const { action, data = {} } = event
  const { OPENID } = cloud.getWXContext()
  
  console.log('🔧 设置API调用:', { action, openid: OPENID })
  
  try {
    switch (action) {
      // 管理员登录
      case 'admin.login':
        const { username, password } = data
        
        if (!username || !password) {
          return { code: 400, message: '用户名和密码不能为空' }
        }
        
        const passwordHash = hashPassword(password)
        const adminUser = await db.collection('admin_users').where({
          username: username,
          password_hash: passwordHash,
          status: 'active'
        }).get()
        
        if (adminUser.data.length > 0) {
          const user = adminUser.data[0]
          
          // 更新openid绑定
          await db.collection('admin_users').doc(user._id).update({
            data: {
              openid: OPENID,
              last_login: new Date(),
              updated_at: new Date()
            }
          })
          
          return {
            code: 200,
            message: '登录成功',
            data: {
              id: user._id,
              username: user.username,
              role: user.role,
              token: `admin_${user._id}_${Date.now()}`
            }
          }
        } else {
          return { code: 401, message: '用户名或密码错误' }
        }
      
      // 修改密码
      case 'admin.changePassword':
        const admin = await verifyAdmin(OPENID)
        if (!admin) {
          return { code: 403, message: '权限不足' }
        }
        
        const { currentPassword, newPassword } = data
        if (!currentPassword || !newPassword) {
          return { code: 400, message: '当前密码和新密码不能为空' }
        }
        
        const currentHash = hashPassword(currentPassword)
        if (admin.password_hash !== currentHash) {
          return { code: 400, message: '当前密码不正确' }
        }
        
        const newHash = hashPassword(newPassword)
        await db.collection('admin_users').doc(admin._id).update({
          data: {
            password_hash: newHash,
            updated_at: new Date()
          }
        })
        
        // 记录操作日志
        await db.collection('admin_logs').add({
          data: {
            action: 'password_change',
            user_id: admin._id,
            username: admin.username,
            timestamp: new Date(),
            ip: context.CLIENTIP || 'unknown'
          }
        })
        
        return { code: 200, message: '密码修改成功' }
      
      // 获取系统设置
      case 'settings.getSystem':
        const adminCheck1 = await verifyAdmin(OPENID)
        if (!adminCheck1) {
          return { code: 403, message: '权限不足' }
        }
        
        const systemSettings = await db.collection('system_settings').get()
        const settingsObj = {}
        
        systemSettings.data.forEach(setting => {
          settingsObj[setting.setting_key] = setting.setting_value
        })
        
        return {
          code: 200,
          message: '获取系统设置成功',
          data: settingsObj
        }
      
      // 更新系统设置
      case 'settings.updateSystem':
        const adminCheck2 = await verifyAdmin(OPENID)
        if (!adminCheck2) {
          return { code: 403, message: '权限不足' }
        }
        
        const { settings } = data
        if (!settings || typeof settings !== 'object') {
          return { code: 400, message: '设置数据格式错误' }
        }
        
        // 批量更新设置
        const updatePromises = Object.keys(settings).map(async (key) => {
          const value = settings[key]
          const type = typeof value
          
          return db.collection('system_settings').where({
            setting_key: key
          }).update({
            data: {
              setting_value: value,
              setting_type: type,
              updated_by: adminCheck2.username,
              updated_at: new Date()
            }
          })
        })
        
        await Promise.all(updatePromises)
        
        // 记录操作日志
        await db.collection('admin_logs').add({
          data: {
            action: 'system_settings_update',
            user_id: adminCheck2._id,
            username: adminCheck2.username,
            changes: settings,
            timestamp: new Date(),
            ip: context.CLIENTIP || 'unknown'
          }
        })
        
        return { code: 200, message: '系统设置更新成功' }
      
      // 获取用户偏好
      case 'preferences.get':
        const adminCheck3 = await verifyAdmin(OPENID)
        if (!adminCheck3) {
          return { code: 403, message: '权限不足' }
        }
        
        const userPrefs = await db.collection('user_preferences').where({
          user_id: adminCheck3._id
        }).get()
        
        const prefsObj = {}
        userPrefs.data.forEach(pref => {
          prefsObj[pref.preference_key] = pref.preference_value
        })
        
        return {
          code: 200,
          message: '获取用户偏好成功',
          data: prefsObj
        }
      
      // 更新用户偏好
      case 'preferences.update':
        const adminCheck4 = await verifyAdmin(OPENID)
        if (!adminCheck4) {
          return { code: 403, message: '权限不足' }
        }
        
        const { preferences } = data
        if (!preferences || typeof preferences !== 'object') {
          return { code: 400, message: '偏好数据格式错误' }
        }
        
        // 批量更新偏好
        const prefUpdatePromises = Object.keys(preferences).map(async (key) => {
          const value = preferences[key]
          
          // 先尝试更新，如果不存在则创建
          const existing = await db.collection('user_preferences').where({
            user_id: adminCheck4._id,
            preference_key: key
          }).get()
          
          if (existing.data.length > 0) {
            return db.collection('user_preferences').doc(existing.data[0]._id).update({
              data: {
                preference_value: value,
                updated_at: new Date()
              }
            })
          } else {
            return db.collection('user_preferences').add({
              data: {
                user_id: adminCheck4._id,
                preference_key: key,
                preference_value: value,
                created_at: new Date(),
                updated_at: new Date()
              }
            })
          }
        })
        
        await Promise.all(prefUpdatePromises)
        
        return { code: 200, message: '用户偏好更新成功' }
      
      // 初始化数据
      case 'system.init':
        await initDefaultAdmin()
        await initDefaultSettings()
        return { code: 200, message: '系统初始化完成' }
      
      default:
        return { code: 400, message: '未知操作' }
    }
    
  } catch (error) {
    console.error('设置API错误:', error)
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    }
  }
}
