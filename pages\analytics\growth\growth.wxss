/* 成长报告页面样式 */
.growth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 加载状态 */
.growth-container .van-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

/* 主要内容 */
.content {
  padding-bottom: 40rpx;
}

/* 头部统计卡片 */
.stats-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.header-title {
  text-align: center;
  margin-bottom: 32rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 分享按钮 */
.share-button {
  position: absolute;
  top: 40rpx;
  right: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  background: rgba(84, 112, 198, 0.1);
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.share-button:active {
  transform: scale(0.95);
  background: rgba(84, 112, 198, 0.2);
}

.share-text {
  font-size: 20rpx;
  color: #5470C6;
  margin-top: 4rpx;
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: #f8f9ff;
  border-radius: 16rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #5470C6;
  line-height: 1.2;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 通用卡片样式 */
.section-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

/* 趋势图 */
.trend-chart {
  padding: 24rpx 0;
}

.chart-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 200rpx;
  padding: 0 16rpx;
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.trend-bar {
  width: 24rpx;
  background: linear-gradient(to top, #5470C6, #91CC75);
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  min-height: 20rpx;
  transition: height 0.3s ease;
}

.trend-label {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.trend-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #5470C6;
}

/* 质量分析 */
.quality-trend {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.quality-trend.up {
  color: #52C41A;
}

.quality-trend.down {
  color: #FF4D4F;
}

.quality-content {
  display: flex;
  gap: 32rpx;
}

.quality-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.score-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #5470C6, #91CC75);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 16rpx;
}

.score-number {
  font-size: 36rpx;
  font-weight: 700;
  color: white;
}

.score-total {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  position: absolute;
  bottom: 24rpx;
  right: 20rpx;
}

.score-desc {
  font-size: 24rpx;
  color: #666;
}

.suggestions {
  flex: 1;
}

.suggestions-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  margin-bottom: 12rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 专业能力雷达图 */
.ability-radar {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.radar-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.ability-name {
  width: 120rpx;
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

.ability-bar {
  flex: 1;
  height: 16rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.ability-progress {
  height: 100%;
  background: linear-gradient(90deg, #5470C6, #91CC75);
  border-radius: 8rpx;
  transition: width 0.5s ease;
}

.ability-score {
  width: 60rpx;
  text-align: right;
  font-size: 24rpx;
  font-weight: 600;
  color: #5470C6;
}

/* 成就展示 */
.achievements-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #f8f9ff;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.achievement-item.unlocked {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);
  transform: scale(1.02);
  border: 2rpx solid #FFD700;
}

.achievement-item.unlocked .achievement-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.achievement-item.unlocked .achievement-name,
.achievement-item.unlocked .achievement-desc {
  color: white;
}

.achievement-item.locked {
  background: linear-gradient(135deg, #E5E5E5 0%, #CCCCCC 100%);
  color: #999;
  opacity: 0.8;
}

.achievement-icon {
  font-size: 48rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(84, 112, 198, 0.1);
  border-radius: 50%;
}

.achievement-info {
  flex: 1;
}

.achievement-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

.achievement-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.achievement-progress {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.progress-text {
  font-size: 20rpx;
  color: #5470C6;
}

.achievement-date {
  font-size: 20rpx;
  color: #52C41A;
}

/* 月度亮点 */
.highlights {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 26rpx;
  color: #1a1a1a;
  padding: 16rpx;
  background: #f6ffed;
  border-radius: 12rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
}

.share-btn, .export-btn {
  flex: 1;
  height: 88rpx !important;
  border-radius: 44rpx !important;
  font-size: 28rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8rpx !important;
}

.share-btn {
  background: linear-gradient(135deg, #5470C6, #91CC75) !important;
  border: none !important;
}

.export-btn {
  background: white !important;
  border: 2rpx solid #5470C6 !important;
  color: #5470C6 !important;
}
