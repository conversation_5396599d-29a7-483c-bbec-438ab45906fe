/* Dashboard 搜索框样式 */
.dashboard-search .ant-input-search {
  border-radius: 25px !important; /* 大圆角样式 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border: 2px solid #d1d5db !important; /* 更明显的边框颜色 */
  background: white !important;
  transition: all 0.3s ease !important;
}

.dashboard-search .ant-input-search:hover {
  border-color: #4096ff !important;
  box-shadow: 0 6px 16px rgba(64, 150, 255, 0.2) !important;
  transform: translateY(-1px) !important; /* 轻微上浮效果 */
}

.dashboard-search .ant-input-search:focus-within {
  border-color: #4096ff !important;
  box-shadow: 0 0 0 4px rgba(64, 150, 255, 0.15) !important; /* 更明显的焦点环 */
  transform: translateY(-1px) !important;
}

.dashboard-search .ant-input-search-input-wrapper {
  padding-left: 20px !important; /* 增加左侧内边距 */
}

.dashboard-search .ant-input {
  border: none !important;
  box-shadow: none !important;
  font-size: 16px !important;
  padding: 12px 0 !important;
  background: transparent !important;
}

.dashboard-search .ant-input:focus {
  box-shadow: none !important;
  border: none !important;
}

.dashboard-search .ant-input-search-button {
  border-radius: 0 25px 25px 0 !important; /* 匹配输入框圆角 */
  background: transparent !important; /* 无底色样式 */
  border: none !important;
  height: 100% !important;
  color: #6b7280 !important; /* 图标颜色 */
  transition: all 0.3s ease !important;
}

.dashboard-search .ant-input-search-button:hover {
  background: rgba(64, 150, 255, 0.1) !important; /* 悬停时轻微背景色 */
  color: #4096ff !important; /* 悬停时图标颜色变蓝 */
  transform: scale(1.05) !important; /* 轻微放大效果 */
}

/* 搜索结果动画 */
.search-results-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.search-results-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.search-results-exit {
  opacity: 1;
  transform: translateY(0);
}

.search-results-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

/* 暗色模式适配 */
[data-theme="dark"] .dashboard-search .ant-input-search {
  background: #1f2937 !important; /* 暗色背景 */
  border-color: #4b5563 !important; /* 更明显的暗色边框 */
}

[data-theme="dark"] .dashboard-search .ant-input-search:hover {
  border-color: #4096ff !important;
  box-shadow: 0 6px 16px rgba(64, 150, 255, 0.3) !important;
}

[data-theme="dark"] .dashboard-search .ant-input-search:focus-within {
  border-color: #4096ff !important;
  box-shadow: 0 0 0 3px rgba(64, 150, 255, 0.2) !important;
}

[data-theme="dark"] .dashboard-search .ant-input {
  color: #f9fafb !important; /* 暗色模式文字颜色 */
}

[data-theme="dark"] .dashboard-search .ant-input::placeholder {
  color: #9ca3af !important; /* 暗色模式占位符颜色 */
}

[data-theme="dark"] .dashboard-search .ant-input-search-button {
  color: #9ca3af !important; /* 暗色模式图标颜色 */
}

[data-theme="dark"] .dashboard-search .ant-input-search-button:hover {
  background: rgba(64, 150, 255, 0.2) !important; /* 暗色模式悬停背景 */
  color: #60a5fa !important; /* 暗色模式悬停图标颜色 */
}