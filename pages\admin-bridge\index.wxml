<!--管理后台数据桥接页面-->
<view class="bridge-container">
  <view class="bridge-header">
    <text class="bridge-title">🔌 管理后台数据桥接</text>
    <view class="bridge-status">
      <text class="status-text">状态: </text>
      <text class="status-indicator {{connectionStatus}}">
        {{statusText}}
      </text>
    </view>
  </view>

  <view class="bridge-info">
    <view class="info-item">
      <text class="info-label">环境ID:</text>
      <text class="info-value">cloud1-4g85f8xlb8166ff1</text>
    </view>
    <view class="info-item">
      <text class="info-label">最后更新:</text>
      <text class="info-value">{{lastUpdate ? '刚刚' : '未更新'}}</text>
    </view>
  </view>

  <view class="bridge-actions">
    <button class="sync-btn" bindtap="onSyncData" disabled="{{!isReady}}">
      🔄 手动同步数据
    </button>
  </view>

  <view class="bridge-description">
    <text class="desc-title">使用说明:</text>
    <text class="desc-text">此页面用于管理后台获取小程序数据，请保持页面打开状态。</text>
    <text class="desc-text">数据会每30秒自动同步一次。</text>
  </view>
</view>