import React, { useState, useMemo, useEffect } from 'react'
import { Card, Select, Typography, Space, Statistic, Row, Col } from 'antd'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts'
import { <PERSON>boltOutlined, ClockCircleOutlined, CalendarOutlined } from '@ant-design/icons'
import { dataService } from '../services'
import CostCalculator from '../utils/costCalculator'

const { Title, Text } = Typography
const { Option } = Select

interface AITokensChartProps {
  className?: string
}

// 生成时间数据结构，等待AI配置完成后填充真实数据
const generateHourlyData = () => {
  const hours = []
  const now = new Date()
  for (let i = 23; i >= 0; i--) {
    const hour = new Date(now.getTime() - i * 60 * 60 * 1000)
    hours.push({
      time: hour.getHours().toString().padStart(2, '0') + ':00',
      tokens: 0, // AI配置完成后将从API获取真实数据
      requests: 0
    })
  }
  return hours
}

// 生成日期数据结构，等待AI配置完成后填充真实数据
const generateDailyData = () => {
  const days = []
  const now = new Date()
  for (let i = 29; i >= 0; i--) {
    const day = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
    days.push({
      time: `${day.getMonth() + 1}/${day.getDate()}`,
      tokens: 0, // AI配置完成后将从API获取真实数据
      requests: 0
    })
  }
  return days
}

const AITokensChart: React.FC<AITokensChartProps> = ({ className }) => {
  const [timeRange, setTimeRange] = useState<'hour' | 'day'>('hour')
  const [tokensHistory, setTokensHistory] = useState<any[]>([])
  const [statistics, setStatistics] = useState<any>({})
  const [costStatistics, setCostStatistics] = useState<any>({})
  const [isConnected, setIsConnected] = useState(false)
  const [loading, setLoading] = useState(true)

  // 获取AI tokens数据
  useEffect(() => {
    const fetchTokensData = async () => {
      try {
        setLoading(true)
        
        // 🔥 使用统一的云函数数据服务获取评语、tokens和费用数据
        const [commentsResult, dashboardStats, costStats] = await Promise.all([
          dataService.getComments({ limit: 200 }).catch(() => ({ list: [] })),
          import('../services/realDataService').then(m =>
            m.realDataService.callAPI('getDashboardStats')
          ).catch(() => null),
          // 获取费用统计数据 - 多重策略
          import('../utils/cloudbaseConfig').then(async ({ default: cloudbaseService }) => {
            try {
              console.log('📊 开始获取费用统计数据...')

              // 策略1: 尝试通过adminAPI代理（推荐方式）
              try {
                const proxyResult = await cloudbaseService.callFunction('adminAPI', {
                  action: 'cost.getAllStats'
                })
                console.log('📊 通过adminAPI代理结果:', proxyResult)

                if (proxyResult && (proxyResult.code === 200 || proxyResult.success)) {
                  return proxyResult
                }
              } catch (proxyError) {
                console.warn('📊 adminAPI代理调用失败:', proxyError.message)
              }

              // 策略2: 尝试直接调用getCostStats云函数
              try {
                const directResult = await cloudbaseService.callFunction('getCostStats', {
                  action: 'getAllCostStats'
                })
                console.log('📊 直接调用getCostStats结果:', directResult)

                if (directResult && directResult.success) {
                  return directResult
                }
              } catch (directError) {
                console.warn('📊 getCostStats云函数调用失败:', directError.message)
              }

              // 策略3: 如果都失败，返回null让前端计算
              console.warn('📊 所有云函数调用都失败，将使用前端计算')
              return null

            } catch (error) {
              console.error('📊 费用统计获取完全失败:', error)
              return null
            }
          }).catch(() => null)
        ])
        
        console.log('📊 获取到评语数据:', commentsResult.list?.length || 0, '条')
        console.log('📊 获取到仪表板统计:', dashboardStats)
        console.log('📊 获取到费用统计:', costStats)

        // 转换评语数据为tokens历史记录
        const tokensData = commentsResult.list?.map((comment: any) => ({
          timestamp: comment.createTime,
          tokensUsed: comment.tokensUsed || Math.ceil((comment.content?.length || 50) * 1.5), // 基于内容长度估算
          userId: comment.teacherId,
          action: 'comment_generate',
          content: comment.content
        })) || []

        console.log('📊 处理后的tokens数据:', tokensData.length, '条记录')

        setTokensHistory(tokensData)
        setStatistics({
          totalTokensUsed: dashboardStats?.totalTokens ||
            tokensData.reduce((sum: number, item: any) => sum + item.tokensUsed, 0)
        })

        // 设置费用统计数据
        console.log('📊 费用统计原始数据:', costStats)

        // 检查adminAPI调用是否成功
        if (costStats && costStats.code === 200 && costStats.data) {
          // 检查内部费用统计是否成功
          if (costStats.data.success && costStats.data.data) {
            setCostStatistics(costStats.data.data)
            console.log('✅ 费用统计数据设置成功:', costStats.data.data)
            console.log('📊 数据来源:', costStats.data.fallback ? '内置实现' : 'getCostStats云函数')
          } else {
            console.warn('⚠️ 内部费用统计失败，尝试前端计算:', costStats.data)
            // 内部费用统计失败，使用前端计算
            throw new Error('内部费用统计失败')
          }
        } else {
          console.warn('⚠️ 费用统计数据获取失败，尝试前端计算:', costStats)

          // 如果云函数费用数据获取失败，尝试前端计算
          try {
            if (commentsResult.list && commentsResult.list.length > 0) {
              const costCalculationResult = CostCalculator.calculateBatchCost(
                commentsResult.list.map((comment: any) => ({
                  tokensUsed: comment.tokensUsed || Math.ceil((comment.content?.length || 50) * 1.5),
                  aiModel: comment.aiModel || 'doubao-pro-4k',
                  content: comment.content,
                  createTime: comment.createTime
                }))
              )

              const todayResult = CostCalculator.calculateTodayCost(
                commentsResult.list.map((comment: any) => ({
                  tokensUsed: comment.tokensUsed || Math.ceil((comment.content?.length || 50) * 1.5),
                  aiModel: comment.aiModel || 'doubao-pro-4k',
                  content: comment.content,
                  createTime: comment.createTime
                }))
              )

              setCostStatistics({
                total: {
                  totalCost: costCalculationResult.totalCost,
                  totalCalls: costCalculationResult.totalCalls,
                  avgCostPerCall: costCalculationResult.avgCostPerCall,
                  avgCostPerToken: costCalculationResult.avgCostPerToken
                },
                daily: {
                  todayCost: todayResult.todayCost,
                  todayCalls: todayResult.todayCalls
                },
                trend: []
              })
              console.log('✅ 前端费用计算完成:', costCalculationResult)
            } else {
              throw new Error('没有评语数据用于计算')
            }
          } catch (calcError) {
            console.error('❌ 前端费用计算失败:', calcError)
            // 最终使用默认值
            setCostStatistics({
              total: { totalCost: 0, totalCalls: 0, avgCostPerCall: 0, avgCostPerToken: 0 },
              daily: { todayCost: 0, todayCalls: 0 },
              trend: []
            })
          }
        }

        setIsConnected(tokensData.length > 0)
      } catch (error) {
        console.error('获取AI tokens数据失败:', error)
        setIsConnected(false)
      } finally {
        setLoading(false)
      }
    }

    fetchTokensData()
    
    // 每分钟刷新一次数据
    const interval = setInterval(fetchTokensData, 60000)
    
    return () => clearInterval(interval)
  }, [])

  // 处理实时数据，转换为图表格式
  const chartData = useMemo(() => {
    if (tokensHistory.length === 0) {
      // 如果没有实时数据，使用模拟数据
      return timeRange === 'hour' ? generateHourlyData() : generateDailyData()
    }

    // 根据时间范围处理实时数据
    const now = new Date()
    const timeSlots = new Map<string, { tokens: number; requests: number }>()

    if (timeRange === 'hour') {
      // 生成过去24小时的时间槽
      for (let i = 23; i >= 0; i--) {
        const hour = new Date(now.getTime() - i * 60 * 60 * 1000)
        const key = hour.getHours().toString().padStart(2, '0') + ':00'
        timeSlots.set(key, { tokens: 0, requests: 0 })
      }

      // 聚合实时数据到小时槽
      tokensHistory.forEach(record => {
        const recordTime = new Date(record.timestamp)
        const hourKey = recordTime.getHours().toString().padStart(2, '0') + ':00'

        if (timeSlots.has(hourKey)) {
          const slot = timeSlots.get(hourKey)!
          slot.tokens += record.tokensUsed
          slot.requests += 1
        }
      })
    } else {
      // 生成过去30天的时间槽
      for (let i = 29; i >= 0; i--) {
        const day = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
        const key = `${day.getMonth() + 1}/${day.getDate()}`
        timeSlots.set(key, { tokens: 0, requests: 0 })
      }

      // 聚合实时数据到日槽
      tokensHistory.forEach(record => {
        const recordTime = new Date(record.timestamp)
        const dayKey = `${recordTime.getMonth() + 1}/${recordTime.getDate()}`

        if (timeSlots.has(dayKey)) {
          const slot = timeSlots.get(dayKey)!
          slot.tokens += record.tokensUsed
          slot.requests += 1
        }
      })
    }

    // 转换为图表数据格式
    return Array.from(timeSlots.entries()).map(([time, data]) => ({
      time,
      tokens: data.tokens,
      requests: data.requests
    }))
  }, [timeRange, tokensHistory])

  const totalTokens = useMemo(() => {
    return statistics.totalTokensUsed || chartData.reduce((sum, item) => sum + item.tokens, 0)
  }, [statistics.totalTokensUsed, chartData])

  const totalRequests = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.requests, 0)
  }, [chartData])

  const avgTokensPerRequest = useMemo(() => {
    return totalRequests > 0 ? Math.round(totalTokens / totalRequests) : 0
  }, [totalTokens, totalRequests])

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800 dark:text-gray-100">{`时间: ${label}`}</p>
          <p className="text-blue-600 dark:text-blue-400">
            {`Tokens消耗: ${payload[0].value.toLocaleString()}`}
          </p>
          <p className="text-green-600 dark:text-green-400">
            {`请求次数: ${payload[1].value.toLocaleString()}`}
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <Card 
      className={className}
      title={
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
              <ThunderboltOutlined className="text-white text-sm" />
            </div>
            <span>AI Tokens 消耗与费用统计</span>
            {isConnected && tokensHistory.length > 0 && !loading ? (
              <div className="flex items-center gap-1 text-green-500 text-xs">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                实时数据
              </div>
            ) : (
              <div className="flex items-center gap-1 text-gray-400 dark:text-gray-500 text-xs">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                {loading ? '连接中...' : '无数据'}
              </div>
            )}
          </div>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 120 }}
            size="small"
          >
            <Option value="hour">
              <Space>
                <ClockCircleOutlined />
                小时图
              </Space>
            </Option>
            <Option value="day">
              <Space>
                <CalendarOutlined />
                日图
              </Space>
            </Option>
          </Select>
        </div>
      }
      extra={
        <div className="text-sm text-gray-500 dark:text-gray-300">
          {timeRange === 'hour' ? '过去24小时' : '过去30天'}
        </div>
      }
    >
      {/* 统计概览 - 紧凑布局 */}
      <div className="grid grid-cols-4 lg:grid-cols-7 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600 flex items-center justify-center gap-1">
            <ThunderboltOutlined />
            {totalTokens.toLocaleString()}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-300">总消耗 Tokens</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{totalRequests}</div>
          <div className="text-xs text-gray-500 dark:text-gray-300">总请求次数</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600">¥{(costStatistics.total?.totalCost || 0).toFixed(5)}</div>
          <div className="text-xs text-gray-500 dark:text-gray-300">总费用</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">¥{(costStatistics.daily?.todayCost || 0).toFixed(5)}</div>
          <div className="text-xs text-gray-500 dark:text-gray-300">今日费用</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-purple-600">¥{(costStatistics.total?.avgCostPerCall || 0).toFixed(6)}</div>
          <div className="text-xs text-gray-500 dark:text-gray-300">平均费用/次</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-cyan-600">¥{(costStatistics.total?.avgCostPerToken || 0).toFixed(6)}</div>
          <div className="text-xs text-gray-500 dark:text-gray-300">平均费用/千tokens</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-green-500">{costStatistics.daily?.todayCalls || 0}</div>
          <div className="text-xs text-gray-500 dark:text-gray-300">今日调用次数</div>
        </div>
      </div>

      {/* 图表 */}
      <div style={{ height: 300 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--border-light)" />
            <XAxis
              dataKey="time"
              tick={{ fontSize: 12, fill: 'var(--text-tertiary)' }}
              stroke="var(--border-primary)"
            />
            <YAxis
              yAxisId="tokens"
              orientation="left"
              tick={{ fontSize: 12, fill: 'var(--text-tertiary)' }}
              stroke="var(--border-primary)"
            />
            <YAxis
              yAxisId="requests"
              orientation="right"
              tick={{ fontSize: 12, fill: 'var(--text-tertiary)' }}
              stroke="var(--border-primary)"
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar 
              yAxisId="tokens"
              dataKey="tokens" 
              fill="#8884d8" 
              name="Tokens消耗"
              radius={[2, 2, 0, 0]}
            />
            <Bar 
              yAxisId="requests"
              dataKey="requests" 
              fill="#82ca9d" 
              name="请求次数"
              radius={[2, 2, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* 趋势说明 */}
      <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <Text type="secondary" className="text-sm dark:text-gray-300">
          💡 <strong>使用提示：</strong>
          {timeRange === 'hour'
            ? '小时图显示最近24小时的AI使用情况，可以观察用户活跃时段。'
            : '日图显示最近30天的使用趋势，有助于了解长期使用模式。'
          }
          Tokens消耗量反映了AI模型的计算成本。
        </Text>
      </div>
    </Card>
  )
}

export default AITokensChart
