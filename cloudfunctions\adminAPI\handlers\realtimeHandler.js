/**
 * 实时数据模块处理器
 * 处理实时统计、在线状态、消息推送等实时相关操作
 */

module.exports = {
  /**
   * 获取实时统计数据
   */
  async getStats(data, db, cloud, admin) {
    try {
      // 获取今日数据
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      // 并行获取各种实时统计
      const [
        todayComments,
        todayRecords,
        onlineUsers,
        recentOperations,
        systemHealth
      ] = await Promise.all([
        // 今日评语生成数
        db.collection('comments').where({
          createTime: db.command.gte(today)
        }).count(),
        
        // 今日记录数
        db.collection('records').where({
          createTime: db.command.gte(today)
        }).count(),
        
        // 在线用户数（最近15分钟活跃）
        db.collection('users').where({
          lastActiveTime: db.command.gte(new Date(Date.now() - 15 * 60 * 1000))
        }).count(),
        
        // 最近操作（最近1小时）
        db.collection('operation_logs').where({
          createTime: db.command.gte(new Date(Date.now() - 60 * 60 * 1000))
        }).count(),
        
        // 系统健康检查
        Promise.resolve({
          database: 'healthy',
          storage: 'healthy',
          functions: 'healthy'
        })
      ])
      
      // 获取最近24小时的趋势数据
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      const hourlyStats = []
      
      for (let i = 23; i >= 0; i--) {
        const hourStart = new Date(Date.now() - i * 60 * 60 * 1000)
        hourStart.setMinutes(0, 0, 0)
        const hourEnd = new Date(hourStart.getTime() + 60 * 60 * 1000)
        
        const hourComments = await db.collection('comments').where({
          createTime: db.command.and([
            db.command.gte(hourStart),
            db.command.lt(hourEnd)
          ])
        }).count()
        
        hourlyStats.push({
          hour: hourStart.getHours(),
          timestamp: hourStart.toISOString(),
          comments: hourComments.total,
          // 模拟其他指标
          users: Math.floor(Math.random() * 20) + 5,
          operations: Math.floor(Math.random() * 50) + 10
        })
      }
      
      return {
        current: {
          todayComments: todayComments.total,
          todayRecords: todayRecords.total,
          onlineUsers: onlineUsers.total,
          recentOperations: recentOperations.total
        },
        system: systemHealth,
        trends: {
          hourly: hourlyStats,
          summary: {
            peakHour: hourlyStats.reduce((max, curr) => 
              curr.comments > max.comments ? curr : max
            ).hour,
            totalToday: hourlyStats.reduce((sum, curr) => sum + curr.comments, 0)
          }
        },
        lastUpdated: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`获取实时统计失败: ${error.message}`)
    }
  },

  /**
   * 获取在线管理员列表
   */
  async getOnlineAdmins(data, db, cloud, admin) {
    try {
      // 获取最近15分钟活跃的管理员
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000)
      
      const onlineAdminsResult = await db.collection('admins')
        .where({
          lastActiveTime: db.command.gte(fifteenMinutesAgo),
          status: 'active'
        })
        .field({
          password: false, // 不返回密码
          username: true,
          profile: true,
          role: true,
          lastActiveTime: true
        })
        .get()
      
      const onlineAdmins = onlineAdminsResult.data.map(adminItem => {
        const lastActiveTime = new Date(adminItem.lastActiveTime)
        const minutesAgo = Math.floor((Date.now() - lastActiveTime.getTime()) / (1000 * 60))
        
        return {
          id: adminItem._id,
          username: adminItem.username,
          name: adminItem.profile?.name || adminItem.username,
          avatar: adminItem.profile?.avatar || '',
          role: adminItem.role,
          status: minutesAgo <= 5 ? 'online' : 'away', // 5分钟内为在线，否则为离开
          lastActiveTime: adminItem.lastActiveTime,
          minutesAgo
        }
      })
      
      // 按在线状态和最后活跃时间排序
      onlineAdmins.sort((a, b) => {
        if (a.status !== b.status) {
          return a.status === 'online' ? -1 : 1
        }
        return new Date(b.lastActiveTime) - new Date(a.lastActiveTime)
      })
      
      return {
        admins: onlineAdmins,
        summary: {
          total: onlineAdmins.length,
          online: onlineAdmins.filter(a => a.status === 'online').length,
          away: onlineAdmins.filter(a => a.status === 'away').length
        },
        lastUpdated: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`获取在线管理员失败: ${error.message}`)
    }
  },

  /**
   * 发送实时消息
   */
  async sendMessage(data, db, cloud, admin) {
    const { type, title, content, targetAdmins, priority = 'normal' } = data
    
    if (!type || !content) {
      throw new Error('消息类型和内容不能为空')
    }
    
    try {
      const messageData = {
        type, // 'notification', 'alert', 'system', 'broadcast'
        title: title || '系统消息',
        content,
        priority, // 'low', 'normal', 'high', 'urgent'
        targetAdmins: targetAdmins || ['all'],
        senderInfo: {
          id: admin._id,
          name: admin.profile?.name || admin.username,
          role: admin.role
        },
        status: 'sent',
        createTime: db.serverDate(),
        createTimestamp: Date.now(),
        readBy: [], // 已读用户列表
        metadata: {
          channel: 'admin_dashboard',
          source: 'manual'
        }
      }
      
      const result = await db.collection('realtime_messages').add({
        data: messageData
      })
      
      // 在实际实现中，这里应该通过WebSocket或其他实时通信方式推送消息
      // 由于云函数的限制，这里简化处理
      
      return {
        messageId: result._id,
        status: 'sent',
        message: '消息发送成功',
        sentAt: new Date().toISOString(),
        targetCount: targetAdmins === 'all' ? '所有管理员' : targetAdmins.length
      }
    } catch (error) {
      throw new Error(`发送消息失败: ${error.message}`)
    }
  },

  /**
   * 获取实时消息列表
   */
  async getMessages(data, db, cloud, admin) {
    const { page = 1, limit = 20, type, unreadOnly = false } = data
    
    try {
      const skip = (page - 1) * limit
      let query = db.collection('realtime_messages')
      
      // 构建查询条件
      const whereConditions = {
        // 只获取发给当前管理员或所有人的消息
        targetAdmins: db.command.or([
          db.command.eq(['all']),
          db.command.in([admin._id])
        ])
      }
      
      if (type) {
        whereConditions.type = type
      }
      
      if (unreadOnly) {
        whereConditions.readBy = db.command.not(db.command.in([admin._id]))
      }
      
      query = query.where(whereConditions)
      
      const [countResult, dataResult] = await Promise.all([
        query.count(),
        query.skip(skip).limit(limit).orderBy('createTime', 'desc').get()
      ])
      
      const messages = dataResult.data.map(message => ({
        id: message._id,
        type: message.type,
        title: message.title,
        content: message.content,
        priority: message.priority,
        senderInfo: message.senderInfo,
        createTime: message.createTime,
        isRead: message.readBy?.includes(admin._id) || false,
        metadata: message.metadata
      }))
      
      return {
        messages,
        pagination: {
          page,
          limit,
          total: countResult.total,
          pages: Math.ceil(countResult.total / limit)
        },
        unreadCount: unreadOnly ? countResult.total : null
      }
    } catch (error) {
      throw new Error(`获取消息列表失败: ${error.message}`)
    }
  },

  /**
   * 标记消息为已读
   */
  async markMessageRead(data, db, cloud, admin) {
    const { messageId } = data
    
    if (!messageId) {
      throw new Error('消息ID不能为空')
    }
    
    try {
      // 更新消息的已读状态
      await db.collection('realtime_messages').doc(messageId).update({
        data: {
          readBy: db.command.addToSet(admin._id), // 添加到已读列表，避免重复
          lastReadTime: db.serverDate()
        }
      })
      
      return {
        message: '消息已标记为已读'
      }
    } catch (error) {
      throw new Error(`标记消息已读失败: ${error.message}`)
    }
  },

  /**
   * 获取系统活动流
   */
  async getActivityStream(data, db, cloud, admin) {
    const { limit = 50, lastTimestamp } = data
    
    try {
      let query = db.collection('logs')
      
      // 如果提供了最后时间戳，获取之后的记录（用于增量更新）
      if (lastTimestamp) {
        query = query.where({
          createTimestamp: db.command.gt(lastTimestamp)
        })
      }
      
      const logsResult = await query
        .limit(limit)
        .orderBy('createTime', 'desc')
        .get()
      
      const activities = logsResult.data.map(log => ({
        id: log._id,
        type: 'operation',
        action: log.action,
        description: this.formatActivityDescription(log.action, log.adminName),
        actor: {
          id: log.adminId,
          name: log.adminName,
          type: 'admin'
        },
        result: log.result,
        timestamp: log.createTime,
        timestampMs: log.timestamp
      }))
      
      return {
        activities,
        hasMore: logsResult.data.length === limit,
        lastTimestamp: activities.length > 0 ? activities[0].timestampMs : null,
        lastUpdated: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`获取活动流失败: ${error.message}`)
    }
  },

  /**
   * 格式化活动描述
   */
  formatActivityDescription(action, adminName) {
    const actionMap = {
      'auth.login': `${adminName} 登录了系统`,
      'auth.logout': `${adminName} 退出了系统`,
      'ai.createModel': `${adminName} 创建了新的AI模型`,
      'ai.updateModel': `${adminName} 更新了AI模型配置`,
      'ai.testModel': `${adminName} 测试了AI模型`,
      'data.exportData': `${adminName} 导出了数据`,
      'data.importData': `${adminName} 导入了数据`,
      'data.backupData': `${adminName} 执行了数据备份`,
      'system.createAdmin': `${adminName} 创建了新管理员`,
      'system.updateSettings': `${adminName} 更新了系统设置`,
      'system.sendNotification': `${adminName} 发送了系统通知`
    }
    
    return actionMap[action] || `${adminName} 执行了 ${action} 操作`
  },

  /**
   * 获取实时性能指标
   */
  async getPerformanceMetrics(data, db, cloud, admin) {
    try {
      // 获取最近5分钟的操作日志，用于计算性能指标
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
      const recentLogs = await db.collection('operation_logs').where({
        createTime: db.command.gte(fiveMinutesAgo)
      }).get()
      
      // 计算性能指标
      const totalOperations = recentLogs.data.length
      const successfulOperations = recentLogs.data.filter(log => log.result === 'success').length
      const failedOperations = totalOperations - successfulOperations
      
      // 模拟系统资源使用情况
      const metrics = {
        operations: {
          total: totalOperations,
          successful: successfulOperations,
          failed: failedOperations,
          successRate: totalOperations > 0 ? ((successfulOperations / totalOperations) * 100).toFixed(2) : 100
        },
        system: {
          // 实际环境中，这些指标应该从系统监控服务获取
          cpuUsage: Math.floor(Math.random() * 30) + 20, // 20-50%
          memoryUsage: Math.floor(Math.random() * 20) + 40, // 40-60%
          diskUsage: Math.floor(Math.random() * 10) + 30, // 30-40%
          networkLatency: Math.floor(Math.random() * 50) + 20 // 20-70ms
        },
        database: {
          connections: Math.floor(Math.random() * 10) + 5, // 5-15个连接
          queryTime: Math.floor(Math.random() * 100) + 50, // 50-150ms
          activeQueries: Math.floor(Math.random() * 5) + 1 // 1-6个查询
        },
        timestamp: new Date().toISOString(),
        interval: '5m'
      }
      
      return metrics
    } catch (error) {
      throw new Error(`获取性能指标失败: ${error.message}`)
    }
  }
}