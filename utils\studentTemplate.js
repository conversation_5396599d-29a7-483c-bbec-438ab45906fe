/**
 * 学生批量导入模板工具
 */

/**
 * 生成Excel模板数据
 */
function generateExcelTemplate() {
  return {
    headers: [
      '姓名*',
      '学号*',
      '班级*',
      '性别',
      '联系电话',
      '备注'
    ],
    sampleData: [
      ['张小明', '2023001', '三年级一班', '男', '13800138001', '学习认真'],
      ['李小红', '2023002', '三年级一班', '女', '13800138002', '活泼开朗'],
      ['王小华', '2023003', '三年级二班', '男', '13800138003', '积极向上']
    ],
    instructions: [
      '1. 请按照表头格式填写学生信息',
      '2. 带*号的字段为必填项',
      '3. 班级名称必须与系统中已有班级完全一致',
      '4. 性别请填写"男"或"女"',
      '5. 联系电话请填写11位手机号码',
      '6. 单次最多导入100名学生',
      '7. 学号不能重复'
    ]
  };
}

/**
 * 生成CSV模板数据
 */
function generateCSVTemplate() {
  const template = generateExcelTemplate();
  
  // 转换为CSV格式
  const csvHeaders = template.headers.join(',');
  const csvData = template.sampleData.map(row => row.join(',')).join('\n');
  const csvInstructions = template.instructions.map((item, index) => `# ${item}`).join('\n');
  
  return {
    content: `${csvInstructions}\n\n${csvHeaders}\n${csvData}`,
    filename: '学生导入模板.csv'
  };
}

/**
 * 验证导入数据
 */
function validateImportData(data) {
  const errors = [];
  const validData = [];
  
  data.forEach((row, index) => {
    const rowNumber = index + 1;
    const student = {
      name: row[0]?.trim(),
      studentNumber: row[1]?.trim(),
      className: row[2]?.trim(),
      gender: row[3]?.trim(),
      phone: row[4]?.trim(),
      remark: row[5]?.trim() || '',
      hasError: false,
      errors: []
    };
    
    // 验证必填项
    if (!student.name) {
      student.errors.push('姓名不能为空');
      student.hasError = true;
    }
    
    if (!student.studentNumber) {
      student.errors.push('学号不能为空');
      student.hasError = true;
    }
    
    if (!student.className) {
      student.errors.push('班级不能为空');
      student.hasError = true;
    }
    
    // 验证格式
    if (student.gender && !['男', '女'].includes(student.gender)) {
      student.errors.push('性别只能填写"男"或"女"');
      student.hasError = true;
    }

    if (student.phone && !isValidPhone(student.phone)) {
      student.errors.push('联系电话格式错误');
      student.hasError = true;
    }
    
    // 验证学号重复
    const duplicateIndex = validData.findIndex(item => 
      item.studentNumber === student.studentNumber && !item.hasError
    );
    if (duplicateIndex !== -1) {
      student.errors.push(`学号与第${duplicateIndex + 1}行重复`);
      student.hasError = true;
    }
    
    validData.push(student);
  });
  
  return {
    data: validData,
    validCount: validData.filter(item => !item.hasError).length,
    errorCount: validData.filter(item => item.hasError).length
  };
}



/**
 * 验证手机号格式
 */
function isValidPhone(phone) {
  const regex = /^1[3-9]\d{9}$/;
  return regex.test(phone);
}

/**
 * 获取支持的班级列表
 */
function getSupportedClasses() {
  return [
    '三年级一班',
    '三年级二班',
    '四年级一班',
    '四年级二班',
    '五年级一班',
    '五年级二班'
  ];
}

module.exports = {
  generateExcelTemplate,
  generateCSVTemplate,
  validateImportData,
  getSupportedClasses
};
