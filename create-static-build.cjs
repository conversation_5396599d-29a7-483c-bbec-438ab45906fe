const fs = require('fs')
const path = require('path')

console.log('🚀 创建完全兼容的静态构建...')

// 1. 读取构建的JS文件
const distDir = path.join(__dirname, 'dist-static')
const assetsDir = path.join(distDir, 'assets')

if (!fs.existsSync(distDir)) {
  console.error('❌ 请先运行构建: npx vite build --config vite.config.static.ts')
  process.exit(1)
}

// 找到JS文件
const files = fs.readdirSync(assetsDir)
const jsFile = files.find(f => f.endsWith('.js'))

if (!jsFile) {
  console.error('❌ 未找到JS文件')
  process.exit(1)
}

console.log(`📁 找到JS文件: ${jsFile}`)

// 2. 读取JS内容
const jsPath = path.join(assetsDir, jsFile)
let jsContent = fs.readFileSync(jsPath, 'utf8')

console.log(`📊 JS文件大小: ${(jsContent.length / 1024).toFixed(2)} KB`)

// 3. 创建完全兼容的HTML
const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评语灵感君管理后台</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f0f2f5;
            color: #333;
        }
        
        #root {
            min-height: 100vh;
        }
        
        /* 加载动画 */
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f0f2f5;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-content {
            text-align: center;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e6f7ff;
            border-top: 4px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: #666;
            font-size: 14px;
        }
        
        /* 隐藏加载动画的类 */
        .loaded .loading-container {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 加载动画 -->
    <div class="loading-container">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载评语灵感君管理后台...</div>
        </div>
    </div>
    
    <!-- React应用容器 -->
    <div id="root"></div>
    
    <!-- 内联JavaScript，避免模块加载问题 -->
    <script>
        // 设置全局变量
        window.__PINGYU_ADMIN__ = true;
        
        // 应用加载完成后隐藏加载动画
        function hideLoading() {
            document.body.classList.add('loaded');
        }
        
        // 错误处理
        window.addEventListener('error', function(e) {
            console.error('应用加载错误:', e.error);
            document.querySelector('.loading-text').textContent = '应用加载失败，请刷新页面重试';
        });
        
        // 延迟隐藏加载动画，确保React渲染完成
        setTimeout(hideLoading, 2000);
    </script>
    
    <!-- 主应用代码 -->
    <script>
${jsContent}
    </script>
    
    <!-- 确保应用启动 -->
    <script>
        // 如果React没有正确渲染，显示错误信息
        setTimeout(function() {
            const root = document.getElementById('root');
            if (!root.children.length) {
                root.innerHTML = '<div style="padding: 40px; text-align: center; color: #666;">应用启动失败，请检查浏览器控制台</div>';
            }
            hideLoading();
        }, 5000);
    </script>
</body>
</html>`

// 4. 创建最终部署版本
const finalDir = path.join(__dirname, 'dist-final')
if (!fs.existsSync(finalDir)) {
  fs.mkdirSync(finalDir)
}

fs.writeFileSync(path.join(finalDir, 'index.html'), htmlContent, 'utf8')

console.log('✅ 创建最终部署版本')
console.log(`📁 部署目录: ${finalDir}`)
console.log(`📋 部署文件: index.html (单文件，包含所有代码)`)
console.log(`📊 文件大小: ${(htmlContent.length / 1024).toFixed(2)} KB`)

console.log('\n🎉 构建完成！')
console.log('💡 使用方法:')
console.log('1. 直接打开 dist-final/index.html')
console.log('2. 或将 dist-final/index.html 上传到微信云开发')
console.log('3. 无需其他文件，单文件包含所有功能')

console.log('\n🔧 测试建议:')
console.log('1. 双击打开 dist-final/index.html')
console.log('2. 如果正常显示，说明构建成功')
console.log('3. 可以直接部署到任何静态网站托管服务')
