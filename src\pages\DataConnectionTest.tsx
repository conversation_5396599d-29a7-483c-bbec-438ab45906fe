/**
 * 数据连接测试页面
 * 用于测试管理后台与小程序数据库的连通性
 */

import React, { useState } from 'react'
import { Card, Button, Alert, Descriptions, Badge, Space, Typography, Divider, Spin } from 'antd'
import { 
  DatabaseOutlined, 
  WifiOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  ReloadOutlined,
  BugOutlined
} from '@ant-design/icons'
import { dataService } from '../services/index'
import { getDashboardStats, getRecentActivities, getSystemMetrics } from '../services/dashboardApi'

const { Title, Text, Paragraph } = Typography

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message: string
  data?: any
  duration?: number
}

const DataConnectionTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [overallStatus, setOverallStatus] = useState<'pending' | 'success' | 'error'>('pending')

  const tests = [
    {
      name: 'API服务健康检查',
      test: async () => {
        const result = await dataService.healthCheck()
        return { success: true, message: 'API服务健康检查成功', data: result }
      }
    },
    {
      name: '数据库连接测试',
      test: async () => {
        const result = await dataService.testConnection()
        return { success: true, message: '数据库连接成功', data: result }
      }
    },
    {
      name: '仪表板数据获取',
      test: async () => {
        const stats = await getDashboardStats()
        return { success: true, message: '仪表板数据获取成功', data: stats }
      }
    },
    {
      name: '活动记录获取',
      test: async () => {
        const activities = await getRecentActivities(5)
        return { success: true, message: `获取到 ${activities.length} 条活动记录`, data: activities }
      }
    },
    {
      name: '系统性能指标',
      test: async () => {
        const metrics = await getSystemMetrics()
        return { success: true, message: '系统性能指标获取成功', data: metrics }
      }
    }
  ]

  const runAllTests = async () => {
    setIsRunning(true)
    setTestResults([])
    setOverallStatus('pending')

    const results: TestResult[] = []
    let hasError = false

    for (const test of tests) {
      const startTime = Date.now()
      
      // 添加进行中的状态
      const pendingResult: TestResult = {
        name: test.name,
        status: 'pending',
        message: '测试中...'
      }
      results.push(pendingResult)
      setTestResults([...results])

      try {
        console.log(`🧪 开始测试: ${test.name}`)
        const result = await test.test()
        const duration = Date.now() - startTime

        // 更新为成功状态
        const successResult: TestResult = {
          name: test.name,
          status: 'success',
          message: result.message,
          data: result.data,
          duration
        }
        results[results.length - 1] = successResult
        setTestResults([...results])

        console.log(`✅ 测试成功: ${test.name} (${duration}ms)`)

      } catch (error: any) {
        hasError = true
        const duration = Date.now() - startTime

        // 更新为错误状态
        const errorResult: TestResult = {
          name: test.name,
          status: 'error',
          message: error.message || '测试失败',
          duration
        }
        results[results.length - 1] = errorResult
        setTestResults([...results])

        console.error(`❌ 测试失败: ${test.name} (${duration}ms)`, error)
      }

      // 每个测试之间稍微延迟
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    setOverallStatus(hasError ? 'error' : 'success')
    setIsRunning(false)
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      case 'pending':
        return <Spin size="small" />
      default:
        return null
    }
  }

  const getOverallStatusBadge = () => {
    switch (overallStatus) {
      case 'success':
        return <Badge status="success" text="连接正常" />
      case 'error':
        return <Badge status="error" text="连接异常" />
      case 'pending':
        return <Badge status="processing" text="未测试" />
    }
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <Title level={2} className="flex items-center gap-3">
          <DatabaseOutlined />
          数据连接测试
        </Title>
        <Paragraph className="text-gray-600">
          测试管理后台与小程序数据库的连通性，验证数据桥接服务是否正常工作。
        </Paragraph>
      </div>

      {/* 总体状态 */}
      <Card className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <WifiOutlined className="text-2xl text-blue-500" />
            <div>
              <Title level={4} className="mb-1">连接状态</Title>
              {getOverallStatusBadge()}
            </div>
          </div>
          
          <Space>
            <Button
              type="primary"
              icon={<BugOutlined />}
              onClick={runAllTests}
              loading={isRunning}
              size="large"
            >
              {isRunning ? '测试中...' : '开始测试'}
            </Button>
          </Space>
        </div>
      </Card>

      {/* 测试结果 */}
      {testResults.length > 0 && (
        <Card 
          title={
            <div className="flex items-center gap-2">
              <ReloadOutlined />
              <span>测试结果</span>
              <Badge count={testResults.filter(r => r.status === 'success').length} showZero color="green" />
              <Badge count={testResults.filter(r => r.status === 'error').length} showZero color="red" />
            </div>
          }
          className="mb-6"
        >
          <div className="space-y-4">
            {testResults.map((result, index) => (
              <div key={index}>
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <Text strong>{result.name}</Text>
                      <div className="text-sm text-gray-600">{result.message}</div>
                    </div>
                  </div>
                  
                  {result.duration && (
                    <Badge count={`${result.duration}ms`} color="blue" />
                  )}
                </div>

                {result.data && result.status === 'success' && (
                  <div className="mt-2 p-3 bg-green-50 rounded border-l-4 border-green-400">
                    <Text className="text-sm text-gray-600">测试数据:</Text>
                    <pre className="text-xs mt-1 overflow-auto max-h-32">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}

                {index < testResults.length - 1 && <Divider />}
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 帮助信息 */}
      <Card title="故障排除指南">
        <div className="space-y-3">
          <Alert
            message="如果测试失败，请检查以下项目："
            type="info"
            showIcon
          />
          
          <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
            <li>确认云函数 <code>adminAPI</code> 已正确部署并设置了HTTP触发器</li>
            <li>检查云函数的环境配置是否正确（环境ID: cloud1-4g85f8xlb8166ff1）</li>
            <li>验证云数据库权限设置，确保云函数可以访问数据集合</li>
            <li>检查网络连接，确保可以访问腾讯云服务</li>
            <li>查看浏览器控制台和网络面板的错误信息</li>
          </ul>
          
          <Alert
            message="成功连接后，管理后台将显示小程序的真实数据"
            type="success"
            showIcon
          />
        </div>
      </Card>
    </div>
  )
}

export default DataConnectionTest