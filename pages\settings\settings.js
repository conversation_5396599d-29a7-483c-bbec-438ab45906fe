/**
 * 智能设置页面
 * 整合个人中心和设置功能，符合PRD2.0要求
 */
const app = getApp();
const { showLoading, hideLoading, showSuccess, showError, showConfirm } = require('../../utils/globalUtils');
const { DataSyncHelper } = require('./sync-helper');
const {
  DEFAULT_USER_INFO,
  STORAGE_KEYS,
  APP_INFO,
  TIMING,
  AI_CONFIG
} = require('../../utils/constants.js');
const {
  getUserInfo,
  saveUserInfo,
  getAvatarText,
  updateAvatarText,
  getDisplayName
} = require('../../utils/userInfoHelper.js');
const { dataService } = require('../../utils/cacheManager.js');

// 安全获取云服务实例
function getCloudService() {
  try {
    // 优先从全局获取
    if (typeof global !== 'undefined' && global.getCloudService) {
      return global.getCloudService();
    }

    // 备用方案：从app获取
    const cloudService = app.globalData.cloudService;
    if (cloudService) {
      return cloudService;
    }

    // 如果都没有，返回null，让调用方处理
    return null;
  } catch (error) {
    console.error('获取云服务失败:', error);
    return null;
  }
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: DEFAULT_USER_INFO,

    // 使用统计
    stats: {
      totalComments: 0,
      totalStudents: 0,
      totalClasses: 0,
      timeSaved: 0,
      qualityScore: 0
    },

    // AI使用统计
    usageStats: {
      usedCount: 0,
      totalLimit: 20,
      remaining: 20,
      percentage: 0
    },

    // AI配置
    aiConfig: {
      apiKey: '',
      model: 'doubao-pro-4k',
      temperature: 0.7,
      maxTokens: 1000,
      customPrompts: {
        warm: '',
        formal: '',
        encouraging: '',
        detailed: ''
      }
    },

    // 系统设置
    systemSettings: {
      // 云端功能（默认开启，用户不可见）
      autoSync: true,
      dataEncryption: true,

      // 用户可控制的设置
      notifications: true,        // 消息通知

      // 界面设置（保留）
      darkMode: false,
      fontSize: 'medium',         // small, medium, large
      language: 'zh-CN'
    },

    // 加载状态
    loading: false,
    syncing: false,

    // iOS风格弹窗数据
    showBackupModal: false,
    backupItems: [
      {
        id: 'user',
        icon: '👤',
        title: '用户个人信息',
        subtitle: '账户设置和偏好配置',
        checked: true
      },
      {
        id: 'students',
        icon: '👨‍🎓',
        title: '学生档案数据',
        subtitle: '学生基本信息和成长记录',
        checked: true
      },
      {
        id: 'classes',
        icon: '🏫',
        title: '班级管理信息',
        subtitle: '班级结构和管理配置',
        checked: true
      },
      {
        id: 'comments',
        icon: '💬',
        title: '评语历史记录',
        subtitle: '所有生成的评语内容',
        checked: true
      },
      {
        id: 'records',
        icon: '📝',
        title: '行为记录数据',
        subtitle: '学生行为观察和记录',
        checked: true
      },
      {
        id: 'settings',
        icon: '⚙️',
        title: '系统设置配置',
        subtitle: '个性化设置和偏好',
        checked: true
      }
    ],
    backupFeatures: [
      '完整数据打包，支持一键恢复',
      '本地安全存储，可导出分享',
      '预计用时 3-10 秒（取决于数据量）'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    try {
      // 初始化数据同步助手
      this.syncHelper = new DataSyncHelper();

      // 分步加载，便于定位问题
      this.loadUserDataWithSync();
      this.loadSettings();
      this.loadStats();
      this.setupAutoSync();
    } catch (error) {
      console.error('设置页面加载失败:', error);
      // 显示用户友好的错误信息
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    }
  },

  // 注意：getAvatarText 和 parseNameForAvatar 函数已移至 userInfoHelper.js 工具文件中

  /**
   * 安全获取当前页面路由信息
   */
  getCurrentRoute() {
    try {
      const pages = getCurrentPages();
      if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        return currentPage.route || 'unknown';
      }
      return 'unknown';
    } catch (error) {
      return 'unknown';
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.refreshStats();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理自动同步定时器
    this.clearAutoSync();
  },

  /**
   * 【新增】带同步功能的用户数据加载
   */
  async loadUserDataWithSync() {
    try {
      wx.showLoading({ title: '加载用户信息...', mask: true });
      
      // 使用同步助手恢复用户信息
      const result = await this.syncHelper.restoreUserInfo();
      
      if (result.success) {
        const userInfo = result.data;
        
        // 计算头像文字（使用工具函数）
        const userInfoWithAvatar = updateAvatarText(userInfo);

        this.setData({ userInfo: userInfoWithAvatar });
        // 根据数据来源显示不同提示
        if (result.source === 'cloud') {
          wx.showToast({
            title: '已从云端恢复数据',
            icon: 'success',
            duration: 2000
          });
        } else if (result.source === 'local') {
        }
      } else {
        throw new Error(result.error);
      }
      
      wx.hideLoading();
    } catch (error) {
      wx.hideLoading();
      console.error('[设置页面] 加载用户数据失败:', error);
      
      // 设置默认用户信息
      this.setData({
        userInfo: DEFAULT_USER_INFO
      });
      
      wx.showToast({
        title: '加载失败，使用默认信息',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 加载用户数据（使用工具函数优化）
   */
  async loadUserData() {
    try {
      // 使用工具函数获取用户信息
      const userInfo = getUserInfo(true);

      // 添加头像文字
      const userInfoWithAvatar = updateAvatarText(userInfo);

      this.setData({ userInfo: userInfoWithAvatar });
    } catch (error) {
      console.error('加载用户数据失败:', error);
      // 设置默认用户信息
      this.setData({
        userInfo: DEFAULT_USER_INFO
      });
    }
  },

  /**
   * 加载设置
   */
  loadSettings() {
    try {
      const savedSettings = wx.getStorageSync('systemSettings') || {};
      const aiConfig = wx.getStorageSync('aiConfig') || {};

      // 设置默认值（云端同步和数据加密默认开启，用户不可见）
      const systemSettings = {
        ...this.data.systemSettings,
        ...savedSettings,
        // 云端功能默认开启（用户不可见）
        autoSync: true,
        dataEncryption: true,
        // 用户可控制的设置
        notifications: savedSettings.notifications !== undefined ? savedSettings.notifications : true
      };

      const aiConfigData = {
        ...this.data.aiConfig,
        ...aiConfig
      };
      this.setData({
        systemSettings,
        aiConfig: aiConfigData
      });
      
      // 如果自动同步已开启，启动定时器（但不显示弹窗）
      if (systemSettings.autoSync) {
        this.clearAutoSync();
        this.autoSyncInterval = setInterval(() => {
          this.syncData();
        }, 30 * 60 * 1000);
      }
      
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  },

  /**
   * 加载统计数据（使用缓存优化）
   */
  async loadStats(forceRefresh = false) {
    try {
      // 优先使用缓存的数据服务
      const stats = await dataService.getStatistics(forceRefresh);
      this.setData({ stats });
    } catch (error) {
      console.error('加载统计数据失败:', error);

      // 尝试使用原有的云服务作为降级方案
      try {
        const cloudService = getCloudService();
        if (cloudService) {
          const statsResult = await cloudService.getUserStats();
          if (statsResult && statsResult.success) {
            this.setData({ stats: statsResult.data });
            return;
          }
        }
      } catch (fallbackError) {
        console.error('降级方案也失败:', fallbackError);
      }

      // 显示空数据
      this.setData({
        stats: {
          totalComments: 0,
          totalStudents: 0,
          totalClasses: 0,
          timeSaved: 0,
          qualityScore: 0
        }
      });
    }
  },

  /**
   * 刷新统计数据（强制刷新缓存）
   */
  async refreshStats() {
    await this.loadStats(true); // 强制刷新
    this.loadUsageStats(); // 加载AI使用统计
  },

  /**
   * 保存系统设置
   */
  saveSystemSettings() {
    try {
      wx.setStorageSync('systemSettings', this.data.systemSettings);
      showSuccess('设置已保存');
    } catch (error) {
      console.error('保存设置失败:', error);
      showError('保存失败');
    }
  },

  /**
   * 保存AI配置
   */
  saveAIConfig() {
    try {
      wx.setStorageSync('aiConfig', this.data.aiConfig);
      showSuccess('AI配置已保存');
    } catch (error) {
      console.error('保存AI配置失败:', error);
      showError('保存失败');
    }
  },

  /**
   * 编辑用户信息
   */
  editUserInfo() {
    wx.showActionSheet({
      itemList: ['修改姓名', '更换头像'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.editUserName();
        } else if (res.tapIndex === 1) {
          this.changeAvatar();
        }
      }
    });
  },

  /**
   * 编辑用户姓名
   */
  editUserName() {
    wx.showModal({
      title: '修改姓名',
      editable: true,
      placeholderText: '请输入姓名',
      content: this.data.userInfo.name || '',
      success: (res) => {
        if (res.confirm && res.content && res.content.trim()) {
          const newName = res.content.trim();

          // 更新用户信息（使用工具函数）
          const updatedUserInfo = updateAvatarText({
            ...this.data.userInfo,
            name: newName
          });

          this.setData({
            userInfo: updatedUserInfo
          });

          // 保存到本地存储
          saveUserInfo(updatedUserInfo);

          // 【新增】使用同步助手保存
          this.syncHelper.syncUserInfo(updatedUserInfo);
          // 立即通知其他页面用户信息已更新
          this.notifyUserInfoUpdate(updatedUserInfo);

          wx.showToast({
            title: '姓名修改成功',
            icon: 'success'
          });

          // 触发数据同步
          if (this.data.systemSettings.autoSync) {
            this.syncData();
          }
        } else if (res.confirm && !res.content.trim()) {
          wx.showToast({
            title: '姓名不能为空',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 更换头像
   */
  changeAvatar() {
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: (res) => {
        let sourceType = [];
        if (res.tapIndex === 0) {
          sourceType = ['camera'];
        } else if (res.tapIndex === 1) {
          sourceType = ['album'];
        }

        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: sourceType,
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];
            this.uploadAvatar(tempFilePath);
          },
          fail: (err) => {
            console.error('选择图片失败:', err);
            wx.showToast({
              title: '选择图片失败',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  /**
   * 上传头像
   */
  uploadAvatar(tempFilePath) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    try {
      // 生成头像文件名
      const timestamp = Date.now();
      const fileName = `avatar_${timestamp}.jpg`;
      const savedPath = `${wx.env.USER_DATA_PATH}/${fileName}`;

      // 保存图片到本地
      const fs = wx.getFileSystemManager();
      const imageData = fs.readFileSync(tempFilePath);
      fs.writeFileSync(savedPath, imageData);

      // 更新用户信息
      const updatedUserInfo = {
        ...this.data.userInfo,
        avatar: savedPath,
        avatarUrl: tempFilePath // 临时显示用
      };

      // 更新头像文字（虽然有头像了，但保持数据一致性）
      const userInfoWithAvatar = updateAvatarText(updatedUserInfo);

      this.setData({
        userInfo: userInfoWithAvatar
      });

      // 【新增】使用同步助手保存
      this.syncHelper.syncUserInfo(updatedUserInfo);
      // 立即通知其他页面用户信息已更新
      this.notifyUserInfoUpdate(updatedUserInfo);

      wx.hideLoading();
      wx.showToast({
        title: '头像更新成功',
        icon: 'success'
      });

      // 触发数据同步
      if (this.data.systemSettings.autoSync) {
        this.syncData();
      }

    } catch (error) {
      wx.hideLoading();
      console.error('头像上传失败:', error);
      wx.showToast({
        title: '头像上传失败',
        icon: 'none'
      });
    }
  },

  /**
   * 头像加载错误处理
   */
  onAvatarError(e) {
    // 清除错误的头像路径
    const updatedUserInfo = {
      ...this.data.userInfo,
      avatarUrl: '',
      avatar: ''
    };

    // 更新头像文字
    const userInfoWithAvatar = updateAvatarText(updatedUserInfo);

    this.setData({
      userInfo: userInfoWithAvatar
    });

    // 更新本地存储
    wx.setStorageSync('userInfo', updatedUserInfo);
  },

  /**
   * 通知其他页面用户信息已更新 - 增强版
   */
  notifyUserInfoUpdate(userInfo) {
    try {
      // 方法1: 立即同步到全局App实例
      const app = getApp();
      if (app.globalData) {
        app.globalData.userInfo = userInfo;
        app.globalData.userInfoUpdateTime = Date.now();
      }
      
      // 方法2: 确保本地存储同步
      try {
        wx.setStorageSync('userInfo', userInfo);
      } catch (storageError) {
        console.error('[设置页面] 同步到本地存储失败:', storageError);
      }

      // 方法3: 触发自定义事件（如果有事件总线）
      if (app.eventBus && app.eventBus.emit) {
        app.eventBus.emit('userInfoUpdated', userInfo);
      }

      // 方法4: 使用页面栈通知其他页面 - 改进版
      try {
        const pages = getCurrentPages();
        if (pages && Array.isArray(pages)) {
          pages.forEach((page, index) => {
            try {
              const route = page.route || 'unknown';
              // 特别处理首页
              if (route === 'pages/index/index' && page.onUserInfoUpdate) {
                page.onUserInfoUpdate(userInfo);
                
                // 额外调用首页的强制同步方法
                if (page.forceSyncUserInfo) {
                  setTimeout(() => {
                    page.forceSyncUserInfo();
                  }, 100);
                }
              } else if (page && page.onUserInfoUpdate && typeof page.onUserInfoUpdate === 'function') {
                page.onUserInfoUpdate(userInfo);
              } else {
              }
            } catch (pageError) {
            }
          });
        }
      } catch (pagesError) {
      }
      
      // 方法5: 手机端静默处理
    } catch (error) {
      console.error('[设置页面] 通知用户信息更新失败:', error);
      
      // 手机端错误静默处理
      console.error('[设置页面] 用户信息更新通知失败:', error);
    }
  },

  /**
   * 班级管理
   */
  goToClassManagement() {
    wx.navigateTo({
      url: '/pages/class/list/list'
    });
  },

  /**
   * AI配置管理 - 评语风格偏好设置
   */
  goToAIConfig() {
    const currentStyle = this.data.aiConfig.commentStyle || 'balanced';
    
    wx.showActionSheet({
      itemList: ['温暖亲切', '正式规范', '鼓励激励', '详细具体'],
      success: (res) => {
        const styles = ['warm', 'formal', 'encouraging', 'detailed'];
        const styleNames = ['温暖亲切', '正式规范', '鼓励激励', '详细具体'];
        const selectedStyle = styles[res.tapIndex];
        const selectedName = styleNames[res.tapIndex];
        
        // 更新AI配置
        const updatedAIConfig = {
          ...this.data.aiConfig,
          commentStyle: selectedStyle,
          commentStyleName: selectedName
        };
        
        this.setData({
          aiConfig: updatedAIConfig
        });
        
        // 保存到本地存储
        wx.setStorageSync('aiConfig', updatedAIConfig);
        
        wx.showToast({
          title: `已设置为${selectedName}风格`,
          icon: 'success'
        });
      }
    });
  },

  /**
   * 数据导出管理 - 重新设计，只包含导出相关功能
   */
  showDataManagement() {
    wx.showActionSheet({
      itemList: ['导出所有数据', '导出学生数据', '导出评语记录', '导出班级数据'],
      success: (res) => {
        const actions = [
          'exportAllData',
          'exportStudents', 
          'exportComments',
          'exportClasses'
        ];
        this.handleDataAction(actions[res.tapIndex]);
      }
    });
  },

  /**
   * 处理数据操作
   */
  async handleDataAction(action) {
    switch (action) {
      case 'exportAllData':
        this.exportAllData();
        break;
      case 'exportStudents':
        this.exportStudentData();
        break;
      case 'exportComments':
        this.exportCommentData();
        break;
      case 'exportClasses':
        this.exportClassData();
        break;
    }
  },

  /**
   * 导出所有数据
   */
  async exportAllData() {
    wx.showModal({
      title: '📤 导出所有数据',
      content: `━━━━━━━━━━━━━━━━━━━━━━━━
📊 数据导出功能

📋 导出内容：
   ✅ 用户基本信息
   ✅ 学生档案列表
   ✅ 班级管理数据
   ✅ 评语历史记录
   ✅ 行为记录详情
   ✅ 统计分析数据

📄 导出格式：
   • 结构化文本格式
   • 易于阅读和处理
   • 支持复制分享
   • 兼容多种应用

🔧 使用场景：
   • 数据迁移备份
   • 外部工具分析
   • 报告生成素材
   • 存档保留记录

确定要开始导出吗？
━━━━━━━━━━━━━━━━━━━━━━━━`,
      confirmText: '开始导出',
      cancelText: '暂不导出',
      success: (res) => {
        if (res.confirm) {
          this.performExportAllData();
        }
      }
    });
  },

  /**
   * 执行导出所有数据
   */
  async performExportAllData() {
    wx.showLoading({
      title: '正在导出...',
      mask: true
    });

    try {
      // 获取所有数据
      const userData = {
        userInfo: wx.getStorageSync('userInfo') || {},
        systemSettings: wx.getStorageSync('systemSettings') || {},
        aiConfig: wx.getStorageSync('aiConfig') || {}
      };

      const studentData = wx.getStorageSync('students') || [];
      const classData = wx.getStorageSync('classes') || [];
      const commentData = wx.getStorageSync('comments') || [];

      // 创建导出内容
      const exportData = {
        exportInfo: {
          appName: '评语灵感君',
          version: '3.0.0',
          exportTime: new Date().toLocaleString('zh-CN'),
          dataCount: {
            students: studentData.length,
            classes: classData.length,
            comments: commentData.length
          }
        },
        userData,
        studentData,
        classData,
        commentData
      };

      // 格式化为文本内容
      const content = this.formatExportContent(exportData);

      wx.hideLoading();

      // 提供导出选项
      wx.showActionSheet({
        itemList: ['复制到剪贴板', '生成分享文件'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.copyToClipboard(content, '所有数据');
          } else if (res.tapIndex === 1) {
            this.shareAsFile(content, 'all_data');
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('导出所有数据失败:', error);
      wx.showToast({
        title: '导出失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 导出学生数据
   */
  async exportStudentData() {
    wx.showLoading({
      title: '导出学生数据...',
      mask: true
    });

    try {
      const students = wx.getStorageSync('students') || [];
      
      if (students.length === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '暂无学生数据可导出',
          icon: 'none'
        });
        return;
      }

      // 格式化学生数据
      const content = this.formatStudentData(students);
      
      wx.hideLoading();

      wx.showActionSheet({
        itemList: ['复制到剪贴板', '生成分享文件'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.copyToClipboard(content, '学生数据');
          } else if (res.tapIndex === 1) {
            this.shareAsFile(content, 'students');
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('导出学生数据失败:', error);
      wx.showToast({
        title: '导出失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 导出评语记录
   */
  async exportCommentData() {
    wx.showLoading({
      title: '导出评语记录...',
      mask: true
    });

    try {
      const comments = wx.getStorageSync('comments') || [];
      
      if (comments.length === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '暂无评语记录可导出',
          icon: 'none'
        });
        return;
      }

      // 格式化评语数据
      const content = this.formatCommentData(comments);
      
      wx.hideLoading();

      wx.showActionSheet({
        itemList: ['复制到剪贴板', '生成分享文件'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.copyToClipboard(content, '评语记录');
          } else if (res.tapIndex === 1) {
            this.shareAsFile(content, 'comments');
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('导出评语记录失败:', error);
      wx.showToast({
        title: '导出失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 导出班级数据
   */
  async exportClassData() {
    wx.showLoading({
      title: '导出班级数据...',
      mask: true
    });

    try {
      const classes = wx.getStorageSync('classes') || [];
      
      if (classes.length === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '暂无班级数据可导出',
          icon: 'none'
        });
        return;
      }

      // 格式化班级数据
      const content = this.formatClassData(classes);
      
      wx.hideLoading();

      wx.showActionSheet({
        itemList: ['复制到剪贴板', '生成分享文件'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.copyToClipboard(content, '班级数据');
          } else if (res.tapIndex === 1) {
            this.shareAsFile(content, 'classes');
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('导出班级数据失败:', error);
      wx.showToast({
        title: '导出失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 格式化导出内容（所有数据）
   */
  formatExportContent(exportData) {
    const { exportInfo, userData, studentData, classData, commentData } = exportData;
    
    let content = `=== 评语灵感君 数据导出 ===\n\n`;
    content += `导出时间：${exportInfo.exportTime}\n`;
    content += `应用版本：${exportInfo.version}\n`;
    content += `数据统计：学生 ${exportInfo.dataCount.students} 人，班级 ${exportInfo.dataCount.classes} 个，评语 ${exportInfo.dataCount.comments} 条\n\n`;
    
    // 用户信息
    content += `=== 用户信息 ===\n`;
    content += `姓名：${userData.userInfo.name || '未设置'}\n`;
    content += `昵称：${userData.userInfo.nickName || '未设置'}\n`;
    content += `学校：${userData.userInfo.school || '未设置'}\n`;
    content += `学科：${userData.userInfo.subject || '未设置'}\n\n`;
    
    // 学生数据
    if (studentData.length > 0) {
      content += `=== 学生数据 (${studentData.length}人) ===\n`;
      studentData.forEach((student, index) => {
        content += `${index + 1}. ${student.name || '未命名'} - ${student.className || '未分班'}\n`;
        if (student.studentId) content += `   学号：${student.studentId}\n`;
        if (student.gender) content += `   性别：${student.gender}\n`;
        if (student.birthDate) content += `   生日：${student.birthDate}\n`;
        content += `\n`;
      });
    }
    
    // 班级数据
    if (classData.length > 0) {
      content += `=== 班级数据 (${classData.length}个) ===\n`;
      classData.forEach((cls, index) => {
        content += `${index + 1}. ${cls.name || '未命名班级'}\n`;
        if (cls.grade) content += `   年级：${cls.grade}\n`;
        if (cls.studentCount) content += `   学生人数：${cls.studentCount}人\n`;
        content += `\n`;
      });
    }
    
    // 评语记录
    if (commentData.length > 0) {
      content += `=== 评语记录 (${commentData.length}条) ===\n`;
      commentData.forEach((comment, index) => {
        content += `${index + 1}. ${comment.studentName || '未知学生'}\n`;
        content += `   创建时间：${comment.createTime || '未知'}\n`;
        content += `   评语内容：${comment.content || '无内容'}\n`;
        if (comment.score) content += `   评分：${comment.score}\n`;
        content += `\n`;
      });
    }
    
    content += `=== 导出完成 ===\n`;
    content += `由 评语灵感君 v${exportInfo.version} 生成`;
    
    return content;
  },

  /**
   * 格式化学生数据
   */
  formatStudentData(students) {
    let content = `=== 学生数据导出 ===\n\n`;
    content += `导出时间：${new Date().toLocaleString('zh-CN')}\n`;
    content += `学生总数：${students.length}人\n\n`;
    
    students.forEach((student, index) => {
      content += `${index + 1}. 姓名：${student.name || '未命名'}\n`;
      content += `   班级：${student.className || '未分班'}\n`;
      if (student.studentId) content += `   学号：${student.studentId}\n`;
      if (student.gender) content += `   性别：${student.gender}\n`;
      if (student.birthDate) content += `   生日：${student.birthDate}\n`;
      if (student.phone) content += `   联系电话：${student.phone}\n`;
      if (student.address) content += `   地址：${student.address}\n`;
      content += `\n`;
    });
    
    content += `=== 导出完成 ===\n由 评语灵感君 生成`;
    return content;
  },

  /**
   * 格式化班级数据
   */
  formatClassData(classes) {
    let content = `=== 班级数据导出 ===\n\n`;
    content += `导出时间：${new Date().toLocaleString('zh-CN')}\n`;
    content += `班级总数：${classes.length}个\n\n`;
    
    classes.forEach((cls, index) => {
      content += `${index + 1}. 班级名称：${cls.name || '未命名班级'}\n`;
      if (cls.grade) content += `   年级：${cls.grade}\n`;
      if (cls.studentCount) content += `   学生人数：${cls.studentCount}人\n`;
      if (cls.teacherName) content += `   班主任：${cls.teacherName}\n`;
      if (cls.description) content += `   描述：${cls.description}\n`;
      content += `\n`;
    });
    
    content += `=== 导出完成 ===\n由 评语灵感君 生成`;
    return content;
  },

  /**
   * 格式化评语数据
   */
  formatCommentData(comments) {
    let content = `=== 评语记录导出 ===\n\n`;
    content += `导出时间：${new Date().toLocaleString('zh-CN')}\n`;
    content += `评语总数：${comments.length}条\n\n`;
    
    comments.forEach((comment, index) => {
      content += `${index + 1}. 学生姓名：${comment.studentName || '未知学生'}\n`;
      content += `   创建时间：${comment.createTime || '未知时间'}\n`;
      if (comment.className) content += `   班级：${comment.className}\n`;
      if (comment.score) content += `   评分：${comment.score}\n`;
      if (comment.wordCount) content += `   字数：${comment.wordCount}字\n`;
      content += `   评语内容：\n   ${comment.content || '无内容'}\n`;
      content += `\n`;
    });
    
    content += `=== 导出完成 ===\n由 评语灵感君 生成`;
    return content;
  },

  /**
   * 复制到剪贴板
   */
  copyToClipboard(content, dataType) {
    wx.setClipboardData({
      data: content,
      success: () => {
        wx.showToast({
          title: `${dataType}已复制到剪贴板`,
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 生成分享文件
   */
  shareAsFile(content, fileName) {
    // 由于微信小程序限制，这里暂时使用模拟实现
    // 实际实现可能需要调用云函数来生成文件
    wx.showModal({
      title: '分享文件',
      content: '数据已准备完成！\n\n由于小程序限制，建议使用"复制到剪贴板"功能，然后粘贴到文档应用中保存。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 执行数据备份 - 使用iOS风格弹窗
   */
  async performDataBackup() {
    this.setData({
      showBackupModal: true
    });
  },

  /**
   * 备份弹窗确认
   */
  onBackupConfirm() {
    this.setData({
      showBackupModal: false
    });
    // 延迟一点执行备份，让弹窗关闭动画完成
    setTimeout(() => {
      this.createDataBackup();
    }, 300);
  },

  /**
   * 备份弹窗取消
   */
  onBackupCancel() {
    this.setData({
      showBackupModal: false
    });
  },

  /**
   * 创建数据备份
   */
  async createDataBackup() {
    wx.showLoading({
      title: '正在备份...',
      mask: true
    });

    try {
      // 获取所有数据
      const backupData = {
        backupInfo: {
          appName: '评语灵感君',
          version: '3.0.0',
          backupTime: new Date().toLocaleString('zh-CN'),
          backupId: 'backup_' + Date.now()
        },
        userData: {
          userInfo: wx.getStorageSync('userInfo') || {},
          systemSettings: wx.getStorageSync('systemSettings') || {},
          aiConfig: wx.getStorageSync('aiConfig') || {}
        },
        appData: {
          students: wx.getStorageSync('students') || [],
          classes: wx.getStorageSync('classes') || [],
          comments: wx.getStorageSync('comments') || [],
          records: wx.getStorageSync('records') || []
        }
      };

      // 将备份数据保存到本地存储
      const backupKey = `backup_${Date.now()}`;
      wx.setStorageSync(backupKey, backupData);

      // 同时创建备份列表记录
      let backupList = wx.getStorageSync('backupList') || [];
      backupList.unshift({
        id: backupKey,
        name: `备份_${new Date().toLocaleDateString('zh-CN')}`,
        time: backupData.backupInfo.backupTime,
        dataCount: {
          students: backupData.appData.students.length,
          classes: backupData.appData.classes.length,
          comments: backupData.appData.comments.length,
          records: backupData.appData.records.length
        }
      });

      // 只保留最近10个备份
      if (backupList.length > 10) {
        const removedBackups = backupList.splice(10);
        removedBackups.forEach(backup => {
          wx.removeStorageSync(backup.id);
        });
      }

      wx.setStorageSync('backupList', backupList);

      wx.hideLoading();

      // 提供选项
      wx.showActionSheet({
        itemList: ['查看备份文件', '导出备份数据', '完成'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.showBackupList();
          } else if (res.tapIndex === 1) {
            const content = this.formatBackupContent(backupData);
            this.copyToClipboard(content, '备份数据');
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('数据备份失败:', error);
      wx.showToast({
        title: '备份失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 执行数据恢复
   */
  async performDataRestore() {
    // 获取备份列表
    const backupList = wx.getStorageSync('backupList') || [];
    
    if (backupList.length === 0) {
      wx.showModal({
        title: '暂无备份',
        content: '当前没有可用的备份文件。\n\n您可以：\n1. 先创建数据备份\n2. 或者手动输入备份数据',
        showCancel: true,
        cancelText: '取消',
        confirmText: '手动输入',
        success: (res) => {
          if (res.confirm) {
            this.showManualRestore();
          }
        }
      });
      return;
    }

    // 显示备份列表供选择
    const itemList = backupList.map(backup => 
      `${backup.name} (${backup.time})`
    );

    wx.showActionSheet({
      itemList: ['手动输入备份数据', ...itemList],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.showManualRestore();
        } else {
          const selectedBackup = backupList[res.tapIndex - 1];
          this.restoreFromBackup(selectedBackup.id);
        }
      }
    });
  },

  /**
   * 从备份恢复数据
   */
  async restoreFromBackup(backupId) {
    try {
      const backupData = wx.getStorageSync(backupId);
      
      if (!backupData) {
        wx.showToast({
          title: '备份文件不存在',
          icon: 'none'
        });
        return;
      }

      wx.showModal({
        title: '确认恢复',
        content: `确定要恢复此备份吗？\n\n备份时间：${backupData.backupInfo.backupTime}\n\n这将覆盖当前所有数据！`,
        confirmText: '恢复',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.performRestore(backupData);
          }
        }
      });

    } catch (error) {
      console.error('恢复数据失败:', error);
      wx.showToast({
        title: '恢复失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 执行恢复操作
   */
  async performRestore(backupData) {
    wx.showLoading({
      title: '正在恢复...',
      mask: true
    });

    try {
      // 恢复用户数据
      if (backupData.userData) {
        if (backupData.userData.userInfo) {
          wx.setStorageSync('userInfo', backupData.userData.userInfo);
        }
        if (backupData.userData.systemSettings) {
          wx.setStorageSync('systemSettings', backupData.userData.systemSettings);
        }
        if (backupData.userData.aiConfig) {
          wx.setStorageSync('aiConfig', backupData.userData.aiConfig);
        }
      }

      // 恢复应用数据
      if (backupData.appData) {
        if (backupData.appData.students) {
          wx.setStorageSync('students', backupData.appData.students);
        }
        if (backupData.appData.classes) {
          wx.setStorageSync('classes', backupData.appData.classes);
        }
        if (backupData.appData.comments) {
          wx.setStorageSync('comments', backupData.appData.comments);
        }
        if (backupData.appData.records) {
          wx.setStorageSync('records', backupData.appData.records);
        }
      }

      // 重新加载页面数据
      this.loadUserDataWithSync();
      this.loadSettings();
      this.loadStats();

      // 通知其他页面数据已更新
      const app = getApp();
      if (app.globalData) {
        app.globalData.dataRestored = true;
        app.globalData.lastRestoreTime = Date.now();
      }

      wx.hideLoading();

      wx.showModal({
        title: '恢复成功',
        content: `数据已成功恢复！\n\n恢复的备份：${backupData.backupInfo.backupTime}\n\n建议重启小程序以确保所有功能正常。`,
        showCancel: false,
        confirmText: '知道了'
      });

    } catch (error) {
      wx.hideLoading();
      console.error('恢复操作失败:', error);
      wx.showToast({
        title: '恢复失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 显示手动恢复界面
   */
  showManualRestore() {
    wx.showModal({
      title: '手动恢复数据',
      content: '请将备份数据粘贴到剪贴板，然后点击"从剪贴板恢复"。\n\n备份数据应该是以"=== 评语灵感君 数据导出 ==="开头的文本。',
      confirmText: '从剪贴板恢复',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.restoreFromClipboard();
        }
      }
    });
  },

  /**
   * 从剪贴板恢复数据
   */
  async restoreFromClipboard() {
    try {
      wx.showLoading({
        title: '读取剪贴板...',
        mask: true
      });

      wx.getClipboardData({
        success: (res) => {
          wx.hideLoading();
          const clipboardData = res.data;
          
          if (!clipboardData || !clipboardData.includes('评语灵感君')) {
            wx.showToast({
              title: '剪贴板中无有效备份数据',
              icon: 'none'
            });
            return;
          }

          // 解析剪贴板数据（这是简化的解析，实际可能需要更复杂的逻辑）
          wx.showModal({
            title: '确认恢复',
            content: '检测到剪贴板中的备份数据，确定要恢复吗？\n\n这将覆盖当前所有数据！',
            confirmText: '恢复',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                // 这里可以添加更复杂的数据解析逻辑
                wx.showToast({
                  title: '手动恢复功能开发中',
                  icon: 'none'
                });
              }
            }
          });
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({
            title: '读取剪贴板失败',
            icon: 'none'
          });
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('从剪贴板恢复失败:', error);
      wx.showToast({
        title: '恢复失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 显示备份列表
   */
  showBackupList() {
    const backupList = wx.getStorageSync('backupList') || [];
    
    if (backupList.length === 0) {
      wx.showToast({
        title: '暂无备份文件',
        icon: 'none'
      });
      return;
    }

    let content = '备份文件列表：\n\n';
    backupList.forEach((backup, index) => {
      content += `${index + 1}. ${backup.name}\n`;
      content += `   时间：${backup.time}\n`;
      content += `   数据：学生${backup.dataCount.students}人，班级${backup.dataCount.classes}个，评语${backup.dataCount.comments}条\n\n`;
    });

    wx.showModal({
      title: '备份列表',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 格式化备份内容
   */
  formatBackupContent(backupData) {
    let content = `=== 评语灵感君 数据备份 ===\n\n`;
    content += `备份时间：${backupData.backupInfo.backupTime}\n`;
    content += `备份ID：${backupData.backupInfo.backupId}\n`;
    content += `应用版本：${backupData.backupInfo.version}\n\n`;
    
    // JSON格式的完整备份数据
    content += `=== 备份数据 (JSON格式) ===\n`;
    content += JSON.stringify(backupData, null, 2);
    content += `\n\n=== 备份完成 ===`;
    
    return content;
  },

  /**
   * 备份所有数据
   */
  async backupAllData() {
    const confirmed = await showConfirm('确定要备份所有数据吗？');
    if (!confirmed) return;

    showLoading('备份中...');
    try {
      const cloudService = getCloudService();
      const result = await cloudService.backupAllData();

      hideLoading();
      if (result.success) {
        showSuccess('备份成功');
        this.updateSyncTime();
      } else {
        showError('备份失败');
      }
    } catch (error) {
      hideLoading();
      console.error('备份失败:', error);
      showError('备份失败');
    }
  },

  /**
   * 恢复所有数据
   */
  async restoreAllData() {
    const confirmed = await showConfirm('确定要恢复数据吗？这将覆盖当前数据！');
    if (!confirmed) return;

    showLoading('恢复中...');
    try {
      const cloudService = getCloudService();
      const result = await cloudService.restoreAllData();

      hideLoading();
      if (result.success) {
        showSuccess('恢复成功');
        this.loadStats();
      } else {
        showError('恢复失败');
      }
    } catch (error) {
      hideLoading();
      console.error('恢复失败:', error);
      showError('恢复失败');
    }
  },

  /**
   * 数据同步
   */
  async syncData() {
    this.setData({ syncing: true });

    try {
      const cloudService = getCloudService();
      const result = await cloudService.syncAllData();

      if (result.success) {
        showSuccess('同步成功');
        this.updateSyncTime();
        this.loadStats();
      } else {
        showError('同步失败');
      }
    } catch (error) {
      console.error('同步失败:', error);
      showError('同步失败');
    } finally {
      this.setData({ syncing: false });
    }
  },

  /**
   * 更新同步时间
   */
  updateSyncTime() {
    const now = new Date();
    const timeStr = `${now.getMonth() + 1}-${now.getDate()} ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

    this.setData({
      'systemSettings.lastSyncTime': timeStr
    });

    this.saveSystemSettings();
  },

  /**
   * 设置自动同步（静默启动，用于页面初始化）
   */
  setupAutoSync() {
    // 清除之前的定时器
    this.clearAutoSync();

    if (this.data.systemSettings.autoSync) {
      // 每30分钟自动同步一次
      this.autoSyncInterval = setInterval(() => {
        this.syncData();
      }, 30 * 60 * 1000);
    }
  },

  /**
   * 清除自动同步
   */
  clearAutoSync() {
    if (this.autoSyncInterval) {
      clearInterval(this.autoSyncInterval);
      this.autoSyncInterval = null;
    }
  },


  /**
   * 帮助与反馈
   */
  showHelpAndFeedback() {
    wx.showActionSheet({
      itemList: ['使用帮助', '常见问题', '意见反馈', '联系客服'],
      success: (res) => {
        const actions = ['help', 'faq', 'feedback', 'contact'];
        this.handleHelpAction(actions[res.tapIndex]);
      }
    });
  },

  /**
   * 处理帮助操作
   */
  handleHelpAction(action) {
    switch (action) {
      case 'help':
        wx.navigateTo({
          url: '/pages/help/help'
        });
        break;
      case 'faq':
        wx.navigateTo({
          url: '/pages/faq/faq'
        });
        break;
      case 'feedback':
        wx.navigateTo({
          url: '/pages/feedback/feedback'
        });
        break;
      case 'contact':
        wx.showModal({
          title: '联系客服',
          content: '客服微信：AI-Teacher-Helper\n工作时间：9:00-18:00',
          showCancel: false
        });
        break;
    }
  },

  /**
   * 显示协议与隐私 - 直接跳转到协议页面
   */
  showAgreementAndPrivacy() {
    wx.navigateTo({
      url: '/pages/agreement/agreement'
    });
  },











  /**
   * 显示用户同意记录
   */
  async showConsentRecords() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      const userInfo = wx.getStorageSync('userInfo') || {};
      const userId = userInfo.userId || wx.getStorageSync('userId');

      if (!userId) {
        wx.hideLoading();
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      // 调用云函数查询同意记录
      const result = await new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'getUserConsentRecords',
          data: {
            userId: userId,
            includeHistory: true
          },
          success: resolve,
          fail: reject
        });
      });

      wx.hideLoading();

      if (result.result.success) {
        this.displayConsentRecords(result.result.data);
      } else {
        wx.showToast({
          title: '查询失败：' + result.result.error,
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('[showConsentRecords] 查询同意记录失败:', error);

      wx.showModal({
        title: '查询失败',
        content: `查询同意记录时发生错误：\n${error.message}\n\n这可能是网络问题或服务暂时不可用。`,
        showCancel: false
      });
    }
  },

  /**
   * 显示同意记录详情
   */
  displayConsentRecords(data) {
    const { records, statistics } = data;

    if (records.length === 0) {
      wx.showModal({
        title: '📋 我的同意记录',
        content: '暂无同意记录。\n\n这可能是因为：\n• 您是首次使用本功能\n• 记录正在同步中\n• 系统升级导致的临时问题',
        showCancel: false
      });
      return;
    }

    // 格式化记录信息
    const recordsText = records.slice(0, 5).map((record, index) => {
      const time = new Date(record.consentTime).toLocaleString('zh-CN');
      const typeText = record.consentType === 'both' ? '隐私政策+用户协议' :
                      record.consentType === 'privacy_policy' ? '隐私政策' : '用户协议';
      const statusText = record.isActive ? '✅ 有效' : '⚪ 已失效';

      return `${index + 1}. ${typeText}\n   时间：${time}\n   版本：${record.consentVersion}\n   状态：${statusText}`;
    }).join('\n\n');

    const summaryText = `━━━━━━━━━━━━━━━━━━━━━━━━
📊 统计信息

📋 记录总数：${statistics.totalRecords} 条
✅ 有效记录：${statistics.activeRecords} 条
📅 首次同意：${statistics.firstConsentTime ? new Date(statistics.firstConsentTime).toLocaleString('zh-CN') : '无'}
🕐 最近同意：${statistics.latestConsentTime ? new Date(statistics.latestConsentTime).toLocaleString('zh-CN') : '无'}

━━━━━━━━━━━━━━━━━━━━━━━━
📋 同意记录（最近5条）

${recordsText}

━━━━━━━━━━━━━━━━━━━━━━━━
⚖️ 法律说明

根据《个人信息保护法》要求，我们会保存您的同意记录7年，用于合规举证。这些记录证明了您对我们处理您个人信息的明确同意。

您的权利：
• 随时查看同意记录
• 了解数据处理情况
• 撤回同意（注销账户）`;

    wx.showModal({
      title: '📋 我的同意记录',
      content: summaryText,
      confirmText: '我已了解',
      cancelText: '导出记录',
      success: (res) => {
        if (!res.confirm) {
          // 用户选择导出记录
          this.exportConsentRecords(data);
        }
      }
    });
  },

  /**
   * 导出同意记录
   */
  exportConsentRecords(data) {
    const exportData = {
      exportTime: new Date().toISOString(),
      userInfo: {
        userId: wx.getStorageSync('userId'),
        exportReason: 'user_request'
      },
      records: data.records,
      statistics: data.statistics,
      legalNotice: '本记录根据《个人信息保护法》要求保存，用于证明用户对个人信息处理的明确同意。'
    };

    const content = JSON.stringify(exportData, null, 2);

    wx.setClipboardData({
      data: content,
      success: () => {
        wx.showToast({
          title: '同意记录已复制到剪贴板',
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '导出失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 关于应用
   */
  showAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  /**
   * 设置项变更处理
   */
  onSettingChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail;

    this.setData({
      [`systemSettings.${field}`]: value
    });

    // 根据不同设置项执行相应操作
    this.handleSettingChange(field, value);
    this.saveSystemSettings();
  },

  /**
   * 处理设置变更的具体逻辑
   */
  handleSettingChange(field, value) {
    switch (field) {
      case 'recordNotification':
        this.handleNotificationSetting('记录提醒', value);
        break;
      case 'commentNotification':
        this.handleNotificationSetting('评语提醒', value);
        break;
      case 'achievementNotification':
        this.handleNotificationSetting('成就提醒', value);
        break;
      case 'autoSync':
        this.handleAutoSyncSetting(value);
        break;
      case 'dataBackup':
        this.handleDataBackupSetting(value);
        break;
      case 'dataEncryption':
        this.handleDataEncryptionSetting(value);
        break;
    }
  },

  /**
   * 处理消息提醒设置
   */
  handleNotificationSetting(type, enabled, showToast = true) {
    if (enabled) {
      // 请求通知权限
      wx.requestSubscribeMessage({
        tmplIds: ['notification_template_id'], // 需要替换为实际的模板ID
        success: (res) => {
          if (showToast) {
            wx.showToast({
              title: `${type}已开启`,
              icon: 'success'
            });
          }
        },
        fail: (err) => {
          if (showToast) {
            wx.showToast({
              title: '请在设置中开启通知权限',
              icon: 'none'
            });
          }
        }
      });
    } else {
      if (showToast) {
        wx.showToast({
          title: `${type}已关闭`,
          icon: 'none'
        });
      }
    }
  },

  /**
   * 处理自动同步设置
   */
  handleAutoSyncSetting(enabled) {
    if (enabled) {
      wx.showToast({
        title: '自动同步已开启',
        icon: 'success'
      });
      
      // 启动自动同步（用户手动开启时显示详细信息）
      this.setupAutoSyncWithPrompt();
      
      // 立即执行一次同步
      setTimeout(() => {
        this.syncData();
      }, 1000);
      
    } else {
      wx.showToast({
        title: '自动同步已关闭',
        icon: 'none'
      });
      // 清除定时同步任务
      this.clearAutoSync();
    }
  },

  /**
   * 启动自动同步（带提示）
   */
  setupAutoSyncWithPrompt() {
    // 清除之前的定时器
    this.clearAutoSync();

    // 每30分钟自动同步一次
    this.autoSyncInterval = setInterval(() => {
      this.syncData();
    }, 30 * 60 * 1000);
    // 显示自动同步状态
    setTimeout(() => {
      wx.showModal({
        title: '🔄 自动同步已启用',
        content: `━━━━━━━━━━━━━━━━━━━━━━━━
📱 同步频率：每30分钟自动执行

🛡️ 功能特性：
   ✅ 多设备数据同步
   ✅ 自动备份保护
   ✅ 云端安全存储
   ✅ 数据丢失防护

⚙️ 温馨提示：
   • 首次同步可能需要稍长时间
   • 建议在WiFi环境下使用
   • 可随时在设置中关闭
━━━━━━━━━━━━━━━━━━━━━━━━`,
        showCancel: false,
        confirmText: '我知道了'
      });
    }, 500);
  },

  /**
   * 处理数据备份设置
   */
  handleDataBackupSetting(enabled) {
    if (enabled) {
      wx.showToast({
        title: '数据备份已开启',
        icon: 'success'
      });
      // 执行备份
      this.backupData();
    } else {
      wx.showToast({
        title: '数据备份已关闭',
        icon: 'none'
      });
    }
  },

  /**
   * 处理数据加密设置
   */
  handleDataEncryptionSetting(enabled) {
    if (enabled) {
      wx.showToast({
        title: '数据加密已开启',
        icon: 'success'
      });
      // 这里可以实现具体的加密逻辑
      // 例如：对用户信息、学生数据等进行加密
      this.enableDataEncryption();
      
    } else {
      wx.showModal({
        title: '⚠️ 关闭数据加密',
        content: `━━━━━━━━━━━━━━━━━━━━━━━━
🔓 您即将关闭数据加密保护

⚠️ 风险提示：
   • 用户信息将以明文存储
   • 学生数据失去加密保护
   • 评语内容可能被直接读取
   • 设备丢失时数据易泄露

🛡️ 建议：
   为保护您和学生的隐私安全，
   强烈建议保持加密功能开启

确定要关闭加密保护吗？
━━━━━━━━━━━━━━━━━━━━━━━━`,
        confirmText: '仍要关闭',
        cancelText: '保持开启',
        success: (res) => {
          if (res.confirm) {
            wx.showToast({
              title: '数据加密已关闭',
              icon: 'none'
            });
            this.disableDataEncryption();
          } else {
            // 用户取消，重新开启加密
            this.setData({
              'systemSettings.dataEncryption': true
            });
            this.saveSystemSettings();
          }
        }
      });
    }
  },

  /**
   * 启用数据加密
   */
  enableDataEncryption() {
    try {
      // 设置加密标识
      wx.setStorageSync('dataEncryptionEnabled', true);
      
      // 这里可以添加具体的加密实现
      // 例如：对现有数据进行加密处理
      
      // 显示加密状态提示
      setTimeout(() => {
        wx.showModal({
          title: '🛡️ 数据加密已启用',
          content: `━━━━━━━━━━━━━━━━━━━━━━━━
🔐 加密级别：企业级安全标准

🔒 加密范围：
   ✅ 用户个人信息
   ✅ 学生详细资料
   ✅ 评语内容数据
   ✅ 行为记录信息

🛡️ 安全保障：
   • XOR + Base64 双重加密
   • 32位动态密钥保护
   • 敏感字段自动识别
   • 本地离线加密处理

💡 即使设备丢失或被盗，您的隐私数据也无法被恶意获取
━━━━━━━━━━━━━━━━━━━━━━━━`,
          showCancel: false,
          confirmText: '我知道了'
        });
      }, 500);
      
    } catch (error) {
      console.error('[设置页面] 启用数据加密失败:', error);
      wx.showToast({
        title: '加密设置失败',
        icon: 'none'
      });
    }
  },

  /**
   * 禁用数据加密
   */
  disableDataEncryption() {
    try {
      // 移除加密标识
      wx.removeStorageSync('dataEncryptionEnabled');
      
      // 这里可以添加具体的解密实现
      // 注意：在生产环境中要谨慎处理解密操作
      
      wx.showToast({
        title: '加密保护已关闭',
        icon: 'none'
      });
      
    } catch (error) {
      console.error('[设置页面] 禁用数据加密失败:', error);
      wx.showToast({
        title: '设置修改失败',
        icon: 'none'
      });
    }
  },

  /**
   * AI配置变更处理
   */
  onAIConfigChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value || e.detail;

    this.setData({
      [`aiConfig.${field}`]: value
    });

    this.saveAIConfig();
  },

  // ==================== 通知设置 ====================

  /**
   * 评语通知变更
   */
  onCommentNotificationChange(e) {
    this.setData({
      'systemSettings.commentNotification': e.detail
    });
    this.saveSystemSettings();
  },

  /**
   * 统计通知变更
   */
  onStatsNotificationChange(e) {
    this.setData({
      'systemSettings.statsNotification': e.detail
    });
    this.saveSystemSettings();
  },





  // ==================== 其他功能 ====================

  /**
   * 云端同步 - 增强版
   */
  async syncData() {
    // 检查同步开关
    if (!this.data.systemSettings.autoSync) {
      wx.showToast({
        title: '自动同步已关闭',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '同步中...',
      mask: true
    });

    try {
      // 获取要同步的数据
      const syncData = {
        userInfo: wx.getStorageSync('userInfo') || {},
        systemSettings: wx.getStorageSync('systemSettings') || {},
        aiConfig: wx.getStorageSync('aiConfig') || {},
        students: wx.getStorageSync('students') || [],
        classes: wx.getStorageSync('classes') || [],
        comments: wx.getStorageSync('comments') || [],
        records: wx.getStorageSync('records') || []
      };

      // 尝试云端同步
      let syncResult = { success: false, message: '云服务不可用' };
      
      try {
        const cloudService = getCloudService();
        if (cloudService && cloudService.syncAllData) {
          syncResult = await cloudService.syncAllData(syncData);
        }
      } catch (cloudError) {
        syncResult = { success: false, message: '云端连接失败' };
      }

      // 如果云端同步失败，执行本地备份
      if (!syncResult.success) {
        await this.performLocalBackup(syncData);
        syncResult = { success: true, message: '本地备份完成' };
      }

      // 更新同步时间
      const now = new Date();
      const timeStr = `${now.getMonth() + 1}月${now.getDate()}日 ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

      this.setData({
        'systemSettings.lastSyncTime': timeStr
      });
      this.saveSystemSettings();

      wx.hideLoading();

      if (syncResult.success) {
        wx.showToast({
          title: syncResult.message.includes('本地') ? '本地备份完成' : '同步完成',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: `同步失败: ${syncResult.message}`,
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('数据同步失败:', error);
      wx.showToast({
        title: '同步失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 执行本地备份（作为云端同步的备选方案）
   */
  async performLocalBackup(data) {
    try {
      const backupData = {
        backupInfo: {
          appName: '评语灵感君',
          version: '3.0.0',
          backupTime: new Date().toLocaleString('zh-CN'),
          backupId: 'auto_backup_' + Date.now(),
          type: 'auto_sync_backup'
        },
        userData: {
          userInfo: data.userInfo,
          systemSettings: data.systemSettings,
          aiConfig: data.aiConfig
        },
        appData: {
          students: data.students,
          classes: data.classes,
          comments: data.comments,
          records: data.records
        }
      };

      // 保存自动备份
      const backupKey = backupData.backupInfo.backupId;
      wx.setStorageSync(backupKey, backupData);

      // 更新自动备份列表
      let autoBackups = wx.getStorageSync('autoBackupList') || [];
      autoBackups.unshift({
        id: backupKey,
        time: backupData.backupInfo.backupTime,
        type: 'sync_backup'
      });

      // 只保留最近5个自动备份
      if (autoBackups.length > 5) {
        const removedBackups = autoBackups.splice(5);
        removedBackups.forEach(backup => {
          wx.removeStorageSync(backup.id);
        });
      }

      wx.setStorageSync('autoBackupList', autoBackups);
    } catch (error) {
      console.error('本地备份失败:', error);
      throw error;
    }
  },

  /**
   * 手动触发云端同步
   */
  async manualSyncData() {
    wx.showModal({
      title: '🔄 手动同步',
      content: `━━━━━━━━━━━━━━━━━━━━━━━━
☁️ 即将执行数据同步

🔄 同步流程：
   1️⃣ 检查云端连接状态
   2️⃣ 上传本地数据变更
   3️⃣ 下载云端最新数据
   4️⃣ 合并处理数据冲突
   5️⃣ 更新本地数据副本

📊 同步内容：
   • 用户信息更新
   • 学生档案同步
   • 评语记录备份
   • 设置配置同步

⏱️ 预计耗时：10-30秒
📶 建议在WiFi环境下进行

立即开始同步吗？
━━━━━━━━━━━━━━━━━━━━━━━━`,
      confirmText: '开始同步',
      cancelText: '稍后同步',
      success: (res) => {
        if (res.confirm) {
          this.syncData();
        }
      }
    });
  },

  /**
   * 意见反馈
   */
  showFeedback() {
    wx.showModal({
      title: '💬 意见反馈',
      content: `━━━━━━━━━━━━━━━━━━━━━━━━
📞 联系我们：

📧 邮箱反馈
   <EMAIL>
   （推荐详细描述问题）

💬 微信客服
   chanwarmsun
   （快速响应，工作时间优先）

🕒 服务时间
   周一至周五 9:00-18:00
   周末及节假日延迟回复

💡 反馈建议
   • 详细描述遇到的问题
   • 提供操作步骤和截图
   • 告知设备型号和系统版本
   • 建议改进功能和体验

我们重视每一条反馈，持续优化产品体验！
━━━━━━━━━━━━━━━━━━━━━━━━`,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 使用指南
   */
  goToHelp() {
    wx.showModal({
      title: '📚 使用指南',
      content: `━━━━━━━━━━━━━━━━━━━━━━━━
📱 核心功能导览：

🏠 首页模块
   • 工作概览统计
   • 快捷操作入口
   • 最近活动记录

👥 班级管理
   • 创建班级信息
   • 管理班级学生
   • 批量导入功能

👨‍🎓 学生管理
   • 添加学生档案
   • Excel/CSV批量导入
   • 学生信息编辑

📝 行为记录
   • 记录日常表现
   • 分类标签管理
   • 时间轴展示

💬 AI评语生成
   • 智能评语生成
   • 多种风格选择
   • 个性化定制

📊 数据分析
   • 统计报告生成
   • 趋势分析图表
   • 成长轨迹记录
━━━━━━━━━━━━━━━━━━━━━━━━`,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 显示关于信息
   */
  showAbout() {
    wx.showModal({
      title: '🎉 关于评语灵感君',
      content: `━━━━━━━━━━━━━━━━━━━━━━━━
📱 评语灵感君 v1.0
最专业的【免费】AI评语生成工具

🎯 产品愿景
让每一句评语都充满温度，
让每一位教师都能轻松生成个性化评语

✨ 核心功能
   🤖 AI智能评语生成
   👨‍🎓 学生信息管理系统
   📝 行为记录追踪
   🎨 多风格评语模板
   📊 数据统计分析
   🔒 企业级安全加密
   ☁️ 云端同步备份

🏆 产品特色
   • 基于真实行为记录的AI分析
   • 支持4种专业评语风格
   • 完整的学生成长档案
   • 一键导出分享功能

━━━━━━━━━━━━━━━━━━━━━━━━
© 2025 评语灵感君团队 | v1.0`,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 【新增】手动同步数据到云端
   */
  async manualSyncToCloud() {
    try {
      wx.showLoading({ title: '正在同步到云端...', mask: true });
      
      const currentUserInfo = this.data.userInfo;
      const result = await this.syncHelper.saveToCloud(currentUserInfo);
      
      wx.hideLoading();
      
      if (result.success) {
        wx.showModal({
          title: '同步成功',
          content: '用户信息已成功同步到云端。\\n现在在任何设备上都能看到最新的信息！',
          showCancel: false,
          confirmText: '知道了'
        });
      } else {
        wx.showModal({
          title: '同步失败',
          content: '无法连接到云端，请检查网络连接后重试。',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('[设置页面] 手动同步失败:', error);
      wx.showModal({
        title: '同步失败',
        content: `同步过程中出现错误：${error.message}`,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },

  /**
   * 【新增】从云端恢复数据
   */
  async restoreFromCloud() {
    try {
      const result = await wx.showModal({
        title: '确认恢复',
        content: '确定要从云端恢复用户信息吗？这将覆盖当前的本地数据。',
        cancelText: '取消',
        confirmText: '恢复'
      });

      if (!result.confirm) return;

      wx.showLoading({ title: '正在从云端恢复...', mask: true });
      
      const cloudResult = await this.syncHelper.loadFromCloud();
      
      wx.hideLoading();
      
      if (cloudResult.success) {
        const userInfo = cloudResult.data;
        
        // 计算头像文字
        const userInfoWithAvatar = updateAvatarText(userInfo);

        this.setData({ userInfo: userInfoWithAvatar });
        
        // 保存到本地
        wx.setStorageSync('userInfo', userInfo);
        getApp().globalData.userInfo = userInfo;
        
        // 通知其他页面
        this.notifyUserInfoUpdate(userInfo);
        
        wx.showModal({
          title: '恢复成功',
          content: '用户信息已从云端成功恢复！',
          showCancel: false,
          confirmText: '知道了'
        });
      } else {
        wx.showModal({
          title: '恢复失败',
          content: '云端暂无备份数据或网络连接失败。',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('[设置页面] 云端恢复失败:', error);
      wx.showModal({
        title: '恢复失败',
        content: `恢复过程中出现错误：${error.message}`,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },

  // ==================== 新增UI功能方法 ====================

  /**
   * 跳转到报告页面
   */
  goToReport() {
    wx.navigateTo({
      url: '/pages/analytics/report/report'
    });
  },

  /**
   * 打开通知设置
   */
  openNotificationSettings() {
    wx.showActionSheet({
      itemList: ['记录提醒', '评语提醒', '成就提醒', '统计提醒'],
      success: (res) => {
        const settingKeys = ['recordNotification', 'commentNotification', 'achievementNotification', 'statsNotification'];
        const currentKey = settingKeys[res.tapIndex];
        const currentValue = this.data.systemSettings[currentKey];
        
        this.setData({
          [`systemSettings.${currentKey}`]: !currentValue
        });
        
        this.handleNotificationSetting(res.tapIndex === 0 ? '记录提醒' : 
                                      res.tapIndex === 1 ? '评语提醒' : 
                                      res.tapIndex === 2 ? '成就提醒' : '统计提醒', 
                                      !currentValue);
        this.saveSystemSettings();
      }
    });
  },

  /**
   * 打开自动同步设置
   */
  openAutoSyncSettings() {
    const currentAutoSync = this.data.systemSettings.autoSync;
    
    wx.showModal({
      title: '自动同步设置',
      content: `当前状态：${currentAutoSync ? '已开启' : '已关闭'}\n\n自动同步可以确保您的数据在多设备间保持同步，建议开启。`,
      confirmText: currentAutoSync ? '关闭' : '开启',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'systemSettings.autoSync': !currentAutoSync
          });
          this.handleAutoSyncSetting(!currentAutoSync);
          this.saveSystemSettings();
        }
      }
    });
  },

  /**
   * 消息通知开关切换
   */
  toggleNotifications(e) {
    const currentValue = this.data.systemSettings.notifications;
    const newValue = !currentValue;
    this.setData({
      'systemSettings.notifications': newValue
    });

    // 个性化设置中的消息通知开关不显示toast提示
    // 用户可以通过开关的视觉状态直接看到变化，更加流畅

    this.saveSystemSettings();

    // 如果需要处理通知权限，可以静默处理
    if (newValue) {
      // 静默请求通知权限，不显示toast
      this.handleNotificationSetting('消息通知', newValue, false);
    }
  },



  /**
   * 注销账号
   */
  deleteAccount() {
    wx.showModal({
      title: '⚠️ 注销账号',
      content: '注销后将永久删除：\n\n• 所有学生信息\n• 所有评语记录\n• 所有使用数据\n• 个人设置配置\n\n此操作不可恢复！',
      confirmText: '确认注销',
      confirmColor: '#FF4444',
      success: (res) => {
        if (res.confirm) {
          this.confirmDeleteAccount();
        }
      }
    });
  },

  /**
   * 确认注销账号
   */
  async confirmDeleteAccount() {
    wx.showLoading({ title: '注销中...', mask: true });

    try {
      // 1. 获取用户ID用于云端数据清理
      const userInfo = wx.getStorageSync('userInfo') || {};
      const userId = userInfo.userId || wx.getStorageSync('userId');

      // 2. 清理云端数据
      const cloudService = getCloudService();
      if (cloudService && userId) {
        try {
          // 调用云函数清理用户所有数据
          const clearResult = await cloudService.clearAllUserData(userId);

          if (clearResult && clearResult.success) {
          } else {
          }
        } catch (cloudError) {
          console.error('[注销账号] 云端数据清理异常:', cloudError);
          // 即使云端清理失败，也继续本地清理
        }
      }

      // 3. 清理本地存储的所有数据
      // 获取所有存储的key
      const storageInfo = wx.getStorageInfoSync();
      const allKeys = storageInfo.keys || [];

      // 逐个删除所有存储项
      allKeys.forEach(key => {
        try {
          wx.removeStorageSync(key);
        } catch (error) {
        }
      });

      // 4. 清理本地文件（如头像等）
      try {
        const fs = wx.getFileSystemManager();
        const userDataPath = wx.env.USER_DATA_PATH;

        // 获取用户数据目录下的所有文件
        const files = fs.readdirSync(userDataPath);
        files.forEach(fileName => {
          try {
            const filePath = `${userDataPath}/${fileName}`;
            fs.unlinkSync(filePath);
          } catch (fileError) {
          }
        });
      } catch (fsError) {
      }

      // 5. 清理全局数据
      const app = getApp();
      if (app.globalData) {
        app.globalData.userInfo = null;
        app.globalData.isLoggedIn = false;
        app.globalData.cloudService = null;
      }

      // 6. 等待一段时间确保所有操作完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      wx.hideLoading();

      // 7. 显示注销完成提示
      wx.showModal({
        title: '✅ 注销完成',
        content: `━━━━━━━━━━━━━━━━━━━━━━━━
🗑️ 数据清理完成

✅ 已清理的数据：
   • 用户个人信息
   • 学生档案数据
   • 班级管理信息
   • 评语历史记录
   • 行为记录数据
   • 系统设置配置
   • 本地缓存文件
   • 云端存储数据

🔒 隐私保护：
   您的所有数据已被安全删除，
   无法恢复，隐私得到完全保护。

感谢您使用评语灵感君！
━━━━━━━━━━━━━━━━━━━━━━━━`,
        showCancel: false,
        confirmText: '知道了',
        success: () => {
          // 返回首页并重新初始化
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('[注销账号] 注销过程发生错误:', error);

      wx.showModal({
        title: '⚠️ 注销异常',
        content: `注销过程中遇到问题：\n\n${error.message || '未知错误'}\n\n建议：\n1. 检查网络连接\n2. 重试注销操作\n3. 或联系客服协助处理`,
        confirmText: '重试',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 重试注销
            this.confirmDeleteAccount();
          }
        }
      });
    }
  },

  /**
   * 运行安全测试（开发调试用）
   */
  runSecurityTest() {
    console.log('🔒 开始运行安全测试...');
    
    const testResults = [];
    
    // 辅助函数
    const addTestResult = (category, testName, passed, details) => {
      testResults.push({ category, testName, passed, details });
      const status = passed ? '✅' : '❌';
      console.log(`${status} [${category}] ${testName}: ${details}`);
    };

    // 用户ID验证
    const validateUserId = (userId) => {
      if (!userId || typeof userId !== 'string') return false;
      if (userId.startsWith('test_user_')) return false;
      if (userId.length < 10) return false;
      return true;
    };

    try {
      // 测试1: 用户身份验证
      const currentUserId = this.data.userInfo.userId || wx.getStorageSync('userId');
      const userIdValid = validateUserId(currentUserId);
      addTestResult('AUTH', 'CurrentUserValidation', userIdValid, 
        `当前用户ID: ${currentUserId ? currentUserId.substring(0, 8) + '...' : '无'}`);

      // 测试2: 危险ID拦截
      const dangerousIds = ['test_user_123', 'admin', '', null];
      const dangerousBlocked = dangerousIds.every(id => !validateUserId(id));
      addTestResult('AUTH', 'DangerousIdBlocking', dangerousBlocked, '危险用户ID被正确拦截');

      // 测试3: 本地存储安全
      try {
        wx.setStorageSync('test_security', 'test_value');
        const testValue = wx.getStorageSync('test_security');
        const storageWorking = testValue === 'test_value';
        wx.removeStorageSync('test_security');
        addTestResult('STORAGE', 'LocalStorageSecurity', storageWorking, '本地存储安全测试通过');
      } catch (storageError) {
        addTestResult('STORAGE', 'LocalStorageSecurity', false, `存储测试失败: ${storageError.message}`);
      }

      // 测试4: 云服务连接
      const cloudService = getCloudService();
      const cloudAvailable = cloudService !== null;
      addTestResult('CLOUD', 'CloudServiceConnection', cloudAvailable, 
        cloudAvailable ? '云服务连接正常' : '云服务暂不可用');

      // 测试5: 页面权限
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const pageAccessValid = currentPage && currentPage.route === 'pages/settings/settings';
      addTestResult('PAGE', 'PageAccess', pageAccessValid, `页面访问: ${currentPage ? currentPage.route : '未知'}`);

      // 生成报告
      const totalTests = testResults.length;
      const passedTests = testResults.filter(r => r.passed).length;
      const passRate = Math.round((passedTests / totalTests) * 100);

      console.log('\n📊 安全测试报告:');
      console.log(`总测试: ${totalTests}, 通过: ${passedTests}, 通过率: ${passRate}%`);

      // 显示测试结果给用户
      wx.showModal({
        title: '🔒 安全测试完成',
        content: `测试结果：\n\n总测试数：${totalTests}\n通过测试：${passedTests}\n通过率：${passRate}%\n\n${passRate >= 90 ? '🎉 安全性优秀' : passRate >= 70 ? '⚠️ 安全性良好' : '🚨 需要改进'}\n\n详细结果请查看控制台`,
        showCancel: false,
        confirmText: '知道了'
      });

    } catch (error) {
      console.error('安全测试执行失败:', error);
      wx.showToast({
        title: '测试失败，请查看控制台',
        icon: 'none'
      });
    }
  },

  /**
   * 防止事件冒泡
   */
  preventBubble(e) {
    // 阻止事件冒泡，避免触发卡片点击事件
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  },

  // ==================== 隐私安全功能 ====================

  /**
   * 显示数据加密信息
   */
  showDataEncryptionInfo() {
    wx.showModal({
      title: '数据加密保护',
      content: '🛡️ 数据安全保障\n\n✅ 本地数据已启用加密保护\n✅ 用户隐私信息加密存储\n✅ 评语内容本地加密\n✅ 传输过程HTTPS加密\n\n我们采用行业标准的加密算法，确保您的数据安全。所有敏感信息都经过加密处理，即使设备丢失也无法被恶意获取。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    const privacyContent = `🔒 隐私保护政策

我们非常重视您的隐私保护，特制定本隐私政策：

📋 数据收集
• 仅收集应用功能必需的信息
• 学生信息仅用于评语生成
• 不收集与功能无关的个人信息

🛡️ 数据安全
• 所有数据采用加密存储
• 数据仅保存在您的设备本地
• 云端同步采用端到端加密

📱 数据使用
• 数据仅用于AI评语生成功能
• 不会向第三方分享您的数据
• 不用于商业推广或营销

🔄 数据控制
• 您可随时导出自己的数据
• 可随时删除所有本地数据
• 拥有数据的完全控制权

💡 权限说明
• 存储权限：保存评语和设置
• 网络权限：AI生成和云端同步
• 不申请不必要的设备权限

📞 联系我们
如有隐私相关问题，请联系：
邮箱：<EMAIL>
微信：chanwarmsun

最后更新：2025年8月`;

    wx.showModal({
      title: '隐私政策',
      content: privacyContent,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 加载AI使用统计
   */
  loadUsageStats() {
    try {
      // 导入使用控制器
      const { usageController } = require('../../utils/usageController');
      
      // 获取使用统计
      const checkResult = usageController.canUseAI();
      const percentage = Math.round((checkResult.usedCount / checkResult.totalLimit) * 100);
      
      this.setData({
        usageStats: {
          usedCount: checkResult.usedCount,
          totalLimit: checkResult.totalLimit,
          remaining: checkResult.remaining,
          percentage: percentage
        }
      });
    } catch (error) {
      console.error('加载使用统计失败:', error);
      // 设置默认值
      this.setData({
        usageStats: {
          usedCount: 0,
          totalLimit: 20,
          remaining: 20,
          percentage: 0
        }
      });
    }
  }


});