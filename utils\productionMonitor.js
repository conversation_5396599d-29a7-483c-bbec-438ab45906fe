/**
 * 生产环境监控系统
 * 实时监控系统状态，及时发现和处理问题
 */

class ProductionMonitor {
  constructor() {
    this.config = {
      // 监控间隔
      monitorInterval: 30000, // 30秒
      alertInterval: 60000,   // 1分钟
      
      // 告警阈值
      thresholds: {
        // 性能阈值
        responseTime: 5000,     // 响应时间 > 5秒
        errorRate: 0.05,        // 错误率 > 5%
        memoryUsage: 100,       // 内存使用 > 100MB
        
        // 业务阈值
        concurrentUsers: 300,   // 并发用户 > 300
        aiCallQueue: 50,        // AI调用队列 > 50
        failedLogins: 10,       // 登录失败 > 10次/分钟
        
        // 系统阈值
        cloudFunctionErrors: 5, // 云函数错误 > 5次/分钟
        databaseErrors: 3,      // 数据库错误 > 3次/分钟
        networkErrors: 10       // 网络错误 > 10次/分钟
      },
      
      // 告警配置
      alertConfig: {
        enableEmail: false,     // 邮件告警（需要配置）
        enableWechat: true,     // 微信告警
        enableConsole: true,    // 控制台告警
        enableStorage: true     // 本地存储告警记录
      }
    };
    
    // 监控数据
    this.metrics = {
      performance: [],
      errors: [],
      business: [],
      system: []
    };
    
    // 告警记录
    this.alerts = [];
    
    // 用户会话跟踪
    this.userSessions = new Map();
    
    // 启动监控
    this.startMonitoring();
  }

  /**
   * 启动监控系统
   */
  startMonitoring() {
    console.log('🔍 生产环境监控系统启动');
    
    // 性能监控
    setInterval(() => {
      this.collectPerformanceMetrics();
    }, this.config.monitorInterval);
    
    // 错误监控
    setInterval(() => {
      this.collectErrorMetrics();
    }, this.config.monitorInterval);
    
    // 业务监控
    setInterval(() => {
      this.collectBusinessMetrics();
    }, this.config.monitorInterval);
    
    // 系统监控
    setInterval(() => {
      this.collectSystemMetrics();
    }, this.config.monitorInterval);
    
    // 告警检查
    setInterval(() => {
      this.checkAlerts();
    }, this.config.alertInterval);
    
    // 数据清理
    setInterval(() => {
      this.cleanupOldData();
    }, 300000); // 每5分钟清理一次
  }

  /**
   * 收集性能指标
   */
  async collectPerformanceMetrics() {
    try {
      const metrics = {
        timestamp: Date.now(),
        memoryUsage: this.getMemoryUsage(),
        responseTime: await this.measureResponseTime(),
        pageLoadTime: this.getPageLoadTime(),
        cloudFunctionTime: await this.measureCloudFunctionTime()
      };
      
      this.metrics.performance.push(metrics);
      
      // 检查性能阈值
      this.checkPerformanceThresholds(metrics);
      
    } catch (error) {
      console.error('性能指标收集失败:', error);
    }
  }

  /**
   * 收集错误指标
   */
  collectErrorMetrics() {
    try {
      const now = Date.now();
      const oneMinuteAgo = now - 60000;
      
      // 从错误日志中统计最近1分钟的错误
      const recentErrors = this.getRecentErrors(oneMinuteAgo);
      
      const metrics = {
        timestamp: now,
        totalErrors: recentErrors.length,
        errorRate: this.calculateErrorRate(recentErrors),
        errorTypes: this.categorizeErrors(recentErrors),
        criticalErrors: recentErrors.filter(e => e.level === 'critical').length
      };
      
      this.metrics.errors.push(metrics);
      
      // 检查错误阈值
      this.checkErrorThresholds(metrics);
      
    } catch (error) {
      console.error('错误指标收集失败:', error);
    }
  }

  /**
   * 收集业务指标
   */
  async collectBusinessMetrics() {
    try {
      const metrics = {
        timestamp: Date.now(),
        activeUsers: this.getActiveUserCount(),
        aiCallQueue: await this.getAICallQueueSize(),
        loginAttempts: this.getLoginAttempts(),
        commentGenerated: this.getCommentGenerationCount(),
        userRetention: this.calculateUserRetention()
      };
      
      this.metrics.business.push(metrics);
      
      // 检查业务阈值
      this.checkBusinessThresholds(metrics);
      
    } catch (error) {
      console.error('业务指标收集失败:', error);
    }
  }

  /**
   * 收集系统指标
   */
  async collectSystemMetrics() {
    try {
      const metrics = {
        timestamp: Date.now(),
        cloudFunctionStatus: await this.checkCloudFunctionStatus(),
        databaseStatus: await this.checkDatabaseStatus(),
        networkStatus: this.checkNetworkStatus(),
        storageUsage: await this.getStorageUsage()
      };
      
      this.metrics.system.push(metrics);
      
      // 检查系统阈值
      this.checkSystemThresholds(metrics);
      
    } catch (error) {
      console.error('系统指标收集失败:', error);
    }
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    try {
      const performance = wx.getPerformance();
      if (performance && performance.memory) {
        return Math.round(performance.memory.usedJSHeapSize / 1024 / 1024); // MB
      }
    } catch (error) {
      console.warn('无法获取内存使用情况:', error);
    }
    return 0;
  }

  /**
   * 测量响应时间
   */
  async measureResponseTime() {
    const startTime = Date.now();
    
    try {
      await wx.cloud.callFunction({
        name: 'getUserId',
        data: { healthCheck: true }
      });
      
      return Date.now() - startTime;
    } catch (error) {
      return -1; // 表示调用失败
    }
  }

  /**
   * 获取页面加载时间
   */
  getPageLoadTime() {
    try {
      const performance = wx.getPerformance();
      if (performance && performance.timing) {
        return performance.timing.loadEventEnd - performance.timing.navigationStart;
      }
    } catch (error) {
      console.warn('无法获取页面加载时间:', error);
    }
    return 0;
  }

  /**
   * 测量云函数响应时间
   */
  async measureCloudFunctionTime() {
    const functions = ['getStudents', 'getStatistics'];
    const times = [];
    
    for (const funcName of functions) {
      const startTime = Date.now();
      try {
        await wx.cloud.callFunction({
          name: funcName,
          data: { healthCheck: true }
        });
        times.push(Date.now() - startTime);
      } catch (error) {
        times.push(-1);
      }
    }
    
    const validTimes = times.filter(t => t > 0);
    return validTimes.length > 0 ? Math.round(validTimes.reduce((sum, t) => sum + t, 0) / validTimes.length) : -1;
  }

  /**
   * 获取最近错误
   */
  getRecentErrors(since) {
    // 这里需要从实际的错误日志系统获取数据
    // 简化实现
    return [];
  }

  /**
   * 计算错误率
   */
  calculateErrorRate(errors) {
    // 简化实现
    return errors.length / 100; // 假设基数为100
  }

  /**
   * 错误分类
   */
  categorizeErrors(errors) {
    const categories = {
      network: 0,
      cloudFunction: 0,
      database: 0,
      ai: 0,
      other: 0
    };
    
    errors.forEach(error => {
      if (error.type) {
        categories[error.type] = (categories[error.type] || 0) + 1;
      } else {
        categories.other++;
      }
    });
    
    return categories;
  }

  /**
   * 获取活跃用户数
   */
  getActiveUserCount() {
    const now = Date.now();
    const fiveMinutesAgo = now - 5 * 60 * 1000;
    
    let activeCount = 0;
    for (const [userId, session] of this.userSessions.entries()) {
      if (session.lastActivity > fiveMinutesAgo) {
        activeCount++;
      }
    }
    
    return activeCount;
  }

  /**
   * 获取AI调用队列大小
   */
  async getAICallQueueSize() {
    try {
      // 这里需要从AI调用优化器获取队列大小
      // 简化实现
      return 0;
    } catch (error) {
      return -1;
    }
  }

  /**
   * 获取登录尝试次数
   */
  getLoginAttempts() {
    // 简化实现
    return 0;
  }

  /**
   * 获取评语生成数量
   */
  getCommentGenerationCount() {
    // 简化实现
    return 0;
  }

  /**
   * 计算用户留存率
   */
  calculateUserRetention() {
    // 简化实现
    return 0.8; // 80%
  }

  /**
   * 检查云函数状态
   */
  async checkCloudFunctionStatus() {
    const functions = ['login', 'getUserId', 'callDoubaoAPI', 'getStudents'];
    const status = {};
    
    for (const funcName of functions) {
      try {
        const startTime = Date.now();
        await wx.cloud.callFunction({
          name: funcName,
          data: { healthCheck: true }
        });
        status[funcName] = {
          status: 'healthy',
          responseTime: Date.now() - startTime
        };
      } catch (error) {
        status[funcName] = {
          status: 'error',
          error: error.message
        };
      }
    }
    
    return status;
  }

  /**
   * 检查数据库状态
   */
  async checkDatabaseStatus() {
    try {
      const db = wx.cloud.database();
      const startTime = Date.now();
      
      await db.collection('users').limit(1).get();
      
      return {
        status: 'healthy',
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    try {
      const networkType = wx.getNetworkType();
      return {
        status: 'healthy',
        networkType: networkType
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * 获取存储使用情况
   */
  async getStorageUsage() {
    try {
      const storageInfo = wx.getStorageInfoSync();
      return {
        currentSize: storageInfo.currentSize,
        limitSize: storageInfo.limitSize,
        keys: storageInfo.keys.length
      };
    } catch (error) {
      return {
        currentSize: 0,
        limitSize: 0,
        keys: 0
      };
    }
  }

  /**
   * 检查性能阈值
   */
  checkPerformanceThresholds(metrics) {
    const { thresholds } = this.config;
    
    if (metrics.memoryUsage > thresholds.memoryUsage) {
      this.triggerAlert('performance', 'high_memory_usage', {
        current: metrics.memoryUsage,
        threshold: thresholds.memoryUsage,
        message: `内存使用过高: ${metrics.memoryUsage}MB`
      });
    }
    
    if (metrics.responseTime > thresholds.responseTime) {
      this.triggerAlert('performance', 'slow_response', {
        current: metrics.responseTime,
        threshold: thresholds.responseTime,
        message: `响应时间过长: ${metrics.responseTime}ms`
      });
    }
  }

  /**
   * 检查错误阈值
   */
  checkErrorThresholds(metrics) {
    const { thresholds } = this.config;
    
    if (metrics.errorRate > thresholds.errorRate) {
      this.triggerAlert('error', 'high_error_rate', {
        current: metrics.errorRate,
        threshold: thresholds.errorRate,
        message: `错误率过高: ${Math.round(metrics.errorRate * 100)}%`
      });
    }
    
    if (metrics.criticalErrors > 0) {
      this.triggerAlert('error', 'critical_errors', {
        current: metrics.criticalErrors,
        message: `发现 ${metrics.criticalErrors} 个严重错误`
      });
    }
  }

  /**
   * 检查业务阈值
   */
  checkBusinessThresholds(metrics) {
    const { thresholds } = this.config;
    
    if (metrics.activeUsers > thresholds.concurrentUsers) {
      this.triggerAlert('business', 'high_concurrent_users', {
        current: metrics.activeUsers,
        threshold: thresholds.concurrentUsers,
        message: `并发用户数过高: ${metrics.activeUsers}`
      });
    }
    
    if (metrics.aiCallQueue > thresholds.aiCallQueue) {
      this.triggerAlert('business', 'ai_queue_backlog', {
        current: metrics.aiCallQueue,
        threshold: thresholds.aiCallQueue,
        message: `AI调用队列积压: ${metrics.aiCallQueue}`
      });
    }
  }

  /**
   * 检查系统阈值
   */
  checkSystemThresholds(metrics) {
    // 检查云函数状态
    Object.keys(metrics.cloudFunctionStatus).forEach(funcName => {
      const status = metrics.cloudFunctionStatus[funcName];
      if (status.status === 'error') {
        this.triggerAlert('system', 'cloud_function_error', {
          function: funcName,
          error: status.error,
          message: `云函数 ${funcName} 异常: ${status.error}`
        });
      }
    });
    
    // 检查数据库状态
    if (metrics.databaseStatus.status === 'error') {
      this.triggerAlert('system', 'database_error', {
        error: metrics.databaseStatus.error,
        message: `数据库异常: ${metrics.databaseStatus.error}`
      });
    }
  }

  /**
   * 触发告警
   */
  triggerAlert(category, type, details) {
    const alert = {
      id: this.generateAlertId(),
      category,
      type,
      details,
      timestamp: Date.now(),
      level: this.getAlertLevel(category, type),
      resolved: false
    };
    
    this.alerts.push(alert);
    
    // 发送告警
    this.sendAlert(alert);
    
    // 记录告警
    if (this.config.alertConfig.enableStorage) {
      this.saveAlert(alert);
    }
  }

  /**
   * 获取告警级别
   */
  getAlertLevel(category, type) {
    const criticalTypes = ['critical_errors', 'database_error', 'high_memory_usage'];
    const warningTypes = ['high_error_rate', 'slow_response', 'ai_queue_backlog'];
    
    if (criticalTypes.includes(type)) {
      return 'critical';
    } else if (warningTypes.includes(type)) {
      return 'warning';
    } else {
      return 'info';
    }
  }

  /**
   * 发送告警
   */
  sendAlert(alert) {
    const { alertConfig } = this.config;
    
    if (alertConfig.enableConsole) {
      this.sendConsoleAlert(alert);
    }
    
    if (alertConfig.enableWechat) {
      this.sendWechatAlert(alert);
    }
    
    if (alertConfig.enableEmail) {
      this.sendEmailAlert(alert);
    }
  }

  /**
   * 控制台告警
   */
  sendConsoleAlert(alert) {
    const emoji = alert.level === 'critical' ? '🚨' : alert.level === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`${emoji} [${alert.level.toUpperCase()}] ${alert.details.message}`);
  }

  /**
   * 微信告警
   */
  sendWechatAlert(alert) {
    // 这里可以实现微信群机器人告警
    console.log('微信告警:', alert.details.message);
  }

  /**
   * 邮件告警
   */
  sendEmailAlert(alert) {
    // 这里可以实现邮件告警
    console.log('邮件告警:', alert.details.message);
  }

  /**
   * 保存告警记录
   */
  saveAlert(alert) {
    try {
      const alerts = wx.getStorageSync('monitor_alerts') || [];
      alerts.push(alert);
      
      // 只保留最近100条告警
      if (alerts.length > 100) {
        alerts.splice(0, alerts.length - 100);
      }
      
      wx.setStorageSync('monitor_alerts', alerts);
    } catch (error) {
      console.error('保存告警记录失败:', error);
    }
  }

  /**
   * 清理旧数据
   */
  cleanupOldData() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    
    // 清理旧的监控数据
    Object.keys(this.metrics).forEach(category => {
      this.metrics[category] = this.metrics[category].filter(
        metric => now - metric.timestamp < maxAge
      );
    });
    
    // 清理旧的告警
    this.alerts = this.alerts.filter(
      alert => now - alert.timestamp < maxAge
    );
    
    // 清理过期的用户会话
    for (const [userId, session] of this.userSessions.entries()) {
      if (now - session.lastActivity > 30 * 60 * 1000) { // 30分钟
        this.userSessions.delete(userId);
      }
    }
  }

  /**
   * 生成告警ID
   */
  generateAlertId() {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 记录用户活动
   */
  recordUserActivity(userId) {
    this.userSessions.set(userId, {
      lastActivity: Date.now()
    });
  }

  /**
   * 获取监控报告
   */
  getMonitoringReport() {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;
    
    // 最近1小时的数据
    const recentMetrics = {
      performance: this.metrics.performance.filter(m => m.timestamp > oneHourAgo),
      errors: this.metrics.errors.filter(m => m.timestamp > oneHourAgo),
      business: this.metrics.business.filter(m => m.timestamp > oneHourAgo),
      system: this.metrics.system.filter(m => m.timestamp > oneHourAgo)
    };
    
    const recentAlerts = this.alerts.filter(a => a.timestamp > oneHourAgo);
    
    return {
      timestamp: now,
      period: '1小时',
      metrics: recentMetrics,
      alerts: recentAlerts,
      summary: {
        totalAlerts: recentAlerts.length,
        criticalAlerts: recentAlerts.filter(a => a.level === 'critical').length,
        activeUsers: this.getActiveUserCount(),
        systemHealth: this.calculateSystemHealth(recentMetrics)
      }
    };
  }

  /**
   * 计算系统健康度
   */
  calculateSystemHealth(metrics) {
    let healthScore = 100;
    
    // 基于各种指标计算健康度
    if (metrics.errors.length > 0) {
      const avgErrorRate = metrics.errors.reduce((sum, m) => sum + m.errorRate, 0) / metrics.errors.length;
      healthScore -= avgErrorRate * 100;
    }
    
    if (metrics.performance.length > 0) {
      const avgResponseTime = metrics.performance.reduce((sum, m) => sum + m.responseTime, 0) / metrics.performance.length;
      if (avgResponseTime > this.config.thresholds.responseTime) {
        healthScore -= 10;
      }
    }
    
    return Math.max(0, Math.round(healthScore));
  }
}

// 创建全局监控实例
const productionMonitor = new ProductionMonitor();

module.exports = {
  ProductionMonitor,
  productionMonitor
};
