{"name": "pingyu-admin-new", "version": "2.0.0", "description": "评语灵感君管理后台 - 现代化重构版", "private": true, "type": "module", "dependencies": {"@ant-design/icons": "^5.2.6", "@cloudbase/js-sdk": "^2.19.2", "@cloudbase/node-sdk": "^3.10.1", "antd": "^5.12.8", "axios": "^1.6.5", "classnames": "^2.3.2", "cors": "^2.8.5", "dayjs": "^1.11.10", "echarts": "^5.4.3", "express": "^4.21.2", "http-proxy-middleware": "^2.0.9", "lodash-es": "^4.17.21", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "recharts": "^3.1.0", "swr": "^2.2.4", "wx-server-sdk": "^3.0.1", "xlsx": "^0.18.5", "zustand": "^4.4.7"}, "devDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.46.1", "@types/lodash-es": "^4.17.12", "@types/node": "^22.0.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.1", "@welldone-software/why-did-you-render": "^8.0.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "rollup": "^4.46.1", "tailwindcss": "^3.4.0", "terser": "^5.43.1", "typescript": "^5.6.3", "vite": "^5.4.8", "vite-bundle-analyzer": "^0.7.0"}, "scripts": {"dev": "vite --config vite.config.fixed.ts --port 8080 --host", "build": "vite build --config vite.config.ts", "preview": "vite preview --port 8080", "start": "npm run dev", "start:clean": "node check-and-fix-deps.js && npm run dev", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "typecheck": "tsc --noEmit"}, "engines": {"node": ">=18.0.0"}}