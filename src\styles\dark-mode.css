/**
 * 全局暗黑模式样式
 * 确保所有组件在暗黑模式下都有良好的可读性和视觉效果
 */

/* ==========================================================================
   全局暗黑模式变量
   ========================================================================== */

[data-theme="dark"] {
  /* 背景色系 */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-quaternary: #475569;
  
  /* 文字色系 - 确保高对比度 */
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
  --text-quaternary: #94a3b8;
  --text-disabled: #64748b;
  
  /* 边框色系 */
  --border-primary: #475569;
  --border-secondary: #64748b;
  --border-light: #334155;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.5);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.5), 0 2px 4px -2px rgb(0 0 0 / 0.5);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.5);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.5), 0 8px 10px -6px rgb(0 0 0 / 0.5);
  
  /* 状态色系 */
  --success-bg: #064e3b;
  --success-text: #6ee7b7;
  --warning-bg: #78350f;
  --warning-text: #fbbf24;
  --error-bg: #7f1d1d;
  --error-text: #fca5a5;
  --info-bg: #164e63;
  --info-text: #67e8f9;
}

/* ==========================================================================
   Ant Design 组件暗黑模式覆盖
   ========================================================================== */

/* 卡片组件 */
[data-theme="dark"] .ant-card {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-card-head {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-card-head-title {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-card-body {
  color: var(--text-primary) !important;
}

/* 表格组件 */
[data-theme="dark"] .ant-table {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-table-thead > tr > th {
  background: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .ant-table-tbody > tr > td {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-light) !important;
}

[data-theme="dark"] .ant-table-tbody > tr:hover > td {
  background: var(--bg-tertiary) !important;
}

[data-theme="dark"] .ant-table-tbody > tr.ant-table-row-selected > td {
  background: var(--bg-quaternary) !important;
}

/* 输入框组件 */
[data-theme="dark"] .ant-input {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-input:hover {
  border-color: var(--border-secondary) !important;
}

[data-theme="dark"] .ant-input:focus,
[data-theme="dark"] .ant-input-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

[data-theme="dark"] .ant-input::placeholder {
  color: var(--text-quaternary) !important;
}

/* 输入框搜索 */
[data-theme="dark"] .ant-input-search {
  background: var(--bg-tertiary) !important;
}

[data-theme="dark"] .ant-input-search .ant-input {
  background: transparent !important;
}

[data-theme="dark"] .ant-input-search .ant-input-search-button {
  background: var(--bg-quaternary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-input-search .ant-input-search-button:hover {
  background: var(--bg-tertiary) !important;
  color: #3b82f6 !important;
}

/* 文本域 */
[data-theme="dark"] .ant-input {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* 选择器 */
[data-theme="dark"] .ant-select .ant-select-selector {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-select-arrow {
  color: var(--text-tertiary) !important;
}

[data-theme="dark"] .ant-select-dropdown {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .ant-select-item {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-select-item:hover {
  background: var(--bg-tertiary) !important;
}

[data-theme="dark"] .ant-select-item-option-selected {
  background: var(--bg-quaternary) !important;
  color: #3b82f6 !important;
}

/* 按钮组件 */
[data-theme="dark"] .ant-btn {
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger) {
  background: var(--bg-tertiary) !important;
}

[data-theme="dark"] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):hover {
  background: var(--bg-quaternary) !important;
  border-color: var(--border-secondary) !important;
}

[data-theme="dark"] .ant-btn-text {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-btn-text:hover {
  background: var(--bg-tertiary) !important;
}

/* 标签页 */
[data-theme="dark"] .ant-tabs {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-tabs-tab {
  color: var(--text-tertiary) !important;
}

[data-theme="dark"] .ant-tabs-tab:hover {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .ant-tabs-tab-active {
  color: #3b82f6 !important;
}

[data-theme="dark"] .ant-tabs-ink-bar {
  background: #3b82f6 !important;
}

[data-theme="dark"] .ant-tabs-content-holder {
  background: var(--bg-secondary) !important;
}

/* 模态框 */
[data-theme="dark"] .ant-modal-content {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-modal-header {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .ant-modal-title {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-modal-footer {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
}

/* 下拉菜单 */
[data-theme="dark"] .ant-dropdown {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .ant-dropdown-menu {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .ant-dropdown-menu-item {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-dropdown-menu-item:hover {
  background: var(--bg-tertiary) !important;
}

/* 工具提示 */
[data-theme="dark"] .ant-tooltip-inner {
  background: var(--bg-quaternary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-tooltip-arrow::before {
  background: var(--bg-quaternary) !important;
}

/* 分页器 */
[data-theme="dark"] .ant-pagination {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-pagination-item {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .ant-pagination-item a {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-pagination-item:hover {
  border-color: #3b82f6 !important;
}

[data-theme="dark"] .ant-pagination-item-active {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

[data-theme="dark"] .ant-pagination-item-active a {
  color: white !important;
}

/* 进度条 */
[data-theme="dark"] .ant-progress-text {
  color: var(--text-primary) !important;
}

/* 统计数值 */
[data-theme="dark"] .ant-statistic {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ant-statistic-title {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .ant-statistic-content {
  color: var(--text-primary) !important;
}

/* 标签 */
[data-theme="dark"] .ant-tag {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* 头像 */
[data-theme="dark"] .ant-avatar {
  background: var(--bg-quaternary) !important;
  color: var(--text-primary) !important;
}

/* 徽章 */
[data-theme="dark"] .ant-badge {
  color: var(--text-primary) !important;
}

/* 空状态 */
[data-theme="dark"] .ant-empty {
  color: var(--text-tertiary) !important;
}

[data-theme="dark"] .ant-empty-description {
  color: var(--text-quaternary) !important;
}

/* 加载状态 */
[data-theme="dark"] .ant-spin-text {
  color: var(--text-primary) !important;
}

/* 分割线 */
[data-theme="dark"] .ant-divider {
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .ant-divider-inner-text {
  color: var(--text-secondary) !important;
}

/* ==========================================================================
   自定义组件暗黑模式
   ========================================================================== */

/* 确保所有文字在暗黑模式下可读 */
[data-theme="dark"] {
  color: var(--text-primary) !important;
}

[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  color: var(--text-primary) !important;
}

[data-theme="dark"] p {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .text-gray-800 {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .text-gray-600 {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .text-gray-500 {
  color: var(--text-tertiary) !important;
}

[data-theme="dark"] .text-gray-400 {
  color: var(--text-quaternary) !important;
}
