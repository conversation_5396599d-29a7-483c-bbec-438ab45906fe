/**
 * 智能分享系统 - 聚焦用户增长的分享引擎
 * 重新设计：极简化 + 增长导向
 */

/**
 * 核心分享策略：
 * 1. 成果炫耀分享 - 推动新用户增长
 * 2. 实用导出分享 - 满足工作需求
 */

/**
 * 智能分享主入口 - 根据场景自动选择最佳分享方式
 * @param {Object} content - 分享内容
 * @param {String} scene - 分享场景 ('achievement' | 'export')
 * @param {Object} options - 配置选项
 */
async function smartShare(content, scene = 'achievement', options = {}) {

  try {
    if (scene === 'achievement') {
      // 成果炫耀分享 - 增长核心
      await shareAchievement(content, options);
    } else if (scene === 'export') {
      // 实用导出分享 - 功能需求
      await shareExport(content, options);
    } else {
      throw new Error('未知分享场景');
    }
  } catch (error) {
    console.error('[智能分享] 分享失败:', error);
    wx.showToast({
      title: '分享失败，请重试',
      icon: 'none'
    });
  }
}

/**
 * 成果炫耀分享 - 核心增长引擎
 * 设计理念：让用户想炫耀自己用AI生成了专业评语
 */
async function shareAchievement(content, options = {}) {

  const {
    studentName = '某同学',
    commentContent = '',
    score = 0,
    style = '专业评语',
    teacherName = '老师'
  } = content;

  // 生成病毒传播卡片
  const cardData = {
    // 主标题 - 突出AI价值
    title: 'AI智能评语生成',
    subtitle: `为${studentName}生成专业评语`,
    
    // 核心内容 - 展示评语质量
    content: {
      comment: commentContent,
      score: score,
      style: style,
      teacher: studentName
    },
    
    // 社交传播元素
    social: {
      headline: generateViralHeadline(content),
      callToAction: '我也要试试AI评语助手',
      stats: {
        '评语质量': '专业级',
        '生成速度': '3分钟',
        '使用体验': '极佳'
      }
    },
    
    // 品牌元素
    brand: {
      name: '评语灵感君',
      slogan: '3分钟生成专业评语',
      qrcode: true // 自动生成小程序码
    }
  };

  // 一键生成分享卡片
  await generateViralCard(cardData, options);
}

/**
 * 生成病毒传播标题
 */
function generateViralHeadline(content) {
  const headlines = [
    `AI智能评语生成，3分钟完成专业级评语！教师们的效率神器`,
    `用评语灵感君生成的评语，家长们都说比以前的更用心更专业`,
    `终于找到救星了！AI评语助手让我告别加班写评语的痛苦`,
    `推荐给同事们的神器：AI评语生成，质量专业、速度超快`,
    `评语灵感君真的好用！节省90%时间，评语质量还更高`
  ];
  
  return headlines[Math.floor(Math.random() * headlines.length)];
}

/**
 * 生成优美高级的分享卡片 - 全新设计
 */
async function generateViralCard(data, options = {}) {

  try {
    wx.showLoading({
      title: '生成分享卡片...',
      mask: true
    });

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    const canvasId = options.canvasId || 'shareCanvas';
    
    // 卡片尺寸 - 调整高度确保内容完整显示，为二维码预留空间
    const cardWidth = 375;
    const cardHeight = 667; // 增加高度，避免被遮盖
    
    const ctx = wx.createCanvasContext(canvasId);
    
    // 1. 绘制优雅的渐变背景 - 参考最新设计趋势
    const gradient = ctx.createLinearGradient(0, 0, cardWidth, cardHeight);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(0.5, '#764ba2');
    gradient.addColorStop(1, '#f093fb');
    ctx.setFillStyle(gradient);
    ctx.fillRect(0, 0, cardWidth, cardHeight);
    
    // 2. 绘制精美装饰元素
    drawAdvancedDecorations(ctx, cardWidth, cardHeight);
    
    // 3. 绘制主卡片容器 - 重新设计，包含所有内容
    const cardPadding = 20;
    const cardY = 30;
    const qrSize = 70; // 减小二维码尺寸
    const cardContentHeight = cardHeight - 80; // 减少预留空间，白色卡片包含所有内容
    
    // 主卡片背景 - 毛玻璃效果，包含所有内容
    ctx.setFillStyle('rgba(255, 255, 255, 0.95)');
    ctx.setShadow(0, 20, 40, 'rgba(0, 0, 0, 0.1)');
    roundRect(ctx, cardPadding, cardY, cardWidth - cardPadding * 2, cardContentHeight, 24);
    ctx.fill();
    ctx.setShadow(0, 0, 0, 'transparent');
    
    // 4. 顶部标题区域设计
    let currentY = cardY + 40;
    
    // AI机器人图标
    ctx.setFillStyle('#4f46e5');
    ctx.setFontSize(32);
    ctx.setTextAlign('center');
    ctx.fillText('🤖', cardWidth / 2, currentY);
    
    currentY += 45;
    
    // 主标题 - 现代字体设计
    ctx.setFillStyle('#1e293b');
    ctx.setFontSize(28);
    ctx.setTextAlign('center');
    ctx.fillText('AI 智能评语生成', cardWidth / 2, currentY);
    
    currentY += 35;
    
    // 副标题
    ctx.setFillStyle('#64748b');
    ctx.setFontSize(16);
    ctx.setTextAlign('center');
    ctx.fillText(data.subtitle, cardWidth / 2, currentY);
    
    currentY += 50;
    
    // 5. 三个特色数据展示 - 模仿HTML页面的grid布局
    const statY = currentY;
    const statWidth = (cardWidth - 80) / 3;
    const statData = [
      { label: '评语质量', value: '专业级', color: '#4f46e5' },
      { label: '生成速度', value: '3分钟', color: '#4f46e5' },
      { label: '使用体验', value: '⭐⭐⭐⭐⭐', color: '#f59e0b' }
    ];
    
    statData.forEach((stat, index) => {
      const x = 40 + index * statWidth;
      
      // 数值
      ctx.setFillStyle(stat.color);
      ctx.setFontSize(18);
      ctx.setTextAlign('center');
      ctx.fillText(stat.value, x + statWidth / 2, statY);
      
      // 标签
      ctx.setFillStyle('#64748b');
      ctx.setFontSize(12);
      ctx.fillText(stat.label, x + statWidth / 2, statY + 20);
    });
    
    currentY += 80;
    
    // 6. CTA按钮区域 - 模仿HTML页面的按钮设计
    const btnWidth = 240;
    const btnHeight = 44;
    const btnX = (cardWidth - btnWidth) / 2;
    const btnY = currentY;
    
    // 绘制渐变按钮背景
    const btnGradient = ctx.createLinearGradient(btnX, btnY, btnX + btnWidth, btnY + btnHeight);
    btnGradient.addColorStop(0, '#4f46e5');
    btnGradient.addColorStop(1, '#7c3aed');
    ctx.setFillStyle(btnGradient);
    ctx.setShadow(0, 4, 15, 'rgba(79, 70, 229, 0.3)');
    roundRect(ctx, btnX, btnY, btnWidth, btnHeight, 22);
    ctx.fill();
    ctx.setShadow(0, 0, 0, 'transparent');
    
    // 按钮文字
    ctx.setFillStyle('#ffffff');
    ctx.setFontSize(16);
    ctx.setTextAlign('center');
    ctx.fillText('我也要试试 AI 评语助手', cardWidth / 2, btnY + 28);
    
    currentY += 60;
    
    // 7. 底部区域设计 - 按照红框标注重新布局
    const bottomSectionY = cardY + cardContentHeight - 100; // 距离底部100px的位置开始
    const textAreaWidth = 140; // 左侧文字区域宽度
    const qrAreaWidth = 100; // 右侧二维码区域宽度
    const qrNewSize = 80; // 调整二维码尺寸以适应右侧区域
    
    // 7.1 左侧文字区域 - 对应小红框
    const textX = cardPadding + 20;
    const textY = bottomSectionY + 20;
    
    ctx.setFillStyle('#64748b');
    ctx.setFontSize(13);
    ctx.setTextAlign('left');
    ctx.fillText('评语灵感君', textX, textY);
    
    ctx.setFillStyle('#94a3b8');
    ctx.setFontSize(11);
    ctx.fillText('3分钟生成专业评语', textX, textY + 18);
    
    ctx.setFillStyle('#a1a5ab');
    ctx.setFontSize(10);
    ctx.fillText('长按识别', textX, textY + 35);
    
    // 7.2 右侧二维码区域 - 对应大红框
    const qrAreaX = cardWidth - cardPadding - qrAreaWidth;
    const qrX = qrAreaX + (qrAreaWidth - qrNewSize) / 2; // 在右侧区域居中
    const qrY = bottomSectionY + 10; // 距离底部区域顶部10px
    
    // 二维码背景
    ctx.setFillStyle('#f8f9fa');
    ctx.setShadow(0, 2, 8, 'rgba(0, 0, 0, 0.1)');
    roundRect(ctx, qrX, qrY, qrNewSize, qrNewSize, 8);
    ctx.fill();
    ctx.setShadow(0, 0, 0, 'transparent');
    
    // 二维码边框
    ctx.setStrokeStyle('#e2e8f0');
    ctx.setLineWidth(1);
    ctx.strokeRect(qrX, qrY, qrNewSize, qrNewSize);

    // 当有真实二维码时，调用: drawQRCode(ctx, qrX, qrY, qrNewSize, qrImagePath)
    
    // 当前为占位设计
    ctx.setFillStyle('#3b82f6');
    ctx.setFontSize(24);
    ctx.setTextAlign('center');
    ctx.fillText('📱', qrX + qrNewSize / 2, qrY + qrNewSize / 2 - 8);
    
    ctx.setFillStyle('#64748b');
    ctx.setFontSize(10);
    ctx.fillText('小程序码', qrX + qrNewSize / 2, qrY + qrNewSize / 2 + 10);
    
    // 10. 执行绘制并分享
    ctx.draw(false, () => {
      wx.canvasToTempFilePath({
        canvasId: canvasId,
        width: cardWidth,
        height: cardHeight,
        success: (res) => {
          wx.hideLoading();
          
          // 直接触发分享
          shareViralCard(res.tempFilePath, data);
        },
        fail: (error) => {
          wx.hideLoading();
          console.error('[病毒卡片] 生成失败:', error);
          wx.showToast({
            title: '卡片生成失败',
            icon: 'none'
          });
        }
      });
    });
    
  } catch (error) {
    wx.hideLoading();
    console.error('[病毒卡片] 生成异常:', error);
    throw error;
  }
}

/**
 * 分享病毒卡片 - 一键分享到微信
 */
function shareViralCard(imagePath, data) {

  // 保存到相册并提示分享
  wx.saveImageToPhotosAlbum({
    filePath: imagePath,
    success: () => {
      // 显示分享指引
      wx.showModal({
        title: '🎉 分享卡片已生成',
        content: `${data.social.headline}\n\n📱 卡片已保存到相册\n💡 快去朋友圈或微信群分享吧！\n\n🎯 每次分享都能帮助更多老师发现这个好工具`,
        confirmText: '知道了',
        showCancel: false,
        success: () => {
          // 跟踪分享行为
          trackShareEvent('achievement_card', {
            content_type: 'viral_card',
            student_name: data.content.student || 'anonymous',
            share_time: new Date().toISOString()
          });
        }
      });
    },
    fail: (error) => {
      console.error('[病毒分享] 保存失败:', error);
      
      // 降级方案：直接预览图片
      wx.previewImage({
        urls: [imagePath],
        success: () => {
          wx.showToast({
            title: '长按图片保存分享',
            icon: 'none',
            duration: 3000
          });
        }
      });
    }
  });
}

/**
 * 实用导出分享 - 简化版
 */
async function shareExport(content, options = {}) {

  const {
    data,
    fileName = `评语数据_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`,
    title = '评语数据导出'
  } = content;

  try {
    // 直接使用简化的Excel导出
    const { shareExcelToWeChat } = require('./shareUtils');
    
    await shareExcelToWeChat(data, {
      fileName,
      title,
      headers: options.headers || ['学生姓名', '班级', '评语内容', '评分', '日期'],
      formatRow: options.formatRow || ((item) => [
        item.studentName || '',
        item.className || '',
        item.content || '',
        item.score || '',
        item.date || new Date().toLocaleDateString('zh-CN')
      ])
    });
    
    // 跟踪导出行为
    trackShareEvent('data_export', {
      content_type: 'excel',
      data_count: data.length,
      export_time: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('[导出分享] 导出失败:', error);
    throw error;
  }
}

/**
 * 绘制高级装饰元素
 */
function drawAdvancedDecorations(ctx, width, height) {
  // 渐变圆形装饰 - 更优雅的设计
  const decorations = [
    { x: width * 0.85, y: height * 0.15, radius: 50, opacity: 0.1 },
    { x: width * 0.15, y: height * 0.85, radius: 70, opacity: 0.08 },
    { x: width * 0.9, y: height * 0.7, radius: 30, opacity: 0.06 }
  ];
  
  decorations.forEach(decoration => {
    ctx.setFillStyle(`rgba(255, 255, 255, ${decoration.opacity})`);
    ctx.beginPath();
    ctx.arc(decoration.x, decoration.y, decoration.radius, 0, 2 * Math.PI);
    ctx.fill();
  });
  
  // 添加一些现代化的几何装饰
  ctx.setFillStyle('rgba(255, 255, 255, 0.05)');
  ctx.fillRect(width * 0.05, height * 0.3, 3, 80);
  ctx.fillRect(width * 0.95, height * 0.4, 3, 60);
}

/**
 * 装饰元素绘制 - 兼容性函数
 */
function drawDecorations(ctx, width, height) {
  drawAdvancedDecorations(ctx, width, height);
}

/**
 * 圆角矩形绘制
 */
function roundRect(ctx, x, y, width, height, radius) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
}

/**
 * 文本智能换行
 */
function wrapText(ctx, text, maxWidth, fontSize) {
  const lines = [];
  const chars = text.split('');
  let currentLine = '';
  
  chars.forEach(char => {
    const testLine = currentLine + char;
    const metrics = ctx.measureText(testLine);
    
    if (metrics.width > maxWidth && currentLine !== '') {
      lines.push(currentLine);
      currentLine = char;
    } else {
      currentLine = testLine;
    }
  });
  
  if (currentLine) {
    lines.push(currentLine);
  }
  
  return lines;
}

/**
 * 分享事件跟踪
 */
function trackShareEvent(eventType, data) {
  try {
    // 记录分享行为数据
    const shareData = {
      event: eventType,
      timestamp: Date.now(),
      ...data
    };
    
    // 保存到本地统计
    const shareStats = wx.getStorageSync('shareStats') || [];
    shareStats.push(shareData);
    wx.setStorageSync('shareStats', shareStats);

  } catch (error) {
    console.error('[分享统计] 记录失败:', error);
  }
}

/**
 * 智能分享入口 - 根据内容自动判断分享场景
 */
function autoShare(content) {
  // 判断分享场景
  if (content.commentContent || content.studentName) {
    // 有评语内容，使用成果分享
    return smartShare(content, 'achievement');
  } else if (content.data && Array.isArray(content.data)) {
    // 是数据列表，使用导出分享
    return smartShare(content, 'export');
  } else {
    // 默认使用成果分享
    return smartShare(content, 'achievement');
  }
}

/**
 * 绘制真实二维码 - 预留函数
 * @param {CanvasContext} ctx - Canvas上下文
 * @param {number} x - 二维码X坐标
 * @param {number} y - 二维码Y坐标
 * @param {number} size - 二维码尺寸
 * @param {string} qrImagePath - 二维码图片路径（可选）
 */
function drawQRCode(ctx, x, y, size, qrImagePath) {
  if (qrImagePath) {
    // 使用真实二维码图片
    ctx.drawImage(qrImagePath, x, y, size, size);
  } else {
    // 当前占位设计保持不变
    ctx.setFillStyle('#ffffff');
    roundRect(ctx, x, y, size, size, 12);
    ctx.fill();
    
    ctx.setFillStyle('#3b82f6');
    ctx.setFontSize(24);
    ctx.setTextAlign('center');
    ctx.fillText('📱', x + size / 2, y + size / 2 - 8);
    
    ctx.setFillStyle('#64748b');
    ctx.setFontSize(10);
    ctx.fillText('小程序码', x + size / 2, y + size / 2 + 8);
  }
}

module.exports = {
  smartShare,
  shareAchievement,
  shareExport,
  autoShare,
  trackShareEvent,
  drawQRCode // 导出二维码绘制函数，方便后期使用
};