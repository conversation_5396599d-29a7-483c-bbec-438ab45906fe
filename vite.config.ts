import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [
    react({
      // 正确的React配置，解决CSP问题
      fastRefresh: true,
      babel: {
        // 禁用可能导致eval的babel插件
        presets: [],
        plugins: []
      }
    })
  ],

  // 统一的路径别名
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services')
    }
  },

  // 开发服务器配置 - 专门修复Chrome自动刷新问题
  server: {
    port: 8080,
    host: '0.0.0.0',
    strictPort: false,
    cors: true,
    // 🔥 Chrome专用HMR修复配置
    hmr: {
      port: 8081,  // 使用不同端口避免Chrome WebSocket冲突
      host: '127.0.0.1',  // 使用127.0.0.1而不是localhost
      overlay: false,  // 禁用错误覆盖层避免触发刷新
      clientPort: 8081  // 明确指定客户端端口
    },
    watch: {
      usePolling: false,
      ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**']
    },
    // 添加Chrome兼容性头部
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    }
  },

  // 正确的构建配置
  build: {
    outDir: 'dist',
    sourcemap: true,
    minify: 'esbuild',
    target: 'es2020',
    rollupOptions: {
      output: {
        format: 'es',
        manualChunks: {
          'react-vendor': ['react', 'react-dom'],
          'ui-vendor': ['antd', '@ant-design/icons'],
          'utils-vendor': ['dayjs', 'lodash-es', 'axios']
        }
      }
    }
  },

  // ESBuild配置
  esbuild: {
    target: 'es2020',
    format: 'esm',
    keepNames: true
  },

  // 全局变量定义
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    global: 'globalThis'
  },

  // 依赖优化
  optimizeDeps: {
    include: [
      'react', 
      'react-dom', 
      'antd', 
      '@ant-design/icons',
      'dayjs', 
      'lodash-es',
      'axios',
      'react-router-dom'
    ],
    esbuildOptions: {
      target: 'es2020'
    }
  }
})