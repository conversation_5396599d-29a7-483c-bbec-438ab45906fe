/**
 * 验证用户Token云函数
 */
const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  try {
    const { token } = event;
    const wxContext = cloud.getWXContext();

    if (!token) {
      return {
        success: false,
        error: 'Token不能为空'
      };
    }

    // 验证token（这里可以根据实际需求实现token验证逻辑）
    // 目前使用openid作为用户标识
    const openid = wxContext.OPENID;

    if (!openid) {
      return {
        success: false,
        error: '获取用户信息失败'
      };
    }

    // 查询或创建用户记录
    try {
      const userResult = await db.collection('users').doc(openid).get();
      
      if (userResult.data) {
        // 用户已存在，返回用户信息
        return {
          success: true,
          data: {
            id: openid,
            ...userResult.data
          }
        };
      }
    } catch (error) {
      // 用户不存在，创建默认用户
      const defaultUser = {
        openid: openid,
        nickName: '微信用户',
        avatarUrl: '',
        name: '', // 真实姓名，初始为空
        avatar: '',
        phone: '',
        email: '',
        school: '',
        subject: '',
        grade: '',
        role: 'teacher',
        status: 'active',
        createTime: new Date(),
        updateTime: new Date(),
        loginCount: 1
      };

      await db.collection('users').doc(openid).set({
        data: defaultUser
      });

      return {
        success: true,
        data: {
          id: openid,
          ...defaultUser
        }
      };
    }

  } catch (error) {
    console.error('验证Token失败:', error);
    return {
      success: false,
      error: error.message || 'Token验证失败'
    };
  }
};
