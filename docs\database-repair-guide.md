# 数据库修复指南

## 问题描述

如果你遇到以下错误：
- `[首页] 数据库初始化失败: Error: errCode: -502005 database collection not exists`
- `[首页] behavior_records集合不存在，使用本地数据`
- `Failed to fetch` 错误

这说明你的云数据库集合不存在或云服务连接有问题。

## 解决方案

### 方法一：自动修复（推荐）

1. **长按首页统计区域**（今日统计部分）
2. 在弹出的菜单中选择：
   - **快速检查**：检查关键数据库集合状态
   - **完整修复**：自动创建所有缺失的数据库集合
   - **测试连接**：测试数据库连接状态
   - **云函数测试**：全面测试云服务功能

### 方法二：手动修复

如果自动修复失败，可以手动执行以下步骤：

#### 1. 检查云开发配置

确保 `project.config.json` 包含云开发配置：
```json
{
  "cloudfunctionRoot": "cloudfunctions/",
  "cloudbaseRoot": "cloudfunctions/",
  "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"
}
```

#### 2. 初始化云开发环境

在微信开发者工具中：
1. 点击工具栏的"云开发"按钮
2. 开通云开发服务
3. 创建云开发环境

#### 3. 部署云函数

确保以下云函数已部署：
- `initDatabase` - 数据库初始化
- `getUserId` - 获取用户ID
- `login` - 用户登录

#### 4. 创建数据库集合

需要创建以下集合：
- `users` - 用户信息
- `students` - 学生信息
- `records` - 评语记录
- `comments` - 评语内容
- `behavior_records` - 学生行为记录
- `user_profiles` - 用户档案
- `user_consent_records` - 用户同意记录
- `ai_configs` - AI配置
- `settings` - 系统设置

## 修复工具说明

### 数据库修复工具 (`utils/databaseRepairTool.js`)

提供以下功能：
- **完整修复**：检查并创建所有缺失的数据库集合
- **快速检查**：检查关键集合状态
- **连接测试**：测试数据库连接

### 数据库初始化器 (`utils/databaseInitializer.js`)

提供以下功能：
- **集合检查**：检查所有必需集合是否存在
- **自动创建**：创建缺失的集合
- **错误处理**：处理各种创建失败情况

### 云服务连接测试 (`test/cloud-connection-test.js`)

提供以下测试：
- 云开发初始化测试
- getUserId云函数测试
- 数据库访问测试
- 网络状态检测测试
- 重试机制测试
- 错误处理测试

## 常见问题

### Q: 为什么会出现集合不存在的错误？

A: 可能的原因：
1. 云开发环境未正确初始化
2. 数据库集合未创建
3. 网络连接问题
4. 云函数部署失败

### Q: 修复后还是有问题怎么办？

A: 尝试以下步骤：
1. 重新启动微信开发者工具
2. 清除缓存和本地存储
3. 重新部署云函数
4. 检查云开发控制台是否有错误日志

### Q: 如何预防这类问题？

A: 建议：
1. 定期备份云数据库
2. 监控云服务状态
3. 使用版本控制管理云函数
4. 设置适当的错误处理和降级方案

## 技术细节

### 错误代码说明

- `-502005`: 数据库集合不存在
- `Failed to fetch`: 网络连接失败或云服务不可用

### 修复流程

1. **检测阶段**：检查所有必需的数据库集合
2. **修复阶段**：创建缺失的集合
3. **验证阶段**：测试创建的集合是否可用
4. **报告阶段**：显示修复结果

### 降级策略

如果云数据库不可用，系统会：
1. 使用本地存储作为备用
2. 显示用户友好的错误提示
3. 提供手动重试选项
4. 记录错误日志供后续分析

## 联系支持

如果以上方法都无法解决问题，请：
1. 查看控制台错误日志
2. 检查云开发控制台状态
3. 联系技术支持并提供详细错误信息
