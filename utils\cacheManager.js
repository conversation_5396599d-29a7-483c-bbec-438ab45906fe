/**
 * 缓存管理工具
 * 提供内存缓存和本地存储缓存功能，优化数据访问性能
 */

const { STORAGE_KEYS } = require('./constants.js');

class CacheManager {
  constructor() {
    // 内存缓存
    this.memoryCache = new Map();
    
    // 缓存配置
    this.config = {
      // 默认过期时间（毫秒）
      defaultTTL: 5 * 60 * 1000, // 5分钟
      
      // 最大缓存条目数
      maxSize: 100,
      
      // 缓存键前缀
      prefix: 'cache_'
    };
    
    // 定期清理过期缓存
    this.startCleanupTimer();
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} value 缓存值
   * @param {number} ttl 过期时间（毫秒），默认使用配置的TTL
   * @param {boolean} persistent 是否持久化到本地存储
   */
  set(key, value, ttl = this.config.defaultTTL, persistent = false) {
    const now = Date.now();
    const expireTime = now + ttl;
    
    const cacheItem = {
      value,
      expireTime,
      createTime: now,
      accessCount: 0,
      lastAccess: now
    };

    // 检查缓存大小限制
    if (this.memoryCache.size >= this.config.maxSize) {
      this.evictLRU();
    }

    // 存储到内存缓存
    this.memoryCache.set(key, cacheItem);

    // 如果需要持久化，存储到本地
    if (persistent) {
      try {
        const persistentKey = this.config.prefix + key;
        wx.setStorageSync(persistentKey, {
          value,
          expireTime,
          createTime: now
        });
      } catch (error) {
        console.warn('持久化缓存失败:', error);
      }
    }
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @param {boolean} checkPersistent 是否检查持久化缓存
   * @returns {any} 缓存值，如果不存在或已过期返回null
   */
  get(key, checkPersistent = false) {
    const now = Date.now();

    // 先检查内存缓存
    if (this.memoryCache.has(key)) {
      const item = this.memoryCache.get(key);
      
      // 检查是否过期
      if (item.expireTime > now) {
        // 更新访问统计
        item.accessCount++;
        item.lastAccess = now;
        return item.value;
      } else {
        // 已过期，删除
        this.memoryCache.delete(key);
      }
    }

    // 如果内存缓存没有，检查持久化缓存
    if (checkPersistent) {
      try {
        const persistentKey = this.config.prefix + key;
        const persistentItem = wx.getStorageSync(persistentKey);
        
        if (persistentItem && persistentItem.expireTime > now) {
          // 恢复到内存缓存
          this.set(key, persistentItem.value, persistentItem.expireTime - now, false);
          return persistentItem.value;
        } else if (persistentItem) {
          // 持久化缓存也过期了，删除
          wx.removeStorageSync(persistentKey);
        }
      } catch (error) {
        console.warn('读取持久化缓存失败:', error);
      }
    }

    return null;
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   * @param {boolean} removePersistent 是否同时删除持久化缓存
   */
  delete(key, removePersistent = false) {
    // 删除内存缓存
    this.memoryCache.delete(key);

    // 删除持久化缓存
    if (removePersistent) {
      try {
        const persistentKey = this.config.prefix + key;
        wx.removeStorageSync(persistentKey);
      } catch (error) {
        console.warn('删除持久化缓存失败:', error);
      }
    }
  }

  /**
   * 清空所有缓存
   * @param {boolean} clearPersistent 是否同时清空持久化缓存
   */
  clear(clearPersistent = false) {
    // 清空内存缓存
    this.memoryCache.clear();

    // 清空持久化缓存
    if (clearPersistent) {
      try {
        const keys = wx.getStorageInfoSync().keys;
        keys.forEach(key => {
          if (key.startsWith(this.config.prefix)) {
            wx.removeStorageSync(key);
          }
        });
      } catch (error) {
        console.warn('清空持久化缓存失败:', error);
      }
    }
  }

  /**
   * 检查缓存是否存在且未过期
   * @param {string} key 缓存键
   * @returns {boolean} 是否存在有效缓存
   */
  has(key) {
    return this.get(key) !== null;
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const now = Date.now();
    let validCount = 0;
    let expiredCount = 0;
    let totalSize = 0;

    for (const [key, item] of this.memoryCache) {
      if (item.expireTime > now) {
        validCount++;
      } else {
        expiredCount++;
      }
      totalSize += JSON.stringify(item.value).length;
    }

    return {
      totalItems: this.memoryCache.size,
      validItems: validCount,
      expiredItems: expiredCount,
      totalSize: totalSize,
      maxSize: this.config.maxSize,
      hitRate: this.calculateHitRate()
    };
  }

  /**
   * 计算缓存命中率
   * @returns {number} 命中率（0-1）
   */
  calculateHitRate() {
    let totalAccess = 0;
    let totalHits = 0;

    for (const item of this.memoryCache.values()) {
      totalAccess += item.accessCount;
      if (item.accessCount > 0) {
        totalHits += item.accessCount;
      }
    }

    return totalAccess > 0 ? totalHits / totalAccess : 0;
  }

  /**
   * LRU淘汰策略
   */
  evictLRU() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, item] of this.memoryCache) {
      if (item.lastAccess < oldestTime) {
        oldestTime = item.lastAccess;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.memoryCache.delete(oldestKey);
    }
  }

  /**
   * 启动定期清理定时器
   */
  startCleanupTimer() {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.cleanupExpired();
    }, 5 * 60 * 1000);
  }

  /**
   * 清理过期缓存
   */
  cleanupExpired() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, item] of this.memoryCache) {
      if (item.expireTime <= now) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      this.memoryCache.delete(key);
    });

    if (expiredKeys.length > 0) {
      console.log(`清理了 ${expiredKeys.length} 个过期缓存项`);
    }
  }

  /**
   * 预热缓存（加载常用数据）
   * @param {Array} preloadKeys 需要预加载的缓存键
   */
  async warmup(preloadKeys = []) {
    for (const key of preloadKeys) {
      try {
        // 尝试从持久化缓存恢复
        this.get(key, true);
      } catch (error) {
        console.warn(`预热缓存 ${key} 失败:`, error);
      }
    }
  }
}

// 创建全局缓存实例
const globalCache = new CacheManager();

/**
 * 数据服务层 - 集成缓存的数据访问
 */
class DataService {
  constructor(cacheManager) {
    this.cache = cacheManager;
  }

  /**
   * 获取用户信息（带缓存）
   * @param {string} userId 用户ID
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(userId, forceRefresh = false) {
    const cacheKey = `user_info_${userId}`;

    if (!forceRefresh) {
      const cached = this.cache.get(cacheKey, true);
      if (cached) {
        return cached;
      }
    }

    try {
      // 从云函数获取数据
      const result = await wx.cloud.callFunction({
        name: 'getUserProfile',
        data: { userId }
      });

      if (result.result && result.result.success) {
        const userInfo = result.result.data;
        // 缓存5分钟，持久化
        this.cache.set(cacheKey, userInfo, 5 * 60 * 1000, true);
        return userInfo;
      }

      throw new Error(result.result?.message || '获取用户信息失败');
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取学生列表（带缓存）
   * @param {Object} params 查询参数
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise<Array>} 学生列表
   */
  async getStudents(params = {}, forceRefresh = false) {
    const cacheKey = `students_${JSON.stringify(params)}`;

    if (!forceRefresh) {
      const cached = this.cache.get(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'getStudents',
        data: params
      });

      if (result.result && result.result.success) {
        const students = result.result.data;
        // 缓存2分钟
        this.cache.set(cacheKey, students, 2 * 60 * 1000);
        return students;
      }

      throw new Error(result.result?.message || '获取学生列表失败');
    } catch (error) {
      console.error('获取学生列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取统计数据（带缓存）
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise<Object>} 统计数据
   */
  async getStatistics(forceRefresh = false) {
    const cacheKey = 'statistics';

    if (!forceRefresh) {
      const cached = this.cache.get(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'getStatistics',
        data: {}
      });

      if (result.result && result.result.success) {
        const stats = result.result.data;
        // 缓存1分钟
        this.cache.set(cacheKey, stats, 60 * 1000);
        return stats;
      }

      throw new Error(result.result?.message || '获取统计数据失败');
    } catch (error) {
      console.error('获取统计数据失败:', error);
      throw error;
    }
  }

  /**
   * 清除相关缓存
   * @param {string} type 缓存类型
   * @param {string} id 相关ID
   */
  invalidateCache(type, id = '') {
    switch (type) {
      case 'user':
        this.cache.delete(`user_info_${id}`, true);
        break;
      case 'students':
        // 清除所有学生相关缓存
        for (const key of this.cache.memoryCache.keys()) {
          if (key.startsWith('students_')) {
            this.cache.delete(key);
          }
        }
        break;
      case 'statistics':
        this.cache.delete('statistics');
        break;
      case 'all':
        this.cache.clear(true);
        break;
    }
  }
}

// 创建全局数据服务实例
const globalDataService = new DataService(globalCache);

module.exports = {
  CacheManager,
  DataService,
  cache: globalCache,
  dataService: globalDataService
};
