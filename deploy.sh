#!/bin/bash

echo "🚀 开始部署评语灵感君管理后台到云开发..."

# 检查是否安装了cloudbase cli
if ! command -v tcb &> /dev/null; then
    echo "❌ 未找到 CloudBase CLI，正在安装..."
    npm install -g @cloudbase/cli
fi

# 构建项目
echo "📦 正在构建项目..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败！"
    exit 1
fi

echo "✅ 构建完成"

# 检查登录状态
echo "🔐 检查登录状态..."
tcb auth list

# 部署静态文件
echo "📤 正在部署到云开发静态托管..."
tcb hosting deploy dist -e cloud1-4g85f8xlb8166ff1

if [ $? -eq 0 ]; then
    echo "🎉 部署成功！"
    echo "🌐 访问地址将在云开发控制台显示"
    echo "📋 请在微信开发者工具或腾讯云控制台查看部署状态"
else
    echo "❌ 部署失败！请检查环境ID和权限设置"
    exit 1
fi