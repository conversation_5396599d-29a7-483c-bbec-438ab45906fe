<!--协议文档页面-->
<view class="agreement-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">📄 服务协议</text>
  </view>

  <!-- 标签切换 -->
  <view class="tab-container">
    <view 
      class="tab-item {{currentTab === 'privacy' ? 'active' : ''}}"
      data-tab="privacy"
      bindtap="switchTab"
    >
      <text class="tab-text">🔒 隐私政策</text>
    </view>
    
    <view 
      class="tab-item {{currentTab === 'agreement' ? 'active' : ''}}"
      data-tab="agreement"
      bindtap="switchTab"
    >
      <text class="tab-text">📄 用户协议</text>
    </view>
  </view>

  <!-- 内容区域 -->
  <scroll-view class="content-container" scroll-y="true">
    <!-- 隐私政策内容 -->
    <view class="content-section" wx:if="{{currentTab === 'privacy'}}">
      <text class="content-text">{{privacyPolicy}}</text>
    </view>

    <!-- 用户协议内容 -->
    <view class="content-section" wx:if="{{currentTab === 'agreement'}}">
      <text class="content-text">{{userAgreement}}</text>
    </view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-button secondary" bindtap="copyContent">
      📋 复制内容
    </button>
    
    <button class="action-button primary" open-type="share">
      📤 分享协议
    </button>
  </view>
</view>
