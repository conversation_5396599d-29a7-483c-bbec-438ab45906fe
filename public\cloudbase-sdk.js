// CloudBase SDK 简化版本 - 专用于评语灵感君管理后台
// 使用腾讯云开发Web SDK避免CORS问题

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = global || self, global.cloudbase = factory());
}(this, (function () { 'use strict';

  // 简化的CloudBase类
  class CloudBase {
    constructor(config) {
      this.config = config || {};
      this.env = config.env || 'cloud1-4g85f8xlb8166ff1';
      this.tcb = null;
      console.log('🎯 CloudBase SDK 初始化:', this.env);
      this.init();
    }

    // 初始化腾讯云开发SDK
    async init() {
      try {
        // 检查是否已加载腾讯云开发SDK
        if (typeof window !== 'undefined' && window.cloudbase) {
          this.tcb = window.cloudbase.init({
            env: this.env
          });
          console.log('✅ 腾讯云开发SDK初始化成功');
        } else if (typeof window !== 'undefined' && window.tcb) {
          // 兼容旧版本SDK
          this.tcb = window.tcb.init({
            env: this.env
          });
          console.log('✅ 腾讯云开发SDK初始化成功 (兼容模式)');
        } else {
          console.warn('⚠️ 腾讯云开发SDK未加载，使用备用方案');
        }
      } catch (error) {
        console.error('❌ 腾讯云开发SDK初始化失败:', error);
      }
    }

    // 调用云函数
    async callFunction(params) {
      const { name, data } = params;
      console.log(`📞 调用云函数: ${name}`, data);

      try {
        // 优先使用腾讯云开发SDK
        if (this.tcb && this.tcb.callFunction) {
          const result = await this.tcb.callFunction({
            name: name,
            data: data
          });
          console.log(`✅ 云函数 ${name} 响应:`, result);
          return result;
        }

        // 备用方案：使用adminAPI代理调用
        if (name === 'dataQuery') {
          // 通过adminAPI转发请求，避免CORS问题
          const adminApiUrl = `https://${this.env}-1365982463.ap-shanghai.app.tcloudbase.com/adminAPI`;
          const response = await fetch(adminApiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              action: 'proxy_dataQuery',
              data: data
            })
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const result = await response.json();
          console.log(`✅ 云函数 ${name} 响应:`, result);

          return {
            result: result
          };
        }

        // 如果都失败，抛出错误
        throw new Error('无法调用云函数，请检查网络连接和配置');

      } catch (error) {
        console.error(`❌ 云函数调用失败: ${name}`, error);
        
        // 抛出错误而不是返回模拟数据
        throw new Error(`云函数调用失败: ${error.message}`);
      }
    }

    // 获取模拟数据
    getMockData(name, data) {
      if (name === 'dataQuery') {
        if (data.action === 'getDashboardStats') {
          return {
            totalUsers: 3,
            todayComments: 5,
            aiCalls: 8,
            satisfaction: 4.2,
            lastUpdated: new Date().toISOString()
          };
        } else if (data.action === 'getRecentActivities') {
          return [
            {
              id: 'mock1',
              userName: '张老师',
              action: '生成学生评语',
              timestamp: new Date().toISOString(),
              actionType: 'comment_generate'
            },
            {
              id: 'mock2',
              userName: '李老师',
              action: '查看学生信息',
              timestamp: new Date(Date.now() - 300000).toISOString(),
              actionType: 'user_login'
            },
            {
              id: 'mock3',
              userName: '王老师',
              action: 'AI评语生成',
              timestamp: new Date(Date.now() - 600000).toISOString(),
              actionType: 'ai_call'
            }
          ];
        }
      }
      return {};
    }
  }

  // CloudBase 工厂函数
  const cloudbase = {
    init: function(config) {
      return new CloudBase(config);
    }
  };

  return cloudbase;

})));

// 确保全局可用
if (typeof window !== 'undefined') {
  window.cloudbase = cloudbase;
  console.log('✅ CloudBase SDK 已就绪');
}