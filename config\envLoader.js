/**
 * 环境变量加载器 - 小程序版本
 * 适配小程序环境，不依赖Node.js模块
 */

class EnvLoader {
  constructor() {
    this.envVars = new Map();
    this.loadDefaultConfig();
  }

  /**
   * 加载默认配置（小程序环境下直接使用硬编码配置）
   */
  loadDefaultConfig() {
    // 小程序环境下的默认配置
    const defaultConfig = {
      'MINIPROGRAM_APP_ID': 'wx3de03090b8e8a734',
      'CLOUD_ENV_ID': 'cloud1-4g85f8xlb8166ff1',
      'CLOUD_REGION': 'ap-shanghai',
      'DOUBAO_API_KEY': 'your-doubao-api-key', // 需要在云函数中配置
      'DOUBAO_API_URL': 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      'DOUBAO_MODEL': 'doubao-pro-4k',
      'AI_TEMPERATURE': '0.7',
      'AI_MAX_TOKENS': '300'
    };

    // 将默认配置加载到envVars中
    Object.entries(defaultConfig).forEach(([key, value]) => {
      this.envVars.set(key, value);
    });

    console.log('✅ 小程序环境配置加载成功');
  }

  /**
   * 获取环境变量（小程序版本）
   */
  get(key, defaultValue = null) {
    return this.envVars.get(key) || defaultValue;
  }

  /**
   * 检查必需的环境变量
   */
  checkRequired(requiredVars) {
    const missing = [];

    requiredVars.forEach(varName => {
      if (!this.get(varName)) {
        missing.push(varName);
      }
    });

    if (missing.length > 0) {
      console.error('❌ 缺少必需的环境变量:', missing.join(', '));
      return false;
    }

    console.log('✅ 所有必需的环境变量都已配置');
    return true;
  }

  /**
   * 验证配置（小程序版本）
   */
  validateConfig() {
    const requiredVars = [
      'MINIPROGRAM_APP_ID',
      'CLOUD_ENV_ID'
    ];

    const isValid = this.checkRequired(requiredVars);

    if (!isValid) {
      console.error('🚨 环境配置验证失败！');
      return false;
    }

    // 验证具体值
    const appId = this.get('MINIPROGRAM_APP_ID');
    if (!appId || !appId.startsWith('wx')) {
      console.error('❌ MINIPROGRAM_APP_ID格式错误，应该以wx开头');
      return false;
    }

    console.log('✅ 小程序环境配置验证通过');
    return true;
  }

  /**
   * 获取云开发配置
   */
  getCloudConfig() {
    return {
      env: this.get('CLOUD_ENV_ID'),
      appid: this.get('MINIPROGRAM_APP_ID'),
      region: this.get('CLOUD_REGION', 'ap-shanghai'),
      traceUser: true,
      timeout: 60000
    };
  }

  /**
   * 获取AI配置
   */
  getAIConfig() {
    return {
      apiKey: this.get('DOUBAO_API_KEY'),
      apiUrl: this.get('DOUBAO_API_URL', 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'),
      model: this.get('DOUBAO_MODEL', 'doubao-pro-4k'),
      temperature: parseFloat(this.get('AI_TEMPERATURE', '0.7')),
      maxTokens: parseInt(this.get('AI_MAX_TOKENS', '300'))
    };
  }
}

// 创建全局实例
const envLoader = new EnvLoader();

module.exports = {
  envLoader,
  EnvLoader
};
