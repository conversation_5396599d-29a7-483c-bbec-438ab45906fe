/**
 * AI配置管理控制器
 */
const cloud = require('wx-server-sdk');
const axios = require('axios');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 获取AI配置
 */
async function getAIConfig(event, context) {
  try {
    const result = await db.collection('system_config')
      .where({
        type: 'ai_config',
        status: 'active'
      })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get();

    if (result.data && result.data.length > 0) {
      // 返回配置时隐藏API密钥的完整值
      const config = result.data[0];
      if (config.apiKey) {
        const keyLength = config.apiKey.length;
        config.apiKey = config.apiKey.substring(0, 4) + '*'.repeat(keyLength - 8) + config.apiKey.substring(keyLength - 4);
      }
      
      return {
        success: true,
        data: config
      };
    } else {
      return {
        success: true,
        data: null,
        message: '未找到AI配置'
      };
    }
  } catch (error) {
    console.error('获取AI配置失败:', error);
    return {
      success: false,
      message: '获取配置失败: ' + error.message
    };
  }
}

/**
 * 保存AI配置
 */
async function saveAIConfig(event, context) {
  try {
    const { OPENID } = cloud.getWXContext();
    
    // 验证管理员权限
    const adminCheck = await db.collection('admin_users')
      .where({
        openid: OPENID,
        status: 'active'
      })
      .get();
      
    if (!adminCheck.data || adminCheck.data.length === 0) {
      return {
        success: false,
        message: '无权限操作'
      };
    }
    
    const configData = {
      ...event,
      updateBy: OPENID,
      updateTime: new Date()
    };
    
    // 检查是否存在现有配置
    const existingConfig = await db.collection('system_config')
      .where({
        type: 'ai_config',
        status: 'active'
      })
      .get();
      
    if (existingConfig.data && existingConfig.data.length > 0) {
      // 更新现有配置
      await db.collection('system_config')
        .doc(existingConfig.data[0]._id)
        .update({
          data: configData
        });
    } else {
      // 创建新配置
      await db.collection('system_config')
        .add({
          data: configData
        });
    }
    
    return {
      success: true,
      message: '配置保存成功'
    };
  } catch (error) {
    console.error('保存AI配置失败:', error);
    return {
      success: false,
      message: '保存配置失败: ' + error.message
    };
  }
}

/**
 * 测试AI连接
 */
async function testAIConnection(event, context) {
  try {
    const { apiKey, apiUrl, model, temperature, maxTokens } = event;

    if (!apiKey || !apiUrl) {
      return {
        success: false,
        message: 'API密钥和API地址不能为空'
      };
    }
    
    if (!model) {
      return {
        success: false,
        message: '模型标识不能为空'
      };
    }
    
    const startTime = Date.now();

    const testRequestData = {
      model: model,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: '请回复"测试成功"'
            }
          ]
        }
      ],
      temperature: temperature || 0.7,
      max_tokens: maxTokens || 50,
      stream: false
    };

    const response = await axios.post(apiUrl, testRequestData, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 15000 // 15秒超时
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (response.data && response.data.choices && response.data.choices[0]) {
      const responseContent = response.data.choices[0].message.content;
      
      return {
        success: true,
        message: '连接测试成功！AI模型响应正常',
        data: {
          content: responseContent,
          model: response.data.model || model,
          usage: response.data.usage || {
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0
          }
        },
        responseTime: `${responseTime}ms`
      };
    } else {
      console.error('AI API响应格式异常:', response.data);
      return {
        success: false,
        message: 'API响应格式错误，请检查模型配置',
        responseTime: `${responseTime}ms`
      };
    }
  } catch (error) {
    console.error('测试AI连接失败:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
    
    // 提供详细的错误信息
    let errorMessage = 'AI连接测试失败';
    
    if (error.response?.status === 401) {
      errorMessage = 'API密钥无效或已过期，请检查配置';
    } else if (error.response?.status === 403) {
      errorMessage = 'API访问权限不足，请检查密钥权限';
    } else if (error.response?.status === 404) {
      errorMessage = 'API地址不正确，请检查配置';
    } else if (error.response?.status === 429) {
      errorMessage = 'API调用频率超限，请稍后再试';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = '无法连接到API服务，请检查网络';
    } else if (error.response?.data?.error?.message) {
      errorMessage = error.response.data.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    return {
      success: false,
      message: errorMessage,
      error: {
        code: error.response?.status || error.code,
        details: error.response?.data || error.message
      }
    };
  }
}

module.exports = {
  getAIConfig,
  saveAIConfig,
  testAIConnection
};
