import React, { useState, useEffect } from 'react'
import { Card, Button, Typography, Space, Divider } from 'antd'
import { CloseOutlined, ExpandAltOutlined, CompressOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

interface InlineDrawerProps {
  visible: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  width?: string | number
  position?: 'right' | 'left' | 'bottom'
  expandable?: boolean
  className?: string
}

const InlineDrawer: React.FC<InlineDrawerProps> = ({
  visible,
  onClose,
  title,
  children,
  width = '400px',
  position = 'right',
  expandable = true,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (visible) {
      setIsAnimating(true)
      const timer = setTimeout(() => setIsAnimating(false), 300)
      return () => clearTimeout(timer)
    }
  }, [visible])

  if (!visible) return null

  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed' as const,
      top: '64px', // 考虑导航栏高度
      bottom: '0',
      zIndex: 1000,
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: position === 'bottom' ? '12px 12px 0 0' : '12px 0 0 12px',
      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      overflow: 'hidden'
    }

    switch (position) {
      case 'right':
        return {
          ...baseStyles,
          right: 0,
          width: isExpanded ? '80vw' : width,
          transform: isAnimating ? 'translateX(20px)' : 'translateX(0)'
        }
      case 'left':
        return {
          ...baseStyles,
          left: 0,
          width: isExpanded ? '80vw' : width,
          transform: isAnimating ? 'translateX(-20px)' : 'translateX(0)'
        }
      case 'bottom':
        return {
          ...baseStyles,
          left: '50%',
          right: 'auto',
          top: 'auto',
          bottom: 0,
          width: isExpanded ? '95vw' : '600px',
          height: isExpanded ? '80vh' : '400px',
          transform: `translateX(-50%) ${isAnimating ? 'translateY(20px)' : 'translateY(0)'}`
        }
      default:
        return baseStyles
    }
  }

  return (
    <>
      {/* 背景遮罩 - 轻微的，不阻止用户查看主内容 */}
      <div 
        className="fixed inset-0 bg-black/5 backdrop-blur-sm z-999"
        style={{ top: '64px' }}
        onClick={onClose}
      />
      
      {/* 抽屉内容 */}
      <div 
        style={getPositionStyles()}
        className={`inline-drawer ${className} ${isAnimating ? 'animate-in' : ''}`}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <Title level={4} className="!mb-0 !text-gray-800 dark:!text-gray-100">
              {title}
            </Title>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
          </div>
          
          <Space>
            {expandable && (
              <Button
                type="text"
                icon={isExpanded ? <CompressOutlined /> : <ExpandAltOutlined />}
                onClick={() => setIsExpanded(!isExpanded)}
                className="hover:bg-gray-100"
                title={isExpanded ? '收起' : '展开'}
              />
            )}
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={onClose}
              className="hover:bg-gray-100"
              title="关闭"
            />
          </Space>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-auto p-6">
          {children}
        </div>

        {/* 底部操作栏（可选） */}
        <div className="border-t border-gray-100 p-4 bg-gray-50/50">
          <div className="flex justify-end space-x-2">
            <Button onClick={onClose}>
              取消
            </Button>
            <Button type="primary">
              确定
            </Button>
          </div>
        </div>
      </div>

      <style jsx>{`
        .inline-drawer.animate-in {
          animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateX(20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
          .inline-drawer {
            position: fixed !important;
            top: 64px !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            border-radius: 12px 12px 0 0 !important;
          }
        }
      `}</style>
    </>
  )
}

export default InlineDrawer