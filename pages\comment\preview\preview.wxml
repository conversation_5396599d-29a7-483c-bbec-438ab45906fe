<!--
  评语预览页面 - 莫兰迪简约设计
-->
<view class="morandi-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">评语预览</view>
    <view class="page-subtitle">共生成 {{successCount}} 条评语</view>
  </view>

  <!-- 生成信息卡片 -->
  <view class="info-card">
    <view class="info-row">
      <text class="info-label">生成风格：</text>
      <text class="info-value">{{generateInfo.styleText}}</text>
    </view>
    <view class="info-row">
      <text class="info-label">评语长度：</text>
      <text class="info-value">{{generateInfo.lengthText}}</text>
    </view>
    <view class="info-row">
      <text class="info-label">生成时间：</text>
      <text class="info-value">{{generateInfo.generateTime}}</text>
    </view>
  </view>

  <!-- 评语列表 -->
  <view class="comment-list">
    <view class="comment-item" wx:for="{{commentList}}" wx:key="id">
      <view class="comment-header">
        <view class="student-info">
          <text class="student-name">{{item.studentName}}</text>
          <text class="student-class">{{item.className}}</text>
        </view>
        <view class="comment-meta">
          <text class="comment-length">{{item.comment.length}}字</text>
        </view>
      </view>
      
      <view class="comment-content">{{item.comment}}</view>
      
      <view class="comment-actions">
        <view class="action-btn secondary" data-index="{{index}}" bindtap="editComment">
          <text>编辑</text>
        </view>
        <view class="action-btn secondary" data-index="{{index}}" bindtap="regenerateComment">
          <text>重新生成</text>
        </view>
        <view class="action-btn danger" data-index="{{index}}" bindtap="deleteComment">
          <text>删除</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{commentList.length === 0}}">
    <view class="empty-icon">📝</view>
    <text class="empty-text">暂无评语数据</text>
    <text class="empty-hint">请返回重新生成</text>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{commentList.length > 0}}">
    <view class="action-row">
      <view class="back-btn" bindtap="goBackToGenerate">
        <text class="back-text">返回修改</text>
      </view>
      
      <view class="save-btn {{saving ? 'loading' : ''}}" bindtap="saveAllComments">
        <view class="btn-content">
          <view class="btn-icon" wx:if="{{!saving}}">💾</view>
          <simple-loading size="20px" color="white" wx:if="{{saving}}" />
          <text class="btn-text">{{saving ? '保存中...' : '保存全部'}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
