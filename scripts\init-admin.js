#!/usr/bin/env node

/**
 * 管理员账户初始化脚本
 * 用于创建系统的第一个管理员账户
 */

const https = require('https')
const readline = require('readline')

// 配置信息
const CONFIG = {
  API_BASE_URL: 'https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/admin',
  DEFAULT_USERNAME: 'admin',
  DEFAULT_PASSWORD: 'admin123',
  DEFAULT_EMAIL: '<EMAIL>'
}

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// 提问函数
const question = (query) => {
  return new Promise((resolve) => {
    rl.question(query, resolve)
  })
}

// 发送HTTP请求
const sendRequest = (data) => {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data)
    
    const options = {
      hostname: new URL(CONFIG.API_BASE_URL).hostname,
      port: 443,
      path: new URL(CONFIG.API_BASE_URL).pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    }

    const req = https.request(options, (res) => {
      let responseData = ''
      
      res.on('data', (chunk) => {
        responseData += chunk
      })
      
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData)
          resolve(result)
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`))
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.write(postData)
    req.end()
  })
}

// 检查是否已有管理员
const checkAdminExists = async () => {
  console.log('🔍 检查系统管理员状态...')
  
  try {
    const response = await sendRequest({
      action: 'auth.checkAdminExists',
      timestamp: Date.now(),
      requestId: `check_admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    })

    if (response.code === 200) {
      return response.data
    } else {
      throw new Error(response.message || '检查管理员状态失败')
    }
  } catch (error) {
    console.error('❌ 检查管理员状态失败:', error.message)
    
    // 如果是网络错误，给出更明确的提示
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      console.log('💡 可能的解决方案:')
      console.log('  1. 检查网络连接是否正常')
      console.log('  2. 确认云函数已正确部署')
      console.log('  3. 检查API地址配置是否正确')
      console.log(`  4. 当前配置的API地址: ${CONFIG.API_BASE_URL}`)
    }
    
    throw error
  }
}

// 初始化管理员
const initAdmin = async (adminData) => {
  console.log('🚀 正在创建管理员账户...')
  
  try {
    const response = await sendRequest({
      action: 'auth.initAdmin',
      ...adminData,
      timestamp: Date.now(),
      requestId: `init_admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    })

    if (response.code === 200) {
      return response.data
    } else {
      throw new Error(response.message || '创建管理员失败')
    }
  } catch (error) {
    console.error('❌ 创建管理员失败:', error.message)
    throw error
  }
}

// 主函数
const main = async () => {
  console.log('🎉 欢迎使用评语灵感君管理后台初始化工具!')
  console.log('=' .repeat(50))
  
  try {
    // 检查是否已有管理员
    const adminStatus = await checkAdminExists()
    
    if (adminStatus.hasAdmin) {
      console.log('⚠️  系统已存在管理员账户！')
      console.log(`   当前管理员数量: ${adminStatus.count}`)
      console.log('')
      console.log('如需重新初始化，请先清理现有管理员数据。')
      return
    }

    console.log('✅ 系统尚未初始化管理员，可以继续创建。')
    console.log('')

    // 获取管理员信息
    console.log('请输入管理员信息（直接回车使用默认值）:')
    console.log('')

    const username = await question(`用户名 (默认: ${CONFIG.DEFAULT_USERNAME}): `) || CONFIG.DEFAULT_USERNAME
    const password = await question(`密码 (默认: ${CONFIG.DEFAULT_PASSWORD}): `) || CONFIG.DEFAULT_PASSWORD
    const email = await question(`邮箱 (默认: ${CONFIG.DEFAULT_EMAIL}): `) || CONFIG.DEFAULT_EMAIL
    const name = await question('管理员姓名 (默认: 系统管理员): ') || '系统管理员'
    const department = await question('部门 (默认: 系统管理部): ') || '系统管理部'

    console.log('')
    console.log('管理员信息确认:')
    console.log('================')
    console.log(`用户名: ${username}`)
    console.log(`密码: ${'*'.repeat(password.length)}`)
    console.log(`邮箱: ${email}`)
    console.log(`姓名: ${name}`)
    console.log(`部门: ${department}`)
    console.log('')

    const confirm = await question('确认创建？(y/N): ')
    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log('❌ 已取消创建。')
      return
    }

    // 创建管理员
    const adminData = {
      username,
      password,
      email,
      profile: {
        name,
        department
      }
    }

    const result = await initAdmin(adminData)
    
    console.log('')
    console.log('🎉 管理员账户创建成功!')
    console.log('========================')
    console.log(`账户ID: ${result.id}`)
    console.log(`用户名: ${result.username}`)
    console.log(`创建时间: ${new Date().toLocaleString()}`)
    console.log('')
    console.log('💡 接下来可以：')
    console.log('  1. 使用创建的账户登录管理后台')
    console.log('  2. 配置系统设置和AI服务')
    console.log('  3. 查看小程序用户数据统计')
    console.log('')
    console.log('🔗 管理后台地址: http://localhost:3000 (开发环境)')

  } catch (error) {
    console.error('')
    console.error('💥 初始化失败:', error.message)
    console.error('')
    
    if (error.code === 'ENOTFOUND') {
      console.error('📝 故障排除建议:')
      console.error('  1. 检查网络连接')
      console.error('  2. 确认云函数服务状态')
      console.error('  3. 验证API地址配置')
      console.error(`     当前地址: ${CONFIG.API_BASE_URL}`)
    }
    
    process.exit(1)
  } finally {
    rl.close()
  }
}

// 处理退出信号
process.on('SIGINT', () => {
  console.log('\n\n👋 初始化已中断，再见!')
  rl.close()
  process.exit(0)
})

// 运行主函数
if (require.main === module) {
  main().catch((error) => {
    console.error('Fatal error:', error.message)
    process.exit(1)
  })
}

module.exports = { checkAdminExists, initAdmin }