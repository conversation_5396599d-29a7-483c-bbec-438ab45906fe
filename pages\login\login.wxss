/* 登录页面样式 */
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: 60rpx;
  left: 32rpx;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.3s ease;
}

.back-button:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

/* 主要内容 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 48rpx 80rpx;
}

/* 应用信息 */
.app-info {
  text-align: center;
  margin-bottom: 120rpx;
}

.app-icon {
  margin-bottom: 48rpx;
}

/* 移除了 .icon-container 样式 */

.logo-image {
  width: 174rpx;
  height: 174rpx;
  border-radius: 32rpx;
  margin: 0 auto;
  display: block;
  /* 添加轻微阴影让logo更突出 */
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.app-title {
  font-size: 56rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.app-features {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
  align-items: center;
  justify-content: center;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 16rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.feature-text {
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
  white-space: nowrap;
}

/* 登录区域 */
.login-section {
  width: 100%;
  max-width: 600rpx;
}

.login-button {
  width: 480rpx;
  height: 96rpx;
  background: #4080FF;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(64, 128, 255, 0.3);
  transition: all 0.3s ease;
  margin: 0 auto 32rpx;
}

.login-button:active {
  transform: scale(0.98);
  background: #3670E8;
  box-shadow: 0 4rpx 16rpx rgba(64, 128, 255, 0.4);
}

.login-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

/* 游客模式按钮 */
.guest-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  width: 480rpx;
  height: 80rpx;
  margin: 24rpx auto 32rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 40rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.guest-button:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.98);
}

.guest-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  font-weight: 500;
}

.login-tips {
  text-align: center;
  line-height: 1.6;
}

.tips-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.link-text {
  font-size: 24rpx;
  color: #fff;
  text-decoration: underline;
  font-weight: 500;
}

/* 弹窗样式 */
.agreement-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.agreement-content {
  flex: 1;
  padding: 0 32rpx;
}

.agreement-text {
  padding: 24rpx 0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin: 32rpx 0 16rpx;
}

.section-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.popup-footer {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}
