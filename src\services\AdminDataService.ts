/**
 * 管理后台数据服务实现
 * 实现IDataService接口，提供管理后台专用的数据访问逻辑
 */

import {
  IDataService,
  DashboardStats,
  ActivityRecord,
  SystemMetrics,
  ConnectionTestResult,
  Student,
  Comment,
  User,
  AIConfig,
  PaginationParams,
  PaginatedResult
} from '../interfaces/IDataService'
import cloudbaseService from '../utils/cloudbaseConfig'
import { handleApiError } from '../utils/errorHandler'
import { env } from '../utils/env'

export class AdminDataService implements IDataService {
  private adminApiUrl = env.API_BASE_URL

  constructor() {
    console.log('🎯 管理后台数据服务初始化')
  }

  // ==================== 基础数据查询 ====================

  async getDashboardStats(): Promise<DashboardStats> {
    return handleApiError(
      'getDashboardStats',
      async () => {
        const result = await cloudbaseService.callFunction('dataQuery', { 
          action: 'getDashboardStats' 
        })

        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '数据获取失败')
        }
      },
      { component: 'AdminDataService' }
    ) || {
      totalUsers: 0,
      todayComments: 0,
      aiCalls: 0,
      satisfaction: 0,
      lastUpdated: new Date().toISOString()
    }
  }

  async getRecentActivities(limit = 10): Promise<ActivityRecord[]> {
    return handleApiError(
      'getRecentActivities',
      async () => {
        const result = await cloudbaseService.callFunction('dataQuery', { 
          action: 'getRecentActivities',
          params: { limit }
        })

        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '活动记录获取失败')
        }
      },
      { component: 'AdminDataService' }
    ) || []
  }

  async getSystemMetrics(): Promise<SystemMetrics> {
    return handleApiError(
      'getSystemMetrics',
      async () => {
        // 模拟系统性能指标
        const now = new Date()
        const hour = now.getHours()
        
        const baseCpu = hour >= 9 && hour <= 17 ? 45 : 25
        const baseMemory = hour >= 9 && hour <= 17 ? 60 : 35
        
        return {
          cpu: Math.round(baseCpu + Math.random() * 20),
          memory: Math.round(baseMemory + Math.random() * 25),
          storage: Math.round(15 + Math.random() * 20),
          apiResponseTime: Math.round(80 + Math.random() * 40),
          activeConnections: Math.round(5 + Math.random() * 25),
          timestamp: new Date().toISOString()
        }
      },
      { component: 'AdminDataService' }
    ) || {
      cpu: 0,
      memory: 0,
      storage: 0,
      apiResponseTime: 0,
      activeConnections: 0,
      timestamp: new Date().toISOString()
    }
  }

  async testConnection(): Promise<ConnectionTestResult> {
    return handleApiError(
      'testConnection',
      async () => {
        const result = await cloudbaseService.callFunction('dataQuery', { 
          action: 'testConnection' 
        })

        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '连接测试失败')
        }
      },
      { component: 'AdminDataService' }
    ) || {
      success: false,
      message: '连接测试失败',
      collections: [],
      sampleData: null
    }
  }

  // ==================== 班级数据管理 ====================

  async getClasses(params: PaginationParams = {}): Promise<PaginatedResult<any>> {
    return handleApiError(
      'getClasses',
      async () => {
        const result = await cloudbaseService.callFunction('dataQuery', {
          action: 'getClasses',
          params
        })

        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '班级数据获取失败')
        }
      },
      { component: 'AdminDataService' }
    ) || {
      list: [],
      total: 0,
      page: params.page || 1,
      limit: params.limit || 20
    }
  }

  // ==================== 学生数据管理 ====================

  async getStudents(params: PaginationParams = {}): Promise<PaginatedResult<Student>> {
    return handleApiError(
      'getStudents',
      async () => {
        const result = await cloudbaseService.callFunction('dataQuery', { 
          action: 'getStudents',
          params
        })

        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '学生数据获取失败')
        }
      },
      { component: 'AdminDataService' }
    ) || {
      list: [],
      total: 0,
      page: params.page || 1,
      limit: params.limit || 20
    }
  }

  async getStudent(id: string): Promise<Student | null> {
    return handleApiError(
      'getStudent',
      async () => {
        const result = await cloudbaseService.callFunction('dataQuery', { 
          action: 'getStudent',
          params: { id }
        })

        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '学生信息获取失败')
        }
      },
      { component: 'AdminDataService' }
    )
  }

  async createStudent(student: Omit<Student, 'id'>): Promise<Student> {
    return handleApiError(
      'createStudent',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.createStudent',
            ...student
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '学生创建失败')
        }
      },
      { component: 'AdminDataService' }
    ) || { ...student, id: '', createTime: new Date().toISOString() } as Student
  }

  async updateStudent(id: string, updates: Partial<Student>): Promise<Student> {
    return handleApiError(
      'updateStudent',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.updateStudent',
            id,
            ...updates
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '学生信息更新失败')
        }
      },
      { component: 'AdminDataService' }
    ) || { ...updates, id } as Student
  }

  async deleteStudent(id: string): Promise<boolean> {
    return handleApiError(
      'deleteStudent',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.deleteStudent',
            id
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        return result.code === 200
      },
      { component: 'AdminDataService' }
    ) || false
  }

  // ==================== 评语数据管理 ====================

  async getComments(params: PaginationParams = {}): Promise<PaginatedResult<Comment>> {
    return handleApiError(
      'getComments',
      async () => {
        const result = await cloudbaseService.callFunction('dataQuery', { 
          action: 'getComments',
          params
        })

        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '评语数据获取失败')
        }
      },
      { component: 'AdminDataService' }
    ) || {
      list: [],
      total: 0,
      page: params.page || 1,
      limit: params.limit || 20
    }
  }

  async getComment(id: string): Promise<Comment | null> {
    return handleApiError(
      'getComment',
      async () => {
        const result = await cloudbaseService.callFunction('dataQuery', { 
          action: 'getComment',
          params: { id }
        })

        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '评语信息获取失败')
        }
      },
      { component: 'AdminDataService' }
    )
  }

  async createComment(comment: Omit<Comment, 'id'>): Promise<Comment> {
    return handleApiError(
      'createComment',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.createComment',
            ...comment
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '评语创建失败')
        }
      },
      { component: 'AdminDataService' }
    ) || { ...comment, id: '', createTime: new Date().toISOString() } as Comment
  }

  async updateComment(id: string, updates: Partial<Comment>): Promise<Comment> {
    return handleApiError(
      'updateComment',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.updateComment',
            id,
            ...updates
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '评语更新失败')
        }
      },
      { component: 'AdminDataService' }
    ) || { ...updates, id } as Comment
  }

  async deleteComment(id: string): Promise<boolean> {
    return handleApiError(
      'deleteComment',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.deleteComment',
            id
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        return result.code === 200
      },
      { component: 'AdminDataService' }
    ) || false
  }

  // ==================== 用户管理 ====================

  async getUsers(params: PaginationParams = {}): Promise<PaginatedResult<User>> {
    return handleApiError(
      'getUsers',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.getUsers',
            ...params
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '用户数据获取失败')
        }
      },
      { component: 'AdminDataService' }
    ) || {
      list: [],
      total: 0,
      page: params.page || 1,
      limit: params.limit || 20
    }
  }

  async getUser(id: string): Promise<User | null> {
    return handleApiError(
      'getUser',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.getUser',
            id
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '用户信息获取失败')
        }
      },
      { component: 'AdminDataService' }
    )
  }

  async createUser(user: Omit<User, 'id'>): Promise<User> {
    return handleApiError(
      'createUser',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.createUser',
            ...user
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '用户创建失败')
        }
      },
      { component: 'AdminDataService' }
    ) || { ...user, id: '', createTime: new Date().toISOString() } as User
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User> {
    return handleApiError(
      'updateUser',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.updateUser',
            id,
            ...updates
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || '用户信息更新失败')
        }
      },
      { component: 'AdminDataService' }
    ) || { ...updates, id } as User
  }

  async deleteUser(id: string): Promise<boolean> {
    return handleApiError(
      'deleteUser',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.deleteUser',
            id
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        return result.code === 200
      },
      { component: 'AdminDataService' }
    ) || false
  }

  // ==================== 系统配置 ====================

  async getAIConfig(): Promise<AIConfig> {
    return handleApiError(
      'getAIConfig',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'ai.getConfig'
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || 'AI配置获取失败')
        }
      },
      { component: 'AdminDataService' }
    ) || {
      model: 'doubao',
      apiKey: '',
      enabledFeatures: []
    } as AIConfig
  }

  async updateAIConfig(config: Partial<AIConfig>): Promise<AIConfig> {
    return handleApiError(
      'updateAIConfig',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'ai.updateConfig',
            ...config
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        if (result.code === 200) {
          return result.data
        } else {
          throw new Error(result.message || 'AI配置更新失败')
        }
      },
      { component: 'AdminDataService' }
    ) || config as AIConfig
  }

  // ==================== 数据统计 ====================

  async getUserStats(): Promise<any> {
    return handleApiError(
      'getUserStats',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.getUserStats'
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        return result.code === 200 ? result.data : {}
      },
      { component: 'AdminDataService' }
    ) || {}
  }

  async getCommentStats(): Promise<any> {
    return handleApiError(
      'getCommentStats',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.getCommentStats'
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        return result.code === 200 ? result.data : {}
      },
      { component: 'AdminDataService' }
    ) || {}
  }

  async getAIStats(): Promise<any> {
    return handleApiError(
      'getAIStats',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'ai.getStats'
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        return result.code === 200 ? result.data : {}
      },
      { component: 'AdminDataService' }
    ) || {}
  }

  async getSystemHealth(): Promise<any> {
    return handleApiError(
      'getSystemHealth',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'system.getHealth'
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        return result.code === 200 ? result.data : {}
      },
      { component: 'AdminDataService' }
    ) || {
      status: 'unknown',
      uptime: 0,
      version: '1.0.0'
    }
  }

  // ==================== 数据导出 ====================

  async exportData(type: 'students' | 'comments' | 'users', format = 'excel'): Promise<boolean> {
    return handleApiError(
      'exportData',
      async () => {
        const response = await fetch(this.adminApiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'data.export',
            type,
            format
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        const result = await response.json()
        return result.code === 200
      },
      { component: 'AdminDataService' }
    ) || false
  }
}

// 导出单例实例
export const adminDataService = new AdminDataService()
export default AdminDataService