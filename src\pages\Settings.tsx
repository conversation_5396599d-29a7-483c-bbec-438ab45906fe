import React, { useState, useEffect } from 'react'
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Tabs,
  Switch,
  InputNumber,
  Select,
  message,
  Divider,
  Avatar,
  Upload,
  Modal,
  notification
} from 'antd'
import { UserOutlined, UploadOutlined, SaveOutlined, SettingOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone, DownloadOutlined, DeleteOutlined } from '@ant-design/icons'
import { useAuthStore } from '../stores/authStore'
import { useLocation, useNavigate } from 'react-router-dom'
import cloudFunctionService from '../services/cloudFunctionService'
import { settingsService, SystemSettings, UserPreferences } from '../services/settingsService'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

const Settings: React.FC = () => {
  const [form] = Form.useForm()
  const [profileForm] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('profile')
  const [passwordModalVisible, setPasswordModalVisible] = useState(false)
  const [avatarUrl, setAvatarUrl] = useState(localStorage.getItem('userAvatar') || '')

  // 系统设置状态
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    systemName: '评语灵感君管理后台',
    version: 'v2.0.0',
    maxUsers: 1000,
    sessionTimeout: 30,
    autoRefresh: false,
    darkMode: false,
    debugMode: false,
    enableCache: true,
    logLevel: 'info',
    backupFrequency: 'daily'
  })

  // 通知设置状态
  const [notificationSettings, setNotificationSettings] = useState<UserPreferences>({
    browserEnabled: true,
    pageAlert: true,
    soundAlert: false,
    systemAlert: true,
    dataBackup: true,
    errorAlert: true
  })

  const { user } = useAuthStore()
  const location = useLocation()
  const navigate = useNavigate()

  // 组件加载时从云数据库加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true)

        // 初始化系统数据（如果需要）
        await settingsService.initializeSystem()

        // 加载系统设置
        const systemData = await settingsService.getSystemSettings()
        setSystemSettings(systemData)

        // 加载用户偏好
        const preferencesData = await settingsService.getUserPreferences()
        setNotificationSettings(preferencesData)

        // 应用暗色主题设置
        if (systemData.darkMode) {
          document.documentElement.classList.add('dark')
        }

        // 设置表单初始值
        form.setFieldsValue(systemData)

        console.log('✅ 设置加载完成')
      } catch (error) {
        console.error('❌ 设置加载失败:', error)
        notification.error({
          message: '设置加载失败',
          description: '无法从服务器加载设置，将使用默认配置'
        })
      } finally {
        setLoading(false)
      }
    }

    loadSettings()
  }, [form])

  // 根据URL参数设置活动标签页
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search)
    const tab = searchParams.get('tab')
    if (tab && ['profile', 'account', 'system'].includes(tab)) {
      setActiveTab(tab)
    }
  }, [location.search])

  // 更新URL参数
  const handleTabChange = (key: string) => {
    setActiveTab(key)
    const searchParams = new URLSearchParams(location.search)
    searchParams.set('tab', key)
    navigate(`${location.pathname}?${searchParams.toString()}`, { replace: true })
  }

  // 导出配置
  const handleExportConfig = () => {
    try {
      const config = {
        profile: profileForm.getFieldsValue(),
        system: form.getFieldsValue(),
        exportTime: new Date().toISOString(),
        version: '1.0.0'
      }

      const dataStr = JSON.stringify(config, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)

      const link = document.createElement('a')
      link.href = url
      link.download = `系统配置_${new Date().toISOString().slice(0, 10)}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      notification.success({
        message: '配置导出成功',
        description: '配置文件已下载到本地'
      })
    } catch (error) {
      notification.error({
        message: '配置导出失败',
        description: '请重试或联系管理员'
      })
    }
  }

  // 导入配置
  const handleImportConfig = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target?.result as string)

        if (config.profile) {
          profileForm.setFieldsValue(config.profile)
        }
        if (config.system) {
          form.setFieldsValue(config.system)
        }

        notification.success({
          message: '配置导入成功',
          description: '配置已恢复，请检查并保存'
        })
      } catch (error) {
        notification.error({
          message: '配置导入失败',
          description: '配置文件格式错误'
        })
      }
    }
    reader.readAsText(file)
    return false // 阻止默认上传行为
  }

  const onSystemSettingsFinish = async (values: any) => {
    setLoading(true)
    try {
      const result = await settingsService.updateSystemSettings(values)

      if (result.success) {
        // 更新本地状态
        setSystemSettings(values)

        // 应用实时设置
        if (values.darkMode !== systemSettings.darkMode) {
          document.documentElement.classList.toggle('dark', values.darkMode)
        }

        if (values.debugMode !== systemSettings.debugMode) {
          if (values.debugMode) {
            console.log('🔧 调试模式已开启')
          }
        }

        notification.success({
          message: '系统设置保存成功',
          description: result.message
        })
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      notification.error({
        message: '系统设置保存失败',
        description: error instanceof Error ? error.message : '请重试或联系管理员'
      })
    } finally {
      setLoading(false)
    }
  }

  const onProfileFinish = async (values: any) => {
    setLoading(true)
    try {
      // 保存个人信息到本地存储
      localStorage.setItem('profileSettings', JSON.stringify(values))
      
      notification.success({
        message: '个人信息更新成功',
        description: '个人信息已成功更新！'
      })
    } catch (error) {
      notification.error({
        message: '个人信息更新失败',
        description: '请重试或联系管理员'
      })
    } finally {
      setLoading(false)
    }
  }

  const onPasswordFinish = async (values: any) => {
    setLoading(true)
    try {
      const result = await settingsService.changePassword(
        values.currentPassword,
        values.newPassword
      )

      if (result.success) {
        notification.success({
          message: '密码修改成功',
          description: result.message
        })
        setPasswordModalVisible(false)
        passwordForm.resetFields()
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      notification.error({
        message: '密码修改失败',
        description: error instanceof Error ? error.message : '请检查原密码是否正确'
      })
    } finally {
      setLoading(false)
    }
  }

  const uploadProps = {
    name: 'avatar',
    showUploadList: false,
    accept: 'image/*',
    beforeUpload: async (file: File) => {
      const isImage = file.type.startsWith('image/')
      if (!isImage) {
        notification.error({
          message: '文件格式错误',
          description: '只能上传图片文件！'
        })
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        notification.error({
          message: '文件过大',
          description: '图片大小不能超过2MB！'
        })
        return false
      }
      
      // 简化的头像上传逻辑 - 转换为base64并保存到本地存储
      try {
        setLoading(true)

        const reader = new FileReader()
        reader.onload = (e) => {
          const base64 = e.target?.result as string
          setAvatarUrl(base64)
          localStorage.setItem('userAvatar', base64)
          notification.success({
            message: '头像上传成功',
            description: '您的头像已更新并保存到本地！'
          })
          setLoading(false)
        }
        reader.onerror = () => {
          notification.error({
            message: '头像上传失败',
            description: '文件读取失败，请重试'
          })
          setLoading(false)
        }
        reader.readAsDataURL(file)
      } catch (error) {
        console.error('头像上传失败:', error)
        notification.error({
          message: '头像上传失败',
          description: '请重试或联系管理员'
        })
        setLoading(false)
      }
      
      return false // 阻止默认上传行为
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6 transition-colors">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <Title level={1} className="!mb-2 theme-text-primary flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                <SettingOutlined className="text-2xl text-white" />
              </div>
              系统设置
            </Title>
            <Text className="theme-text-secondary text-lg">
              管理系统配置和个人信息
            </Text>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold theme-text-primary mb-1">
              {new Date().toLocaleTimeString()}
            </div>
            <div className="theme-text-secondary">
              {new Date().toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long' 
              })}
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-6">

        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-xl transition-colors">
          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            type="card"
            items={[
              {
                key: 'profile',
                label: '个人信息',
                children: (
            <Card title="个人资料" style={{ maxWidth: 800 }}>
              <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24, marginBottom: 24 }}>
                <div style={{ textAlign: 'center' }}>
                  <Avatar
                    size={80}
                    src={avatarUrl}
                    icon={!avatarUrl && <UserOutlined />}
                  />
                  <div style={{ marginTop: 12 }}>
                    <Upload {...uploadProps}>
                      <Button size="small" icon={<UploadOutlined />} loading={loading}>
                        更换头像
                      </Button>
                    </Upload>
                    {avatarUrl && (
                      <Button
                        size="small"
                        type="text"
                        danger
                        style={{ marginLeft: 8 }}
                        onClick={() => {
                          setAvatarUrl('')
                          localStorage.removeItem('userAvatar')
                          message.success('头像已重置')
                        }}
                      >
                        重置
                      </Button>
                    )}
                  </div>
                </div>
                <div style={{ flex: 1 }}>
                  <Form
                    form={profileForm}
                    layout="vertical"
                    onFinish={onProfileFinish}
                    initialValues={{
                      name: user?.name || '',
                      username: user?.username || '',
                      email: user?.email || '',
                      phone: '',
                      description: ''
                    }}
                  >
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 16 }}>
                      <Form.Item
                        label="姓名"
                        name="name"
                        rules={[{ required: true, message: '请输入姓名' }]}
                      >
                        <Input />
                      </Form.Item>

                      <Form.Item
                        label="用户名"
                        name="username"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input disabled />
                      </Form.Item>

                      <Form.Item
                        label="邮箱"
                        name="email"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱' }
                        ]}
                      >
                        <Input />
                      </Form.Item>

                      <Form.Item
                        label="手机号"
                        name="phone"
                      >
                        <Input />
                      </Form.Item>
                    </div>

                    <Form.Item
                      label="个人描述"
                      name="description"
                    >
                      <TextArea rows={3} placeholder="简单介绍一下自己..." />
                    </Form.Item>

                    <Form.Item>
                      <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                        保存个人信息
                      </Button>
                    </Form.Item>
                  </Form>
                </div>
              </div>
            </Card>
                )
              },
              {
                key: 'account',
                label: '账户设置',
                children: (
                  <>
            <Card title="密码管理" style={{ maxWidth: 800, marginBottom: 16 }}>
              <div className="mb-4">
                <Text type="secondary">
                  为了您的账户安全，建议定期更换密码。密码长度至少6位，建议包含字母、数字和特殊字符。
                </Text>
              </div>
              <Button
                type="primary"
                icon={<LockOutlined />}
                onClick={() => setPasswordModalVisible(true)}
              >
                修改密码
              </Button>
            </Card>

            <Card title="安全设置" style={{ maxWidth: 800, marginBottom: 16 }}>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">会话超时</div>
                    <div className="text-sm text-gray-500">设置自动登出时间（当前仅显示，实际超时由系统控制）</div>
                  </div>
                  <Select
                    defaultValue="30"
                    style={{ width: 120 }}
                    onChange={(value) => {
                      localStorage.setItem('sessionTimeout', value)
                      message.success(`会话超时已设置为${value}分钟`)
                    }}
                  >
                    <Option value="15">15分钟</Option>
                    <Option value="30">30分钟</Option>
                    <Option value="60">1小时</Option>
                    <Option value="120">2小时</Option>
                  </Select>
                </div>
                <Divider />
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">安全提醒</div>
                    <div className="text-sm text-gray-500">在浏览器中显示安全相关提醒</div>
                  </div>
                  <Switch
                    defaultChecked={localStorage.getItem('securityReminder') !== 'false'}
                    onChange={(checked) => {
                      localStorage.setItem('securityReminder', checked.toString())
                      message.success(checked ? '已开启安全提醒' : '已关闭安全提醒')
                    }}
                  />
                </div>
              </div>
            </Card>

            <Card title="账户信息" style={{ maxWidth: 800 }}>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">用户ID:</span>
                  <span>{user?.id || 'admin_001'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">账户类型:</span>
                  <span>超级管理员</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">创建时间:</span>
                  <span>2024-01-01 10:00:00</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">最后登录:</span>
                  <span>{new Date().toLocaleString()}</span>
                </div>
              </div>
            </Card>
                  </>
                )
              },
              {
                key: 'system',
                label: '系统配置',
                children: (
            <Card title="基础设置" style={{ marginBottom: 16 }}>
              <Form
                form={form}
                layout="vertical"
                onFinish={onSystemSettingsFinish}
                initialValues={systemSettings}
              >
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="系统名称"
                    name="systemName"
                    rules={[{ required: true, message: '请输入系统名称' }]}
                  >
                    <Input />
                  </Form.Item>

                  <Form.Item
                    label="系统版本"
                    name="version"
                  >
                    <Input disabled />
                  </Form.Item>

                  <Form.Item
                    label="最大用户数"
                    name="maxUsers"
                    tooltip="系统支持的最大用户数量"
                  >
                    <InputNumber min={1} max={10000} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="会话超时(分钟)"
                    name="sessionTimeout"
                    tooltip="用户无操作后自动登出的时间"
                  >
                    <InputNumber min={5} max={120} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="日志级别"
                    name="logLevel"
                  >
                    <Select>
                      <Option value="debug">Debug</Option>
                      <Option value="info">Info</Option>
                      <Option value="warn">Warning</Option>
                      <Option value="error">Error</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    label="备份频率"
                    name="backupFrequency"
                  >
                    <Select>
                      <Option value="hourly">每小时</Option>
                      <Option value="daily">每天</Option>
                      <Option value="weekly">每周</Option>
                      <Option value="monthly">每月</Option>
                    </Select>
                  </Form.Item>
                </div>

                <Divider />

                <Title level={4}>功能开关</Title>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="数据自动刷新"
                    name="autoRefresh"
                    valuePropName="checked"
                    tooltip="开启后页面数据将自动刷新"
                  >
                    <Switch
                      onChange={async (checked) => {
                        const newSettings = { ...systemSettings, autoRefresh: checked }
                        const result = await settingsService.updateSystemSettings({ autoRefresh: checked })
                        if (result.success) {
                          setSystemSettings(newSettings)
                          message.success(checked ? '已开启数据自动刷新' : '已关闭数据自动刷新')
                        } else {
                          message.error('设置更新失败: ' + result.message)
                        }
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    label="暗色主题"
                    name="darkMode"
                    valuePropName="checked"
                    tooltip="切换页面主题色彩"
                  >
                    <Switch
                      onChange={async (checked) => {
                        const newSettings = { ...systemSettings, darkMode: checked }
                        const result = await settingsService.updateSystemSettings({ darkMode: checked })
                        if (result.success) {
                          setSystemSettings(newSettings)
                          document.documentElement.classList.toggle('dark', checked)
                          message.success(checked ? '已切换到暗色主题' : '已切换到亮色主题')
                        } else {
                          message.error('设置更新失败: ' + result.message)
                        }
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    label="调试模式"
                    name="debugMode"
                    valuePropName="checked"
                    tooltip="开启后在控制台显示详细日志"
                  >
                    <Switch
                      onChange={async (checked) => {
                        const newSettings = { ...systemSettings, debugMode: checked }
                        const result = await settingsService.updateSystemSettings({ debugMode: checked })
                        if (result.success) {
                          setSystemSettings(newSettings)
                          if (checked) {
                            console.log('🔧 调试模式已开启')
                          }
                          message.success(checked ? '已开启调试模式' : '已关闭调试模式')
                        } else {
                          message.error('设置更新失败: ' + result.message)
                        }
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    label="数据缓存"
                    name="enableCache"
                    valuePropName="checked"
                    tooltip="开启后提升页面加载速度"
                  >
                    <Switch
                      onChange={async (checked) => {
                        const newSettings = { ...systemSettings, enableCache: checked }
                        const result = await settingsService.updateSystemSettings({ enableCache: checked })
                        if (result.success) {
                          setSystemSettings(newSettings)
                          message.success(checked ? '已开启数据缓存' : '已关闭数据缓存')
                        } else {
                          message.error('设置更新失败: ' + result.message)
                        }
                      }}
                    />
                  </Form.Item>
                </div>

                <Form.Item style={{ marginTop: 24 }}>
                  <Space wrap>
                    <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                      保存配置
                    </Button>
                    <Button onClick={() => form.resetFields()}>
                      重置表单
                    </Button>
                    <Button
                      icon={<DeleteOutlined />}
                      onClick={() => {
                        Modal.confirm({
                          title: '清除缓存',
                          content: '确定要清除所有本地缓存数据吗？这将清除已保存的设置和临时数据。',
                          onOk: () => {
                            localStorage.clear()
                            sessionStorage.clear()
                            message.success('缓存已清除')
                            // 🔥 不自动刷新页面，让用户手动刷新
                          }
                        })
                      }}
                    >
                      清除缓存
                    </Button>
                    <Button icon={<DownloadOutlined />} onClick={handleExportConfig}>
                      导出配置
                    </Button>
                    <Upload
                      accept=".json"
                      showUploadList={false}
                      beforeUpload={handleImportConfig}
                    >
                      <Button icon={<UploadOutlined />}>
                        导入配置
                      </Button>
                    </Upload>
                  </Space>
                </Form.Item>
              </Form>

              <Card title="系统信息" style={{ marginTop: 16 }}>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>
                  <div>
                    <Text type="secondary">浏览器版本</Text>
                    <div>{navigator.userAgent.split(' ').pop()}</div>
                  </div>
                  <div>
                    <Text type="secondary">屏幕分辨率</Text>
                    <div>{window.screen.width} × {window.screen.height}</div>
                  </div>
                  <div>
                    <Text type="secondary">系统语言</Text>
                    <div>{navigator.language}</div>
                  </div>
                  <div>
                    <Text type="secondary">时区</Text>
                    <div>{Intl.DateTimeFormat().resolvedOptions().timeZone}</div>
                  </div>
                  <div>
                    <Text type="secondary">在线状态</Text>
                    <div style={{ color: navigator.onLine ? '#52c41a' : '#ff4d4f' }}>
                      {navigator.onLine ? '在线' : '离线'}
                    </div>
                  </div>
                  <div>
                    <Text type="secondary">本地存储</Text>
                    <div>{Object.keys(localStorage).length} 项</div>
                  </div>
                </div>

                <div style={{ marginTop: 16 }}>
                  <Space>
                    <Button
                      size="small"
                      onClick={() => {
                        const info = {
                          userAgent: navigator.userAgent,
                          screen: `${window.screen.width}×${window.screen.height}`,
                          language: navigator.language,
                          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                          online: navigator.onLine,
                          localStorage: Object.keys(localStorage).length
                        }
                        navigator.clipboard.writeText(JSON.stringify(info, null, 2))
                        message.success('系统信息已复制到剪贴板')
                      }}
                    >
                      复制系统信息
                    </Button>
                    <Button
                      size="small"
                      onClick={() => {
                        Modal.confirm({
                          title: '确认刷新',
                          content: '确定要刷新页面吗？未保存的数据可能会丢失。',
                          onOk: () => window.location.reload()
                        })
                      }}
                    >
                      刷新页面
                    </Button>
                  </Space>
                </div>
              </Card>
            </Card>
                )
              },
              {
                key: 'notifications',
                label: '通知设置',
                children: (
            <Card title="通知配置">
              <Form
                layout="vertical"
                initialValues={notificationSettings}
                onFinish={(values) => {
                  // 更新状态
                  setNotificationSettings(values)

                  // 保存通知设置到本地存储
                  localStorage.setItem('notificationSettings', JSON.stringify(values))

                  // 如果开启了浏览器通知，请求权限
                  if (values.browserEnabled && 'Notification' in window) {
                    Notification.requestPermission().then(permission => {
                      if (permission === 'granted') {
                        new Notification('通知设置', {
                          body: '浏览器通知已开启！',
                          icon: '/favicon.ico'
                        })
                      }
                    })
                  }

                  // 记录设置变更日志
                  const changeLog = {
                    action: 'notification_settings_update',
                    timestamp: new Date().toISOString(),
                    user: user?.username || 'admin',
                    changes: values
                  }
                  const logs = JSON.parse(localStorage.getItem('adminLogs') || '[]')
                  logs.push(changeLog)
                  localStorage.setItem('adminLogs', JSON.stringify(logs))

                  message.success('通知设置保存成功！')
                }}
              >
                <Title level={4}>通知方式</Title>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16, marginBottom: 24 }}>
                  <Form.Item
                    label="浏览器通知"
                    name="browserEnabled"
                    valuePropName="checked"
                    tooltip="在浏览器中显示桌面通知"
                  >
                    <Switch
                      onChange={async (checked) => {
                        const newSettings = { ...notificationSettings, browserEnabled: checked }
                        const result = await settingsService.updateUserPreferences({ browserEnabled: checked })
                        if (result.success) {
                          setNotificationSettings(newSettings)

                          if (checked && 'Notification' in window) {
                            Notification.requestPermission().then(permission => {
                              if (permission === 'granted') {
                                new Notification('浏览器通知', {
                                  body: '浏览器通知已开启！',
                                  icon: '/favicon.ico'
                                })
                              }
                            })
                          }
                          message.success(checked ? '已开启浏览器通知' : '已关闭浏览器通知')
                        } else {
                          message.error('设置更新失败: ' + result.message)
                        }
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    label="页面提醒"
                    name="pageAlert"
                    valuePropName="checked"
                    tooltip="在页面中显示提醒消息"
                  >
                    <Switch
                      onChange={(checked) => {
                        const newSettings = { ...notificationSettings, pageAlert: checked }
                        setNotificationSettings(newSettings)
                        localStorage.setItem('notificationSettings', JSON.stringify(newSettings))
                        message.success(checked ? '已开启页面提醒' : '已关闭页面提醒')
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    label="声音提醒"
                    name="soundAlert"
                    valuePropName="checked"
                    tooltip="重要操作时播放提示音"
                  >
                    <Switch
                      onChange={(checked) => {
                        const newSettings = { ...notificationSettings, soundAlert: checked }
                        setNotificationSettings(newSettings)
                        localStorage.setItem('notificationSettings', JSON.stringify(newSettings))

                        if (checked) {
                          // 播放测试音效
                          const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT')
                          audio.volume = 0.3
                          audio.play().catch(() => {})
                        }
                        message.success(checked ? '已开启声音提醒' : '已关闭声音提醒')
                      }}
                    />
                  </Form.Item>
                </div>

                <Divider />

                <Title level={4}>通知类型</Title>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="系统告警"
                    name="systemAlert"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="用户活动"
                    name="userActivity"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="数据备份"
                    name="dataBackup"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="错误报告"
                    name="errorAlert"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </div>

                <Form.Item style={{ marginTop: 24 }}>
                  <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                    保存通知设置
                  </Button>
                </Form.Item>
              </Form>
            </Card>
                )
              }
            ]}
          />
        </div>
      </div>

      {/* 密码修改模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false)
          passwordForm.resetFields()
        }}
        footer={null}
        width={500}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={onPasswordFinish}
        >
          <Form.Item
            label="当前密码"
            name="currentPassword"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password
              placeholder="请输入当前密码"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6位' },
              {
                pattern: /^(?=.*[a-zA-Z])(?=.*\d)/,
                message: '密码必须包含字母和数字'
              }
            ]}
          >
            <Input.Password
              placeholder="请输入新密码（至少6位，包含字母和数字）"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            label="确认新密码"
            name="confirmPassword"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'))
                }
              })
            ]}
          >
            <Input.Password
              placeholder="请再次输入新密码"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item className="mb-0 mt-6">
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<LockOutlined />}
              >
                确认修改
              </Button>
              <Button
                onClick={() => {
                  setPasswordModalVisible(false)
                  passwordForm.resetFields()
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Settings