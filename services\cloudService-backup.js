/**
 * 云开发服务层
 * 基于微信云开发的数据库操作
 */

// 确保模块正确加载

// 全局云服务实例
let cloudServiceInstance = null;

/**
 * 云数据库服务
 */
class CloudService {
  constructor() {
    this.db = null;
    this.initialized = false;
    // 不在构造函数中立即初始化，等待云开发环境完全就绪
  }

  /**
   * 初始化云数据库（延迟初始化）
   */
  init() {
    return new Promise((resolve, reject) => {
      try {
        // 检查微信云开发是否已初始化
        if (wx.cloud && typeof wx.cloud.database === 'function') {
          this.db = wx.cloud.database();
          this.initialized = true;

          resolve(this.db);
        } else {

          // 延迟重试初始化
          setTimeout(() => {
            this.init().then(resolve).catch(reject);
          }, 200);
        }
      } catch (error) {
        console.error('云数据库初始化失败:', error);
        // 重试初始化
        setTimeout(() => {
          this.init().then(resolve).catch(reject);
        }, 500);
      }
    });
  }

  /**
   * 确保数据库已初始化
   */
  async ensureDbInitialized() {
    if (!this.initialized || !this.db) {
      await this.init();
    }
    return this.db;
  }

  /**
   * 用户登录
   */
  async login(code) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: { code }
      });

      if (result.result && result.result.success) {
        return result.result;
      } else {
        throw new Error(result.result?.error || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取当前用户ID
   */
  getCurrentUserId() {
    return new Promise((resolve, reject) => {
      // 调用云函数获取真实用户ID
      wx.cloud.callFunction({
        name: 'getUserId',
        success: (res) => {

          resolve(res.result.openid);
        },
        fail: (error) => {
          console.error('云函数调用失败:', error);
          reject(error);
        }
      });
    });
  }

  // ==================== 用户信息管理 ====================

  /**
   * 保存用户信息
   */
  async saveUserInfo(userInfo) {
    try {
      const userId = await this.getCurrentUserId();
      
      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();
      
      const result = await db.collection('users').doc(userId).set({
        data: {
          ...userInfo,
          updateTime: new Date(),
          createTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('保存用户信息失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    try {
      const userId = await this.getCurrentUserId();

      // 尝试从云数据库获取用户信息
      try {
        // 确保数据库已初始化
        const db = await this.ensureDbInitialized();
        
        const result = await db.collection('users').doc(userId).get();

        if (result.data) {

          return { success: true, data: result.data };
        }
      } catch (dbError) {

      }

      // 用户不存在，自动创建默认用户

      const defaultUserInfo = {
        name: '新用户',
        avatar: '',
        phone: '',
        email: '',
        school: '',
        subject: '',
        grade: ''
      };

      const createResult = await this.createUser(defaultUserInfo);
      if (createResult.success) {

        return { success: true, data: { _id: userId, ...defaultUserInfo } };
      } else {
        console.error('创建用户失败:', createResult.error);
        return { success: false, message: '创建用户失败' };
      }

    } catch (error) {
      console.error('获取用户信息过程中发生错误:', error);
      return { success: false, error };
    }
  }

  /**
   * 创建用户
   */
  async createUser(userData) {
    try {
      const userId = await this.getCurrentUserId();

      const result = await this.db.collection('users').doc(userId).set({
        data: {
          ...userData,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('创建用户失败:', error);
      return { success: false, error };
    }
  }

  // ==================== 班级管理 ====================

  /**
   * 创建班级
   */
  async createClass(classData) {
    try {
      const userId = await this.getCurrentUserId();
      
      const result = await this.db.collection('classes').add({
        data: {
          ...classData,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('创建班级失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 获取班级列表
   */
  async getClassList() {
    try {
      const userId = await this.getCurrentUserId();
      
      const result = await this.db.collection('classes')
        .where({
          teacherId: userId
        })
        .orderBy('createTime', 'desc')
        .get();

      return { success: true, data: result.data };
    } catch (error) {
      console.error('获取班级列表失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 更新班级信息
   */
  async updateClass(classId, classData) {
    try {
      const result = await this.db.collection('classes').doc(classId).update({
        data: {
          ...classData,
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('更新班级失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 删除班级
   */
  async deleteClass(classId) {
    try {
      const result = await this.db.collection('classes').doc(classId).remove();
      return { success: true, data: result };
    } catch (error) {
      console.error('删除班级失败:', error);
      return { success: false, error };
    }
  }

  // ==================== 学生管理 ====================

  /**
   * 添加学生
   */
  async addStudent(studentData) {
    try {
      const userId = await this.getCurrentUserId();
      
      const result = await this.db.collection('students').add({
        data: {
          ...studentData,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('添加学生失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 批量添加学生
   */
  async batchAddStudents(studentsData) {
    try {
      const userId = await this.getCurrentUserId();
      const batch = [];

      studentsData.forEach(student => {
        batch.push({
          ...student,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        });
      });

      const result = await this.db.collection('students').add(batch);
      return { success: true, data: result };
    } catch (error) {
      console.error('批量添加学生失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 获取学生列表
   */
  async getStudentList(classId = null) {
    try {
      const userId = await this.getCurrentUserId();

      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();

      // 从云数据库获取学生列表
      let query = db.collection('students').where({
        teacherId: userId
      });

      if (classId) {
        query = query.where({
          classId: classId
        });
      }

      const result = await query.orderBy('createTime', 'desc').get();

      return { success: true, data: result.data };
    } catch (error) {
      console.error('获取学生列表失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 获取学生详情
   */
  async getStudentDetail(studentId) {
    try {
      const userId = await this.getCurrentUserId();

      const result = await this.db.collection('students')
        .where({
          _id: studentId,
          teacherId: userId
        })
        .get();

      if (result.data.length === 0) {
        return { success: false, error: '学生不存在' };
      }

      return { success: true, data: result.data[0] };
    } catch (error) {
      console.error('获取学生详情失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 更新学生信息
   */
  async updateStudent(studentId, studentData) {
    try {
      const result = await this.db.collection('students').doc(studentId).update({
        data: {
          ...studentData,
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('更新学生失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 删除学生
   */
  async deleteStudent(studentId) {
    try {
      const result = await this.db.collection('students').doc(studentId).remove();
      return { success: true, data: result };
    } catch (error) {
      console.error('删除学生失败:', error);
      return { success: false, error };
    }
  }

  // ==================== 记录管理 ====================

  /**
   * 添加记录
   */
  async addRecord(recordData) {
    try {
      const userId = await this.getCurrentUserId();

      const result = await this.db.collection('records').add({
        data: {
          ...recordData,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('添加记录失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 创建记录
   */
  async createRecord(recordData) {
    try {
      const userId = await this.getCurrentUserId();
      
      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();

      const result = await db.collection('records').add({
        data: {
          ...recordData,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('创建记录失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 获取记录列表
   */
  async getRecordList(filters = {}) {
    try {
      const userId = await this.getCurrentUserId();
      
      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();
      
      // 构建查询条件
      const whereConditions = {
        teacherId: userId
      };

      // 添加筛选条件
      if (filters.studentId) {
        whereConditions.studentId = filters.studentId;
      }
      if (filters.classId) {
        whereConditions.classId = filters.classId;
      }
      if (filters.behaviorType) {
        whereConditions.behaviorType = filters.behaviorType;
      }

      let query = db.collection('records').where(whereConditions);

      // 添加时间范围筛选
      if (filters.startDate && filters.endDate) {
        query = query.where({
          createTime: db.command.gte(filters.startDate).and(db.command.lte(filters.endDate))
        });
      } else if (filters.startDate) {
        query = query.where({
          createTime: db.command.gte(filters.startDate)
        });
      } else if (filters.endDate) {
        query = query.where({
          createTime: db.command.lte(filters.endDate)
        });
      }

      // 分页处理
      const pageSize = filters.pageSize || 20;
      const page = filters.page || 1;
      const skip = (page - 1) * pageSize;

      const result = await query
        .orderBy('createTime', 'desc')
        .skip(skip)
        .limit(pageSize)
        .get();

      return { success: true, data: result.data };
    } catch (error) {
      console.error('获取记录列表失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 更新记录
   */
  async updateRecord(recordId, recordData) {
    try {
      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();
      
      const result = await db.collection('records').doc(recordId).update({
        data: {
          ...recordData,
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('更新记录失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 删除记录
   */
  async deleteRecord(recordId) {
    try {
      const result = await this.db.collection('records').doc(recordId).remove();
      return { success: true, data: result };
    } catch (error) {
      console.error('删除记录失败:', error);
      return { success: false, error };
    }
  }

  // ==================== AI评语生成 ====================

  /**
   * 生成AI评语
   */
  async generateAIComment(studentId, options = {}) {
    try {

      // 获取学生信息
      const studentResult = await this.getStudentDetail(studentId);
      if (!studentResult.success) {
        throw new Error('获取学生信息失败');
      }
      const student = studentResult.data;

      // 获取学生的行为记录
      const recordsResult = await this.getRecordList({
        studentId: studentId,
        startDate: options.startDate,
        endDate: options.endDate
      });

      if (!recordsResult.success) {
        throw new Error('获取学生记录失败');
      }

      const records = recordsResult.data || [];

      // 构建提示词
      const prompt = this.buildCommentPrompt(student, records, options);

      // 调用豆包API云函数
      const result = await wx.cloud.callFunction({
        name: 'callDoubaoAPI',
        data: {
          prompt: prompt,
          style: options.style || 'warm',
          length: options.length || 'medium',
          temperature: 0.7,
          max_tokens: 300
        }
      });

      if (result.result && result.result.success) {
        return {
          success: true,
          data: {
            studentId: studentId,
            studentName: student.name,
            className: student.className,
            comment: result.result.data.content,
            style: options.style || 'warm',
            length: options.length || 'medium',
            generateTime: new Date().toLocaleString(),
            usage: result.result.data.usage
          }
        };
      } else {
        throw new Error(result.result?.error || 'AI评语生成失败');
      }

    } catch (error) {
      console.error('生成AI评语失败:', error);
      return {
        success: false,
        error: error.message || '生成AI评语失败'
      };
    }
  }

  /**
   * 构建评语生成提示词（优化版）
   */
  buildCommentPrompt(student, records, options) {
    const {
      style = 'warm',
      length = 'medium',
      focus = [],
      customRequirement = '',
      includeAdvice = true,
      includeEncouragement = true,
      startDate,
      endDate
    } = options;

    // 构建专业的教师身份设定
    let prompt = `你是一位有15年教学经验的资深教师，擅长用温暖而专业的语言为学生写评语。\n\n`;

    // 学生基本信息
    prompt += `学生信息：\n`;
    prompt += `- 姓名：${student.name}\n`;
    prompt += `- 班级：${student.className || '未指定班级'}\n`;

    // 时间范围
    if (startDate && endDate) {
      const start = new Date(startDate).toLocaleDateString();
      const end = new Date(endDate).toLocaleDateString();
      prompt += `- 评价时间段：${start} 至 ${end}\n`;
    }

    prompt += `\n`;

    // 学生表现分析（更详细的分类）
    if (records.length > 0) {
      prompt += `学生表现记录分析：\n`;

      // 按类型分类记录
      const recordsByType = {
        positive: records.filter(r => r.behaviorType === 'positive'),
        academic: records.filter(r => r.behaviorType === 'academic'),
        social: records.filter(r => r.behaviorType === 'social'),
        creative: records.filter(r => r.behaviorType === 'creative'),
        negative: records.filter(r => r.behaviorType === 'negative')
      };

      // 优势表现
      const strengths = [];
      if (recordsByType.positive.length > 0) {
        strengths.push(`品德表现：${recordsByType.positive.map(r => r.action).join('、')}`);
      }
      if (recordsByType.academic.length > 0) {
        strengths.push(`学习表现：${recordsByType.academic.map(r => r.action).join('、')}`);
      }
      if (recordsByType.social.length > 0) {
        strengths.push(`社交能力：${recordsByType.social.map(r => r.action).join('、')}`);
      }
      if (recordsByType.creative.length > 0) {
        strengths.push(`创新能力：${recordsByType.creative.map(r => r.action).join('、')}`);
      }

      if (strengths.length > 0) {
        prompt += `【优势表现】\n${strengths.join('\n')}\n\n`;
      }

      // 改进空间
      if (recordsByType.negative.length > 0) {
        prompt += `【改进空间】\n${recordsByType.negative.map(r => r.action).join('、')}\n\n`;
      }

      // 记录频次分析
      const recordCount = records.length;
      const positiveRatio = (recordsByType.positive.length + recordsByType.academic.length + recordsByType.social.length + recordsByType.creative.length) / recordCount;

      prompt += `【表现总结】\n`;
      prompt += `- 记录总数：${recordCount}条\n`;
      prompt += `- 积极表现占比：${Math.round(positiveRatio * 100)}%\n`;

    } else {
      prompt += `【表现记录】\n暂无具体行为记录，请基于学生的一般发展特点生成评语。\n\n`;
    }

    // 关注重点
    if (focus.length > 0) {
      prompt += `【重点关注领域】\n${focus.join('、')}\n\n`;
    }

    // 自定义要求
    if (customRequirement) {
      prompt += `【特殊要求】\n${customRequirement}\n\n`;
    }

    // 评语生成要求
    prompt += `【评语生成要求】\n`;
    prompt += `1. 语言风格：${this.getStyleDescription(style)}\n`;
    prompt += `2. 评语长度：${this.getLengthDescription(length)}\n`;
    prompt += `3. 结构要求：\n`;
    prompt += `   - 开头：总体评价（肯定学生的整体表现）\n`;
    prompt += `   - 中间：具体表现（基于记录的详细分析）\n`;

    if (includeAdvice) {
      prompt += `   - 建议：具体可行的改进建议\n`;
    }
    if (includeEncouragement) {
      prompt += `   - 结尾：温暖的鼓励和期望\n`;
    }

    prompt += `4. 内容要求：\n`;
    prompt += `   - 基于真实记录，不得编造事实\n`;
    prompt += `   - 语言温暖而专业，体现教师关爱\n`;
    prompt += `   - 避免空洞的套话，要有针对性\n`;
    prompt += `   - 字数控制在${this.getLengthRange(length)}字以内\n\n`;

    prompt += `请基于以上信息，为${student.name}同学生成一份专业、温暖、个性化的学生评语：`;

    return prompt;
  }

  /**
   * 获取风格描述（优化版）
   */
  getStyleDescription(style) {
    const styleMap = {
      'warm': '温暖亲切，充满关爱，用温和的语言表达对学生的关心和期望',
      'formal': '正式严谨，客观专业，用规范的教育语言进行客观评价',
      'encouraging': '积极向上，充满鼓励，重点突出学生的进步和潜力',
      'detailed': '详细具体，深入分析，全面细致地分析学生各方面表现'
    };
    return styleMap[style] || '温暖亲切，充满关爱';
  }

  /**
   * 获取长度描述
   */
  getLengthDescription(length) {
    const lengthMap = {
      'short': '简洁明了，重点突出',
      'medium': '适中详细，全面均衡',
      'long': '详细全面，深入分析'
    };
    return lengthMap[length] || '适中详细，全面均衡';
  }

  /**
   * 获取长度范围
   */
  getLengthRange(length) {
    const rangeMap = {
      'short': '50-80',
      'medium': '100-150',
      'long': '200-300'
    };
    return rangeMap[length] || '100-150';
  }

  /**
   * 获取评语统计信息
   */
  async getCommentStatistics() {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 获取总评语数
      const totalResult = await db.collection('comments')
        .where({ teacherId: userId })
        .count();

      // 获取本月评语数
      const now = new Date();
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const monthResult = await db.collection('comments')
        .where({
          teacherId: userId,
          createTime: db.command.gte(monthStart)
        })
        .count();

      // 获取本周评语数
      const weekStart = new Date();
      weekStart.setDate(weekStart.getDate() - weekStart.getDay());
      weekStart.setHours(0, 0, 0, 0);
      const weekResult = await db.collection('comments')
        .where({
          teacherId: userId,
          createTime: db.command.gte(weekStart)
        })
        .count();

      // 获取今日评语数
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);
      const todayResult = await db.collection('comments')
        .where({
          teacherId: userId,
          createTime: db.command.gte(todayStart)
        })
        .count();

      // 获取风格分布
      const styleStats = await this.getCommentStyleStats(userId, db);

      return {
        success: true,
        data: {
          total: totalResult.total || 0,
          thisMonth: monthResult.total || 0,
          thisWeek: weekResult.total || 0,
          today: todayResult.total || 0,
          styleDistribution: styleStats
        }
      };
    } catch (error) {
      console.error('获取评语统计失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取评语风格统计
   */
  async getCommentStyleStats(userId, db) {
    try {
      const styles = ['warm', 'formal', 'encouraging', 'detailed'];
      const styleStats = {};

      for (const style of styles) {
        const result = await db.collection('comments')
          .where({
            teacherId: userId,
            style: style
          })
          .count();
        styleStats[style] = result.total || 0;
      }

      return styleStats;
    } catch (error) {
      console.error('获取风格统计失败:', error);
      return {};
    }
  }

  /**
   * 获取学生行为记录统计
   */
  async getRecordStatistics() {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 获取总记录数
      const totalResult = await db.collection('records')
        .where({ teacherId: userId })
        .count();

      // 获取本月记录数
      const now = new Date();
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const monthResult = await db.collection('records')
        .where({
          teacherId: userId,
          createTime: db.command.gte(monthStart)
        })
        .count();

      // 获取记录类型分布
      const typeStats = await this.getRecordTypeStats(userId, db);

      return {
        success: true,
        data: {
          total: totalResult.total || 0,
          thisMonth: monthResult.total || 0,
          typeDistribution: typeStats
        }
      };
    } catch (error) {
      console.error('获取记录统计失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取记录类型统计
   */
  async getRecordTypeStats(userId, db) {
    try {
      const types = ['positive', 'negative', 'neutral'];
      const typeStats = {};

      for (const type of types) {
        const result = await db.collection('records')
          .where({
            teacherId: userId,
            type: type
          })
          .count();
        typeStats[type] = result.total || 0;
      }

      return typeStats;
    } catch (error) {
      console.error('获取类型统计失败:', error);
      return {};
    }
  }

  /**
   * 保存评语到云数据库
   */
  async saveComment(commentData) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      const result = await db.collection('comments').add({
        data: {
          ...commentData,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('保存评语失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取评语详情
   */
  async getCommentDetail(commentId) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      const result = await db.collection('comments')
        .where({
          _id: commentId,
          teacherId: userId
        })
        .get();

      if (result.data.length === 0) {
        return { success: false, error: '评语不存在或无权限访问' };
      }

      return { success: true, data: result.data[0] };
    } catch (error) {
      console.error('获取评语详情失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 更新评语
   */
  async updateComment(commentId, updateData) {
    try {
      const db = await this.ensureDbInitialized();

      const result = await db.collection('comments').doc(commentId).update({
        data: {
          ...updateData,
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('更新评语失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 删除评语
   */
  async deleteComment(commentId) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 先检查评语是否存在且属于当前用户
      const checkResult = await db.collection('comments')
        .where({
          _id: commentId,
          teacherId: userId
        })
        .get();

      if (checkResult.data.length === 0) {
        return { success: false, error: '评语不存在或无权限删除' };
      }

      // 删除评语
      const result = await db.collection('comments').doc(commentId).remove();
      return { success: true, data: result };
    } catch (error) {
      console.error('删除评语失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取学生的行为记录
   */
  async getStudentRecords(studentId, options = {}) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      let query = db.collection('records').where({
        teacherId: userId,
        studentId: studentId
      });

      // 时间范围筛选
      if (options.startDate && options.endDate) {
        query = query.where({
          createTime: db.command.gte(new Date(options.startDate))
            .and(db.command.lte(new Date(options.endDate)))
        });
      }

      const result = await query
        .orderBy('createTime', 'desc')
        .limit(50)
        .get();

      return { success: true, data: result.data };
    } catch (error) {
      console.error('获取学生记录失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 重新生成评语
   */
  async regenerateComment(commentId) {
    try {
      const db = await this.ensureDbInitialized();

      // 获取原评语信息
      const commentResult = await db.collection('comments').doc(commentId).get();
      if (!commentResult.data) {
        throw new Error('评语不存在');
      }

      const originalComment = commentResult.data;

      // 获取学生记录
      const recordsResult = await this.getStudentRecords(originalComment.studentId, {
        startDate: originalComment.generateOptions?.startDate,
        endDate: originalComment.generateOptions?.endDate
      });

      // 这里应该调用AI服务重新生成
      // 暂时返回模拟数据
      const newContent = `重新生成的评语内容 - ${new Date().toLocaleString()}`;

      // 更新评语
      const updateResult = await this.updateComment(commentId, {
        content: newContent,
        regenerateTime: new Date()
      });

      if (updateResult.success) {
        return {
          success: true,
          data: {
            ...originalComment,
            content: newContent,
            regenerateTime: new Date()
          }
        };
      } else {
        throw new Error(updateResult.error);
      }

    } catch (error) {
      console.error('重新生成评语失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 批量删除评语
   */
  async batchDeleteComments(commentIds) {
    try {
      const db = await this.ensureDbInitialized();
      const batch = db.batch();

      commentIds.forEach(id => {
        batch.delete(db.collection('comments').doc(id));
      });

      const result = await batch.commit();
      return { success: true, data: result };
    } catch (error) {
      console.error('批量删除评语失败:', error);
      return { success: false, error: error.message };
    }
  }
}

// 创建全局实例
const cloudService = new CloudService();
cloudServiceInstance = cloudService;

/**
 * 获取云服务实例的全局函数
 * 这个函数可以在任何地方调用来获取云服务实例
 */
function getCloudService() {
  if (!cloudServiceInstance) {

    cloudServiceInstance = new CloudService();
  }
  return cloudServiceInstance;
}

// 将函数挂载到全局
if (typeof global !== 'undefined') {
  global.getCloudService = getCloudService;
}

// 导出模块
module.exports = {
  cloudService,
  CloudService,
  getCloudService
};
