/**
 * AI费用计算服务
 * 负责根据不同模型的定价计算AI调用费用
 */

class CostCalculationService {
  constructor() {
    // 默认定价配置（元/千tokens）
    this.defaultPricing = {
      'doubao-seed-1-6-flash-250715': {
        inputPrice: 0.00007,   // 输入：0.00007元/千tokens
        outputPrice: 0.0003    // 输出：0.0003元/千tokens
      },
      'doubao-pro-32k': {
        inputPrice: 0.00075,   // 输入：0.00075元/千tokens
        outputPrice: 0.0030    // 输出：0.0030元/千tokens
      },
      'doubao-pro-4k': {
        inputPrice: 0.00075,
        outputPrice: 0.0030
      },
      'gpt-4-turbo': {
        inputPrice: 0.01,      // OpenAI定价示例
        outputPrice: 0.03
      },
      'gpt-3.5-turbo': {
        inputPrice: 0.0015,
        outputPrice: 0.002
      }
    }
  }

  /**
   * 计算AI调用费用
   * @param {Object} params 计算参数
   * @param {string} params.model 模型名称
   * @param {number} params.inputTokens 输入tokens数量
   * @param {number} params.outputTokens 输出tokens数量
   * @param {number} params.inputPrice 自定义输入定价（可选）
   * @param {number} params.outputPrice 自定义输出定价（可选）
   * @returns {Object} 费用计算结果
   */
  calculateCost({
    model,
    inputTokens = 0,
    outputTokens = 0,
    inputPrice = null,
    outputPrice = null
  }) {
    try {
      // 获取模型定价
      const pricing = this.getModelPricing(model, inputPrice, outputPrice)
      
      // 计算费用（定价是按千tokens计算）
      const inputCost = (inputTokens / 1000) * pricing.inputPrice
      const outputCost = (outputTokens / 1000) * pricing.outputPrice
      const totalCost = inputCost + outputCost

      return {
        success: true,
        data: {
          model,
          inputTokens,
          outputTokens,
          totalTokens: inputTokens + outputTokens,
          inputPrice: pricing.inputPrice,
          outputPrice: pricing.outputPrice,
          inputCost: Math.round(inputCost * 100000) / 100000, // 保留5位小数
          outputCost: Math.round(outputCost * 100000) / 100000,
          totalCost: Math.round(totalCost * 100000) / 100000,
          currency: 'CNY'
        }
      }
    } catch (error) {
      console.error('费用计算失败:', error)
      return {
        success: false,
        error: error.message,
        data: {
          model,
          inputTokens,
          outputTokens,
          totalTokens: inputTokens + outputTokens,
          inputCost: 0,
          outputCost: 0,
          totalCost: 0
        }
      }
    }
  }

  /**
   * 获取模型定价
   * @param {string} model 模型名称
   * @param {number} customInputPrice 自定义输入定价
   * @param {number} customOutputPrice 自定义输出定价
   * @returns {Object} 定价信息
   */
  getModelPricing(model, customInputPrice = null, customOutputPrice = null) {
    // 优先使用自定义定价
    if (customInputPrice !== null && customOutputPrice !== null) {
      return {
        inputPrice: customInputPrice,
        outputPrice: customOutputPrice
      }
    }

    // 使用默认定价
    const defaultPrice = this.defaultPricing[model]
    if (defaultPrice) {
      return defaultPrice
    }

    // 如果没有找到对应模型，使用通用定价
    console.warn(`未找到模型 ${model} 的定价配置，使用默认定价`)
    return {
      inputPrice: 0.001,  // 默认输入定价
      outputPrice: 0.002  // 默认输出定价
    }
  }

  /**
   * 批量计算费用
   * @param {Array} usageRecords 使用记录数组
   * @returns {Object} 批量计算结果
   */
  batchCalculateCost(usageRecords) {
    const results = []
    let totalCost = 0
    let totalInputTokens = 0
    let totalOutputTokens = 0

    for (const record of usageRecords) {
      const result = this.calculateCost(record)
      results.push(result)
      
      if (result.success) {
        totalCost += result.data.totalCost
        totalInputTokens += result.data.inputTokens
        totalOutputTokens += result.data.outputTokens
      }
    }

    return {
      success: true,
      data: {
        records: results,
        summary: {
          totalRecords: usageRecords.length,
          totalInputTokens,
          totalOutputTokens,
          totalTokens: totalInputTokens + totalOutputTokens,
          totalCost: Math.round(totalCost * 100000) / 100000
        }
      }
    }
  }

  /**
   * 根据内容长度估算tokens和费用
   * @param {Object} params 估算参数
   * @param {string} params.content 内容文本
   * @param {string} params.model 模型名称
   * @param {number} params.inputOutputRatio 输入输出比例（默认1:1）
   * @returns {Object} 估算结果
   */
  estimateCostFromContent({
    content,
    model,
    inputOutputRatio = 1
  }) {
    // 简单的tokens估算：中文字符约1.5个token，英文单词约1个token
    const chineseChars = (content.match(/[\u4e00-\u9fa5]/g) || []).length
    const englishWords = (content.match(/[a-zA-Z]+/g) || []).length
    const estimatedTokens = Math.ceil(chineseChars * 1.5 + englishWords)

    // 按比例分配输入输出tokens
    const totalRatio = 1 + inputOutputRatio
    const inputTokens = Math.ceil(estimatedTokens / totalRatio)
    const outputTokens = Math.ceil(estimatedTokens * inputOutputRatio / totalRatio)

    return this.calculateCost({
      model,
      inputTokens,
      outputTokens
    })
  }

  /**
   * 更新模型定价
   * @param {string} model 模型名称
   * @param {number} inputPrice 输入定价
   * @param {number} outputPrice 输出定价
   */
  updateModelPricing(model, inputPrice, outputPrice) {
    this.defaultPricing[model] = {
      inputPrice: parseFloat(inputPrice),
      outputPrice: parseFloat(outputPrice)
    }
    console.log(`已更新模型 ${model} 的定价:`, this.defaultPricing[model])
  }

  /**
   * 获取所有模型定价
   * @returns {Object} 所有模型的定价信息
   */
  getAllModelPricing() {
    return { ...this.defaultPricing }
  }
}

// 创建单例实例
const costCalculationService = new CostCalculationService()

module.exports = costCalculationService
