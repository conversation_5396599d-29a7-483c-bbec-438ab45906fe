import React, { useState } from 'react'
import { Card, Button, Typography, Spin, Alert, Table } from 'antd'
import { BugOutlined, ReloadOutlined } from '@ant-design/icons'
import cloudbaseService from '../utils/cloudbaseConfig'

const { Title, Text } = Typography

interface ModelData {
  _id: string
  name?: string
  provider?: string
  model?: string
  status?: string
  [key: string]: any
}

const ModelDebugger: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [models, setModels] = useState<ModelData[]>([])
  const [error, setError] = useState<string | null>(null)

  const debugModels = async () => {
    setLoading(true)
    setError(null)
    
    try {
      console.log('🔍 开始调试AI模型数据...')
      
      const app = cloudbaseService.app
      if (!app) {
        throw new Error('CloudBase应用未初始化')
      }

      const db = app.database()
      
      // 查询ai_configs集合的所有数据
      const result = await db.collection('ai_configs').limit(100).get()
      
      console.log('📊 数据库查询结果:', result)
      console.log('📊 模型数量:', result.data.length)
      console.log('📊 原始数据:', result.data)
      
      setModels(result.data)
      
    } catch (err: any) {
      console.error('❌ 调试失败:', err)
      setError(err.message || '调试过程中发生错误')
    } finally {
      setLoading(false)
    }
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: '_id',
      key: '_id',
      width: 200,
      render: (text: string) => <Text code>{text}</Text>
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => text || <Text type="danger">无名称</Text>
    },
    {
      title: '提供商',
      dataIndex: 'provider',
      key: 'provider',
      render: (text: string) => text || <Text type="secondary">未知</Text>
    },
    {
      title: '模型',
      dataIndex: 'model',
      key: 'model',
      render: (text: string) => text || <Text type="secondary">未知</Text>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => text || <Text type="secondary">未知</Text>
    },
    {
      title: '原始数据',
      key: 'raw',
      render: (_, record: ModelData) => (
        <details>
          <summary>查看原始数据</summary>
          <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
            {JSON.stringify(record, null, 2)}
          </pre>
        </details>
      )
    }
  ]

  return (
    <Card 
      title={
        <div className="flex items-center space-x-2">
          <BugOutlined />
          <span>AI模型数据调试</span>
        </div>
      }
      extra={
        <Button 
          type="primary" 
          icon={<ReloadOutlined />}
          loading={loading}
          onClick={debugModels}
        >
          查询数据库
        </Button>
      }
    >
      <div className="space-y-4">
        <Alert
          message="调试说明"
          description="此工具直接查询ai_configs集合，显示所有模型的原始数据，帮助排查为什么只显示一个模型。"
          type="info"
          showIcon
        />

        {loading && (
          <div className="text-center py-8">
            <Spin size="large" />
            <div className="mt-2">正在查询数据库...</div>
          </div>
        )}

        {error && (
          <Alert
            message="查询失败"
            description={error}
            type="error"
            showIcon
          />
        )}

        {models.length > 0 && (
          <div className="space-y-4">
            <Title level={4}>数据库中的AI模型 ({models.length}个)</Title>
            
            <Table 
              columns={columns}
              dataSource={models}
              rowKey="_id"
              pagination={false}
              size="small"
              scroll={{ x: 800 }}
            />

            <Alert
              message="分析结果"
              description={
                <div>
                  <p><strong>总模型数：</strong>{models.length}</p>
                  <p><strong>有名称的模型：</strong>{models.filter(m => m.name).length}</p>
                  <p><strong>无名称的模型：</strong>{models.filter(m => !m.name).length}</p>
                  <p><strong>激活的模型：</strong>{models.filter(m => m.status === 'active').length}</p>
                </div>
              }
              type="success"
              showIcon
            />
          </div>
        )}

        {!loading && !error && models.length === 0 && (
          <Alert
            message="暂无数据"
            description="点击'查询数据库'按钮开始调试"
            type="info"
            showIcon
          />
        )}
      </div>
    </Card>
  )
}

export default ModelDebugger
