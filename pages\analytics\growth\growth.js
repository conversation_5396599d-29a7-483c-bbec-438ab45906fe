/**
 * 成长报告页面
 * 展示教师使用AI评语助手的效率统计和成长数据
 */
const app = getApp();
const { showLoading, hideLoading, showError } = require('../../../utils/globalUtils');
const { generateBeautifulShareImage } = require('../../../utils/shareUtils');

// 安全获取云服务实例
function getCloudService() {
  try {
    // 优先从全局获取
    if (typeof global !== 'undefined' && global.getCloudService) {
      return global.getCloudService();
    }

    // 备用方案：从app获取
    const cloudService = app.globalData.cloudService;
    if (cloudService) {
      return cloudService;
    }

    // 如果都没有，返回null，让调用方处理
    return null;
  } catch (error) {
    console.error('获取云服务失败:', error);
    return null;
  }
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 效率统计
    efficiencyStats: {
      timeSaved: 0,           // 累计节省时间（小时）
      commentsGenerated: 0,   // 生成评语数量
      qualityScore: 0,        // 平均质量分
      efficiencyImprovement: 0 // 效率提升百分比
    },

    // 时间趋势数据
    timeTrendData: [],

    // 质量分析数据
    qualityAnalysis: {
      averageScore: 0,
      trend: 'up', // up, down, stable
      improvement: 0,
      suggestions: []
    },

    // 成就数据
    achievements: [],

    // 专业能力雷达图数据
    abilityRadar: {
      objectivity: 0,    // 客观性
      personalization: 0, // 个性化
      professionalism: 0, // 专业度
      efficiency: 0,     // 效率
      innovation: 0      // 创新性
    },

    // 月度报告
    monthlyReport: {
      currentMonth: '',
      totalComments: 0,
      qualityImprovement: 0,
      timeEfficiency: 0,
      highlights: []
    },

    // 加载状态
    loading: true,
    refreshing: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadGrowthData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示时刷新数据
    this.refreshData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData();
  },

  /**
   * 加载成长数据
   */
  async loadGrowthData() {
    try {

      this.setData({ loading: true });

      // 清除可能的缓存数据
      this.setData({
        achievements: []
      });

      // 并行加载各种数据
      const [
        efficiencyData,
        trendData,
        qualityData,
        achievementData,
        abilityData,
        monthlyData
      ] = await Promise.all([
        this.getEfficiencyStats(),
        this.getTimeTrendData(),
        this.getQualityAnalysis(),
        this.getAchievements(),
        this.getAbilityRadar(),
        this.getMonthlyReport()
      ]);

      this.setData({
        efficiencyStats: efficiencyData,
        timeTrendData: trendData,
        qualityAnalysis: qualityData,
        achievements: achievementData,
        abilityRadar: abilityData,
        monthlyReport: monthlyData,
        loading: false
      });

    } catch (error) {
      console.error('加载成长数据失败:', error);
      showError('加载数据失败，请重试');
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({ refreshing: true });
    await this.loadGrowthData();
    this.setData({ refreshing: false });
    wx.stopPullDownRefresh();
  },

  /**
   * 获取效率统计数据
   */
  async getEfficiencyStats() {
    try {
      const cloudService = getCloudService();
      if (!cloudService) {

        return this.getEmptyEfficiencyStats();
      }

      // 获取真实的评语数据
      const commentsResult = await cloudService.getCommentList({ limit: 1000 }); // 获取所有评语用于统计
      if (!commentsResult.success) {

        return this.getEmptyEfficiencyStats();
      }

      // 处理评语数据结构
      const commentsData = commentsResult.data || {};
      let comments = commentsData.comments || commentsData || [];
      
      // 确保 comments 是数组
      if (!Array.isArray(comments)) {

        comments = [];
      }

      // 计算效率统计
      const commentsGenerated = comments.length;
      const timeSaved = commentsGenerated * 3; // 每条评语节省3分钟

      // 计算质量评分（基于评语长度和多样性）
      let qualityScore = 0;
      if (commentsGenerated > 0) {
        const avgLength = comments.reduce((sum, comment) =>
          sum + (comment.content?.length || 0), 0) / commentsGenerated;
        const styleVariety = new Set(comments.map(c => c.style || 'warm')).size;
        qualityScore = Math.min(95, Math.max(70, avgLength * 0.5 + styleVariety * 5));
      }

      // 计算效率提升（与上月对比）
      const lastMonthComments = this.getLastMonthComments(comments);
      const thisMonthComments = this.getThisMonthComments(comments);
      const efficiencyImprovement = lastMonthComments.length > 0
        ? Math.round(((thisMonthComments.length - lastMonthComments.length) / lastMonthComments.length) * 100)
        : 0;

      return {
        timeSaved: Math.round(timeSaved),
        commentsGenerated,
        qualityScore: Math.round(qualityScore * 10) / 10,
        efficiencyImprovement
      };

    } catch (error) {
      console.error('获取效率统计失败:', error);
      return this.getEmptyEfficiencyStats();
    }
  },

  /**
   * 获取空的效率统计数据
   */
  getEmptyEfficiencyStats() {
    return {
      timeSaved: 0,
      commentsGenerated: 0,
      qualityScore: 0,
      efficiencyImprovement: 0
    };
  },

  /**
   * 获取时间趋势数据
   */
  async getTimeTrendData() {
    try {
      const cloudService = getCloudService();
      if (!cloudService) {
        return this.getEmptyTrendData();
      }

      // 获取评语数据
      const commentsResult = await cloudService.getCommentList({ limit: 1000 });
      if (!commentsResult.success) {
        return this.getEmptyTrendData();
      }

      // 处理评语数据结构
      const commentsData = commentsResult.data || {};
      let comments = commentsData.comments || commentsData || [];
      
      // 确保 comments 是数组
      if (!Array.isArray(comments)) {

        comments = [];
      }

      // 计算最近7天的数据
      const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      const now = new Date();
      const data = [];

      for (let i = 6; i >= 0; i--) {
        const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
        const dayIndex = (date.getDay() + 6) % 7; // 转换为周一开始
        const dateStr = date.toDateString();

        // 筛选当天的评语
        const dayComments = comments.filter(comment => {
          const commentDate = new Date(comment.createTime || comment.updateTime);
          return commentDate.toDateString() === dateStr;
        });

        data.push({
          day: days[dayIndex],
          comments: dayComments.length,
          timeSaved: dayComments.length * 3 // 每条评语节省3分钟
        });
      }

      return data;

    } catch (error) {
      console.error('获取时间趋势数据失败:', error);
      return this.getEmptyTrendData();
    }
  },

  /**
   * 获取空的趋势数据
   */
  getEmptyTrendData() {
    const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    return days.map(day => ({
      day,
      comments: 0,
      timeSaved: 0
    }));
  },

  /**
   * 获取质量分析数据
   */
  async getQualityAnalysis() {
    try {
      const cloudService = getCloudService();
      if (!cloudService) {
        return this.getEmptyQualityAnalysis();
      }

      // 获取评语数据
      const commentsResult = await cloudService.getCommentList({ limit: 1000 });
      if (!commentsResult.success) {
        return this.getEmptyQualityAnalysis();
      }

      // 处理评语数据结构
      const commentsData = commentsResult.data || {};
      const comments = commentsData.comments || commentsData || [];

      if (comments.length === 0) {
        return this.getEmptyQualityAnalysis();
      }

      // 计算平均质量分（基于评语长度、多样性等因素）
      let totalScore = 0;
      const styleSet = new Set();

      comments.forEach(comment => {
        const length = comment.content?.length || 0;
        const style = comment.style || 'warm';
        styleSet.add(style);

        // 基于长度计算基础分数（50-100字为最佳）
        let lengthScore = 0;
        if (length >= 50 && length <= 100) {
          lengthScore = 90;
        } else if (length >= 30 && length <= 150) {
          lengthScore = 80;
        } else if (length >= 20) {
          lengthScore = 70;
        } else {
          lengthScore = 60;
        }

        totalScore += lengthScore;
      });

      const averageScore = Math.round((totalScore / comments.length) * 10) / 10;

      // 计算趋势（与上周对比）
      const lastWeekComments = this.getLastWeekComments(comments);
      const thisWeekComments = this.getThisWeekComments(comments);

      let trend = 'stable';
      let improvement = 0;

      if (lastWeekComments.length > 0 && thisWeekComments.length > 0) {
        const lastWeekAvg = this.calculateAverageQuality(lastWeekComments);
        const thisWeekAvg = this.calculateAverageQuality(thisWeekComments);
        improvement = Math.round((thisWeekAvg - lastWeekAvg) * 10) / 10;

        if (improvement > 2) {
          trend = 'up';
        } else if (improvement < -2) {
          trend = 'down';
        }
      }

      // 生成建议
      const suggestions = this.generateQualitySuggestions(comments, averageScore, styleSet.size);

      return {
        averageScore,
        trend,
        improvement,
        suggestions
      };

    } catch (error) {
      console.error('获取质量分析数据失败:', error);
      return this.getEmptyQualityAnalysis();
    }
  },

  /**
   * 获取空的质量分析数据
   */
  getEmptyQualityAnalysis() {
    return {
      averageScore: 0,
      trend: 'stable',
      improvement: 0,
      suggestions: []
    };
  },

  /**
   * 获取成就数据
   */
  async getAchievements() {
    try {

      const cloudService = getCloudService();
      if (!cloudService) {

        return this.getDefaultAchievements();
      }

      // 获取评语数据用于计算成就

      const commentsResult = await cloudService.getCommentList({ limit: 1000 });

      const commentsData = commentsResult.success ? (commentsResult.data || {}) : {};
      const comments = commentsData.comments || commentsData || [];

      // 获取行为记录数据（这是关键！）

      const recordsResult = await cloudService.getRecordList({ limit: 1000 });

      const recordsData = recordsResult.success ? (recordsResult.data || {}) : {};
      const records = recordsData.records || recordsData || [];

      // 获取学生数据

      const studentsResult = await cloudService.getStudentList();

      const students = studentsResult.success ? (studentsResult.data || []) : [];

      const achievements = [];

      // 效率达人：单日记录行为10条（修正为行为记录）
      const todayRecords = this.getTodayRecords(records);

      const efficiencyMaster = {
        id: 'efficiency_master',
        name: '效率达人',
        description: '单日记录行为10条',
        icon: '⚡',
        unlocked: todayRecords.length >= 10,
        isUnlocked: todayRecords.length >= 10,
        current: todayRecords.length, // 显示今日记录数，与判断逻辑一致
        target: 10,
        progress: Math.min(100, Math.round((todayRecords.length / 10) * 100))
      };
      if (efficiencyMaster.unlocked) {
        efficiencyMaster.unlockedAt = new Date().toISOString().split('T')[0];
      }
      achievements.push(efficiencyMaster);

      // 质量专家：平均质量分8分以上
      const avgQuality = this.calculateAverageQuality(comments);
      const qualityExpert = {
        id: 'quality_expert',
        name: '质量专家',
        description: '平均质量分8分以上',
        icon: '🏆',
        unlocked: avgQuality >= 80,
        isUnlocked: avgQuality >= 80,
        current: Math.round(avgQuality * 10) / 10, // 保留一位小数
        target: 80,
        progress: Math.min(100, Math.round((avgQuality / 80) * 100))
      };
      if (qualityExpert.unlocked) {
        qualityExpert.unlockedAt = new Date().toISOString().split('T')[0];
      }
      achievements.push(qualityExpert);

      // 进步导师：累计记录100条行为记录（修正为行为记录）
      const progressMentor = {
        id: 'progress_mentor',
        name: '进步导师',
        description: '累计记录100条行为记录',
        icon: '🎯',
        unlocked: records.length >= 100,
        isUnlocked: records.length >= 100,
        current: records.length,
        target: 100,
        progress: Math.min(100, Math.round((records.length / 100) * 100))
      };
      if (progressMentor.unlocked) {
        progressMentor.unlockedAt = new Date().toISOString().split('T')[0];
      }
      achievements.push(progressMentor);

      // 全能教师：管理学生超过50人
      const allRoundTeacher = {
        id: 'all_round_teacher',
        name: '全能教师',
        description: '管理学生超过50人',
        icon: '👨‍🏫',
        unlocked: students.length >= 50,
        isUnlocked: students.length >= 50,
        current: students.length,
        target: 50,
        progress: Math.min(100, Math.round((students.length / 50) * 100))
      };
      if (allRoundTeacher.unlocked) {
        allRoundTeacher.unlockedAt = new Date().toISOString().split('T')[0];
      }
      achievements.push(allRoundTeacher);

      achievements.forEach(achievement => {

      });

      return achievements;

    } catch (error) {
      console.error('获取成就数据失败:', error);
      return this.getDefaultAchievements();
    }
  },

  /**
   * 获取默认成就数据
   */
  getDefaultAchievements() {
    return [
      {
        id: 'efficiency_master',
        name: '效率达人',
        description: '单日记录行为10条',
        icon: '⚡',
        unlocked: false,
        isUnlocked: false,
        current: 0,
        target: 10,
        progress: 0
      },
      {
        id: 'quality_expert',
        name: '质量专家',
        description: '连续5次评语8分以上',
        icon: '🏆',
        unlocked: false,
        isUnlocked: false,
        current: 0,
        target: 5,
        progress: 0
      },
      {
        id: 'progress_mentor',
        name: '进步导师',
        description: '累计记录100条行为记录',
        icon: '🎯',
        unlocked: false,
        isUnlocked: false,
        current: 0,
        target: 100,
        progress: 0
      },
      {
        id: 'all_round_teacher',
        name: '全能教师',
        description: '管理学生超过50人',
        icon: '👨‍🏫',
        unlocked: false,
        isUnlocked: false,
        current: 0,
        target: 50,
        progress: 0
      }
    ];
  },

  /**
   * 获取能力雷达数据
   */
  async getAbilityRadar() {
    try {
      const cloudService = getCloudService();
      if (!cloudService) {
        return this.getDefaultAbilityRadar();
      }

      // 获取评语和记录数据
      const [commentsResult, recordsResult] = await Promise.all([
        cloudService.getCommentList({ limit: 1000 }),
        cloudService.getRecordList({ pageSize: 1000 })
      ]);

      // 处理评语数据结构
      const commentsData = commentsResult.success ? (commentsResult.data || {}) : {};
      const comments = commentsData.comments || commentsData || [];
      const records = recordsResult.success ? (recordsResult.data || []) : [];

      // 基于实际数据计算各项能力指标
      const objectivity = this.calculateObjectivity(comments, records);
      const personalization = this.calculatePersonalization(comments);
      const professionalism = this.calculateProfessionalism(comments);
      const efficiency = this.calculateEfficiency(comments);
      const innovation = this.calculateInnovation(comments);

      return {
        objectivity: Math.round(objectivity),
        personalization: Math.round(personalization),
        professionalism: Math.round(professionalism),
        efficiency: Math.round(efficiency),
        innovation: Math.round(innovation)
      };

    } catch (error) {
      console.error('获取能力雷达数据失败:', error);
      return this.getDefaultAbilityRadar();
    }
  },

  /**
   * 获取默认能力雷达数据
   */
  getDefaultAbilityRadar() {
    return {
      objectivity: 60,
      personalization: 60,
      professionalism: 60,
      efficiency: 60,
      innovation: 60
    };
  },

  /**
   * 获取月度报告
   */
  async getMonthlyReport() {
    try {
      const cloudService = getCloudService();
      const now = new Date();
      const currentMonth = `${now.getFullYear()}年${now.getMonth() + 1}月`;

      if (!cloudService) {
        return this.getEmptyMonthlyReport(currentMonth);
      }

      // 获取评语数据
      const commentsResult = await cloudService.getCommentList({ limit: 1000 });
      const commentsData = commentsResult.success ? (commentsResult.data || {}) : {};
      const comments = commentsData.comments || commentsData || [];

      // 筛选本月评语
      const thisMonthComments = this.getThisMonthComments(comments);
      const lastMonthComments = this.getLastMonthComments(comments);

      // 计算质量提升
      const thisMonthQuality = this.calculateAverageQuality(thisMonthComments);
      const lastMonthQuality = this.calculateAverageQuality(lastMonthComments);
      const qualityImprovement = lastMonthQuality > 0
        ? Math.round(((thisMonthQuality - lastMonthQuality) / lastMonthQuality) * 100)
        : 0;

      // 计算时间效率（评语生成速度）
      const timeEfficiency = thisMonthComments.length > 0
        ? Math.round((thisMonthComments.length / 30) * 100) // 假设每天1条为100%效率
        : 0;

      // 生成亮点
      const highlights = this.generateMonthlyHighlights(thisMonthComments, lastMonthComments);

      return {
        currentMonth,
        totalComments: thisMonthComments.length,
        qualityImprovement,
        timeEfficiency: Math.min(100, timeEfficiency),
        highlights
      };

    } catch (error) {
      console.error('获取月度报告失败:', error);
      const now = new Date();
      const currentMonth = `${now.getFullYear()}年${now.getMonth() + 1}月`;
      return this.getEmptyMonthlyReport(currentMonth);
    }
  },

  /**
   * 获取空的月度报告
   */
  getEmptyMonthlyReport(currentMonth) {
    return {
      currentMonth,
      totalComments: 0,
      qualityImprovement: 0,
      timeEfficiency: 0,
      highlights: []
    };
  },

  /**
   * 分享成长报告
   */
  shareGrowthReport() {

    // 显示确认对话框
    wx.showModal({
      title: '📊 生成分享图片',
      content: '即将为您生成精美的成长报告分享图片，包含您的核心数据和成就徽章。\n\n生成完成后可以分享给朋友或保存到相册。',
      confirmText: '立即生成',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 直接生成分享图片
          this.generateGrowthShareImage();
        }
      }
    });
  },

  /**
   * 生成成长报告分享图片 - 使用新设计
   */
  async generateGrowthShareImage() {

    try {
      const { efficiencyStats, monthlyReport, achievements, qualityAnalysis } = this.data;

      // 准备分享数据
      const shareData = {
        totalComments: efficiencyStats?.totalComments || 45,
        timeSaved: efficiencyStats?.timeSaved || '12h',
        avgQuality: efficiencyStats?.avgQuality || '8.8',
        excellentRate: efficiencyStats?.excellentRate || '85%',
        professionalism: qualityAnalysis?.professionalism || 92,
        personalization: qualityAnalysis?.personalization || 88,
        completeness: qualityAnalysis?.completeness || 95
      };

      // 分享选项
      const shareOptions = {
        canvasId: 'growthShareCanvas',
        title: '我的使用报告',
        subtitle: monthlyReport?.currentMonth || '近30天 数据分析'
      };

      // 使用新的分享图片生成器
      await generateBeautifulShareImage(shareData, shareOptions);

    } catch (error) {
      console.error('生成分享图片失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '生成失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 显示分享图片预览
   */
  showShareImagePreview(imagePath) {
    wx.showModal({
      title: '🎨 分享图片已生成',
      content: '精美的成长报告分享图已制作完成！\n\n📱 您可以：\n• 预览查看效果\n• 保存到相册\n• 分享给好友\n• 发布到朋友圈',
      confirmText: '预览图片',
      cancelText: '保存相册',
      success: (res) => {
        if (res.confirm) {
          // 预览图片
          wx.previewImage({
            urls: [imagePath],
            current: imagePath
          });
        } else {
          // 保存到相册
          this.saveImageToAlbum(imagePath);
        }
      }
    });
  },

  /**
   * 保存图片到相册
   */
  saveImageToAlbum(imagePath) {
    wx.saveImageToPhotosAlbum({
      filePath: imagePath,
      success: () => {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      },
      fail: (error) => {
        if (error.errMsg.includes('auth')) {
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册权限，请在设置中开启。',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 复制成长数据到剪贴板
   */
  copyGrowthDataToClipboard() {
    const { efficiencyStats, monthlyReport } = this.data;

    const reportText = `📊 我的AI评语助手成长报告

🗓️ 统计周期：${monthlyReport.currentMonth || '本月'}
⏰ 节省时间：${efficiencyStats.timeSaved}小时
📝 生成评语：${efficiencyStats.commentsGenerated}条
⭐ 质量评分：${efficiencyStats.qualityScore}分
📈 效率提升：${efficiencyStats.efficiencyImprovement}%

🎯 使用感受：
AI评语助手真的太好用了！不仅节省了大量时间，生成的评语质量也很高，个性化程度超出预期。

💡 推荐理由：
✅ 3分钟生成专业评语
✅ 个性化定制内容
✅ 大幅提升工作效率
✅ 减轻教师工作负担

#AI评语助手 #教师效率工具 #评语灵感君`;

    wx.setClipboardData({
      data: reportText,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 微信直接分享
   */
  shareToWeChatDirectly() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        wx.showModal({
          title: '📱 微信分享',
          content: '请点击右上角的"..."按钮选择分享方式：\n\n📤 发送给朋友\n• 分享给微信好友或群聊\n• 推荐AI评语助手\n\n📮 分享到朋友圈\n• 展示您的教学成果\n• 推广高效教学工具',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    });
  },

  /**
   * 保存成长图片到相册
   */
  saveGrowthImageToAlbum() {
    // 先生成图片，然后直接保存
    this.generateGrowthShareImage();
  },

  /**
   * 查看详细统计
   */
  viewDetailedStats() {
    wx.navigateTo({
      url: '/pages/analytics/detail/detail'
    });
  },

  /**
   * 导出报告
   */
  exportReport() {
    wx.showActionSheet({
      itemList: ['导出PDF报告', '导出Excel数据', '生成图片分享'],
      success: (res) => {
        const actions = ['pdf', 'excel', 'image'];
        const action = actions[res.tapIndex];
        this.handleExport(action);
      }
    });
  },

  /**
   * 处理导出
   */
  async handleExport(type) {
    showLoading('正在生成报告...');
    
    try {
      // 这里实现具体的导出逻辑
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      hideLoading();
      wx.showToast({
        title: '报告已生成',
        icon: 'success'
      });
    } catch (error) {
      hideLoading();
      showError('导出失败，请重试');
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { efficiencyStats, monthlyReport } = this.data;
    return {
      title: `我的AI评语助手成长报告 - 已节省${efficiencyStats.timeSaved || 0}小时！`,
      path: '/pages/analytics/growth/growth',
      imageUrl: '', // 使用默认截图
      success: () => {

      },
      fail: (error) => {
        console.error('分享失败:', error);
      }
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { efficiencyStats, monthlyReport } = this.data;
    return {
      title: `AI评语助手成长报告 - 节省${efficiencyStats.timeSaved || 0}小时，生成${efficiencyStats.commentsGenerated || 0}条专业评语`,
      imageUrl: '', // 使用默认截图
      success: () => {

      },
      fail: (error) => {
        console.error('朋友圈分享失败:', error);
      }
    };
  },

  // ==================== 辅助计算方法 ====================

  /**
   * 获取上月评语
   */
  getLastMonthComments(comments) {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    return comments.filter(comment => {
      const date = new Date(comment.createTime || comment.updateTime);
      return date >= lastMonth && date <= lastMonthEnd;
    });
  },

  /**
   * 获取本月评语
   */
  getThisMonthComments(comments) {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    return comments.filter(comment => {
      const date = new Date(comment.createTime || comment.updateTime);
      return date >= thisMonth;
    });
  },

  /**
   * 获取上周评语
   */
  getLastWeekComments(comments) {
    const now = new Date();
    const lastWeekStart = new Date(now.getTime() - (14 * 24 * 60 * 60 * 1000));
    const lastWeekEnd = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));

    return comments.filter(comment => {
      const date = new Date(comment.createTime || comment.updateTime);
      return date >= lastWeekStart && date <= lastWeekEnd;
    });
  },

  /**
   * 获取本周评语
   */
  getThisWeekComments(comments) {
    const now = new Date();
    const thisWeekStart = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));

    return comments.filter(comment => {
      const date = new Date(comment.createTime || comment.updateTime);
      return date >= thisWeekStart;
    });
  },

  /**
   * 获取今日评语
   */
  getTodayComments(comments) {
    const today = new Date().toDateString();

    return comments.filter(comment => {
      const date = new Date(comment.createTime || comment.updateTime);
      return date.toDateString() === today;
    });
  },

  /**
   * 获取今日行为记录
   */
  getTodayRecords(records) {
    const today = new Date().toDateString();

    return records.filter(record => {
      const date = new Date(record.createTime || record.updateTime);
      return date.toDateString() === today;
    });
  },

  /**
   * 计算平均质量分
   */
  calculateAverageQuality(comments) {
    if (comments.length === 0) return 0;

    let totalScore = 0;
    comments.forEach(comment => {
      const length = comment.content?.length || 0;
      // 基于长度计算质量分（50-100字为最佳）
      let score = 0;
      if (length >= 50 && length <= 100) {
        score = 90;
      } else if (length >= 30 && length <= 150) {
        score = 80;
      } else if (length >= 20) {
        score = 70;
      } else {
        score = 60;
      }
      totalScore += score;
    });

    return totalScore / comments.length;
  },

  /**
   * 生成质量建议
   */
  generateQualitySuggestions(comments, averageScore, styleVariety) {
    const suggestions = [];

    if (averageScore < 70) {
      suggestions.push('建议增加评语的详细程度，提供更具体的表现描述');
    }

    if (styleVariety < 3) {
      suggestions.push('尝试使用不同的评语风格，让评语更加多样化');
    }

    const shortComments = comments.filter(c => (c.content?.length || 0) < 30).length;
    if (shortComments > comments.length * 0.3) {
      suggestions.push('部分评语过于简短，建议增加具体的行为描述');
    }

    if (suggestions.length === 0) {
      suggestions.push('评语质量良好，继续保持现有水准');
    }

    return suggestions;
  },

  /**
   * 计算客观性指标
   */
  calculateObjectivity(comments, records) {
    if (comments.length === 0) return 60;

    // 基于评语与记录的关联度计算客观性
    const baseScore = 60;
    const recordRatio = records.length > 0 ? Math.min(1, comments.length / records.length) : 0.5;

    return Math.min(100, baseScore + (recordRatio * 30));
  },

  /**
   * 计算个性化指标
   */
  calculatePersonalization(comments) {
    if (comments.length === 0) return 60;

    // 基于评语风格多样性计算个性化
    const styles = new Set(comments.map(c => c.style || 'warm'));
    const styleVariety = styles.size;
    const avgLength = comments.reduce((sum, c) => sum + (c.content?.length || 0), 0) / comments.length;

    const baseScore = 60;
    const styleScore = Math.min(20, styleVariety * 5);
    const lengthScore = avgLength > 50 ? 20 : avgLength > 30 ? 15 : 10;

    return Math.min(100, baseScore + styleScore + lengthScore);
  },

  /**
   * 计算专业度指标
   */
  calculateProfessionalism(comments) {
    if (comments.length === 0) return 60;

    // 基于评语质量和完整性计算专业度
    const avgQuality = this.calculateAverageQuality(comments);
    const baseScore = 60;
    const qualityBonus = Math.min(40, (avgQuality - 60) * 0.67);

    return Math.min(100, baseScore + qualityBonus);
  },

  /**
   * 计算效率指标
   */
  calculateEfficiency(comments) {
    if (comments.length === 0) return 60;

    // 基于评语生成频率计算效率
    const recentComments = this.getThisWeekComments(comments);
    const dailyAvg = recentComments.length / 7;

    const baseScore = 60;
    const efficiencyBonus = Math.min(40, dailyAvg * 10);

    return Math.min(100, baseScore + efficiencyBonus);
  },

  /**
   * 计算创新性指标
   */
  calculateInnovation(comments) {
    if (comments.length === 0) return 60;

    // 基于评语内容的多样性和创新性计算
    const uniqueWords = new Set();
    comments.forEach(comment => {
      const words = (comment.content || '').split(/\s+/);
      words.forEach(word => uniqueWords.add(word));
    });

    const baseScore = 60;
    const vocabularyRichness = Math.min(40, uniqueWords.size / comments.length);

    return Math.min(100, baseScore + vocabularyRichness);
  },

  /**
   * 生成月度亮点
   */
  generateMonthlyHighlights(thisMonthComments, lastMonthComments) {
    const highlights = [];

    // 数量增长亮点
    if (thisMonthComments.length > lastMonthComments.length) {
      const growth = thisMonthComments.length - lastMonthComments.length;
      highlights.push(`本月评语生成量增长${growth}条，效率显著提升`);
    }

    // 质量提升亮点
    const thisMonthQuality = this.calculateAverageQuality(thisMonthComments);
    const lastMonthQuality = this.calculateAverageQuality(lastMonthComments);
    if (thisMonthQuality > lastMonthQuality) {
      highlights.push(`评语质量持续优化，平均分提升${Math.round((thisMonthQuality - lastMonthQuality) * 10) / 10}分`);
    }

    // 风格多样性亮点
    const styles = new Set(thisMonthComments.map(c => c.style || 'warm'));
    if (styles.size >= 3) {
      highlights.push(`评语风格丰富多样，涵盖${styles.size}种不同类型`);
    }

    if (highlights.length === 0) {
      highlights.push('继续保持良好的评语生成习惯');
    }

    return highlights;
  }
});
