/**
 * 全局类型定义
 * 基于文档中的数据映射关系和系统架构
 */

import { z } from 'zod'

// ==========================================================================
// 基础数据类型
// ==========================================================================

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
  timestamp: number
}

export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// ==========================================================================
// 学生记录数据类型（与小程序对应）
// ==========================================================================

export const StudentRecordSchema = z.object({
  studentId: z.string(),
  studentName: z.string(),
  className: z.string(),
  grade: z.string(),
  semester: z.string(),
  schoolYear: z.string(),
  teacherName: z.string(),
  performanceMaterial: z.string(),
  behaviorTags: z.array(z.string()),
  achievements: z.array(z.string()),
  subjects: z.record(z.any()),
  
  // 数据完整性字段
  createdAt: z.number(),
  updatedAt: z.number(),
  version: z.string(),
  checksum: z.string()
})

export type StudentRecord = z.infer<typeof StudentRecordSchema>

// 模板变量映射
export interface TemplateVariables {
  studentName: string
  className: string
  grade: string
  semester: string
  schoolYear: string
  teacherName: string
  performanceMaterial: string
  behaviorSummary: string
  achievementList: string
  subjectPerformance: string
}

// ==========================================================================
// 仪表板数据类型
// ==========================================================================

export interface DashboardOverview {
  totalStudents: number
  totalComments: number
  totalClasses: number
  recentComments: number
}

export interface DashboardGrowth {
  students: number
  comments: number
  classes: number
  recent: number
}

export interface ChartDataPoint {
  date: string
  value: number
  category?: string
}

export interface SystemActivity {
  id: string
  type: 'comment_generated' | 'student_added' | 'template_updated' | 'user_login'
  description: string
  timestamp: number
  userId?: string
  metadata?: Record<string, any>
}

export const DashboardDataSchema = z.object({
  overview: z.object({
    totalStudents: z.number(),
    totalComments: z.number(),
    totalClasses: z.number(),
    recentComments: z.number()
  }),
  growth: z.object({
    students: z.number(),
    comments: z.number(),
    classes: z.number(),
    recent: z.number()
  }),
  chartData: z.array(z.object({
    date: z.string(),
    value: z.number(),
    category: z.string().optional()
  })),
  activities: z.array(z.object({
    id: z.string(),
    type: z.enum(['comment_generated', 'student_added', 'template_updated', 'user_login']),
    description: z.string(),
    timestamp: z.number(),
    userId: z.string().optional(),
    metadata: z.record(z.any()).optional()
  }))
})

export type DashboardData = z.infer<typeof DashboardDataSchema>

// ==========================================================================
// AI配置类型
// ==========================================================================

export type AIProvider = 'doubao' | 'openai' | 'wenxin' | 'tongyi'

export interface AIConfig {
  id: string
  provider: AIProvider
  name: string
  apiKey: string
  baseUrl?: string
  model: string
  parameters: {
    temperature: number
    maxTokens: number
    topP: number
    frequencyPenalty?: number
    presencePenalty?: number
  }
  quota: {
    dailyLimit: number
    monthlyLimit: number
    used: {
      daily: number
      monthly: number
    }
  }
  isActive: boolean
  createdAt: number
  updatedAt: number
}

export interface AIUsageStats {
  totalCalls: number
  successRate: number
  averageResponseTime: number
  totalCost: number
  hourlyDistribution: Record<string, number>
  providerDistribution: Record<AIProvider, number>
}

// ==========================================================================
// 模板编辑器类型
// ==========================================================================

export type TemplateStyle = 'formal' | 'caring' | 'encouraging' | 'analytical'

export interface Template {
  id: string
  name: string
  style: TemplateStyle
  content: string
  variables: string[]
  isActive: boolean
  usageCount: number
  createdAt: number
  updatedAt: number
  createdBy: string
}

export interface TemplateVariable {
  name: string
  description: string
  example: string
  required: boolean
}

// ==========================================================================
// 使用监控类型
// ==========================================================================

export interface UsageRecord {
  id: string
  timestamp: number
  provider: AIProvider
  templateId: string
  studentId: string
  responseTime: number
  success: boolean
  errorMessage?: string
  cost: number
  tokensUsed: number
}

export interface UsageMetrics {
  realtime: {
    totalCalls: number
    successRate: number
    averageResponseTime: number
    totalCost: number
  }
  timeDistribution: ChartDataPoint[]
  templateAnalysis: {
    templateId: string
    templateName: string
    count: number
    percentage: number
  }[]
  quotaMonitoring: {
    provider: AIProvider
    dailyUsed: number
    dailyLimit: number
    monthlyUsed: number
    monthlyLimit: number
  }[]
}

// ==========================================================================
// 用户认证类型
// ==========================================================================

export interface User {
  id: string
  username: string
  role: 'admin' | 'teacher'
  avatar?: string
  email?: string
  permissions: string[]
  createdAt: number
  lastLoginAt: number
}

export interface LoginCredentials {
  username: string
  password: string
  remember?: boolean
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresAt: number
}

// ==========================================================================
// 系统配置类型
// ==========================================================================

export interface SystemConfig {
  site: {
    title: string
    description: string
    logo?: string
  }
  features: {
    aiProviders: AIProvider[]
    templateStyles: TemplateStyle[]
    maxDailyQuota: number
  }
  monitoring: {
    enableRealtime: boolean
    retentionDays: number
    alertThresholds: {
      errorRate: number
      responseTime: number
    }
  }
}

// ==========================================================================
// 表单验证类型
// ==========================================================================

export interface FormError {
  field: string
  message: string
}

export interface ValidationResult {
  isValid: boolean
  errors: FormError[]
}

// ==========================================================================
// 主题系统类型
// ==========================================================================

export type Theme = 'light' | 'dark' | 'auto'

export interface ThemeConfig {
  mode: Theme
  primaryColor: string
  borderRadius: 'none' | 'small' | 'medium' | 'large'
  compactMode: boolean
}

// ==========================================================================
// 导出工具类型
// ==========================================================================

export type ExportFormat = 'excel' | 'csv' | 'pdf' | 'json'

export interface ExportOptions {
  format: ExportFormat
  dateRange?: {
    start: string
    end: string
  }
  filters?: Record<string, any>
  columns?: string[]
}

// ==========================================================================
// 错误处理类型
// ==========================================================================

export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: number
  userId?: string
  action?: string
}

export type ErrorLevel = 'low' | 'medium' | 'high' | 'critical'

export interface ErrorReport {
  error: AppError
  level: ErrorLevel
  context: Record<string, any>
  stackTrace?: string
}

// ==========================================================================
// 状态管理类型
// ==========================================================================

export interface GlobalState {
  user: User | null
  theme: ThemeConfig
  config: SystemConfig
  loading: boolean
  error: AppError | null
}

export interface StoreActions {
  setUser: (user: User | null) => void
  setTheme: (theme: Partial<ThemeConfig>) => void
  setConfig: (config: Partial<SystemConfig>) => void
  setLoading: (loading: boolean) => void
  setError: (error: AppError | null) => void
  clearError: () => void
}

// ==========================================================================
// API端点类型
// ==========================================================================

export interface ApiEndpoints {
  // 认证相关
  login: string
  logout: string
  refresh: string
  profile: string
  
  // 仪表板相关
  dashboard: string
  statistics: string
  activities: string
  
  // AI配置相关
  aiConfigs: string
  testConnection: string
  updateConfig: string
  
  // 模板相关
  templates: string
  templateVariables: string
  
  // 使用监控相关
  usageStats: string
  usageRecords: string
  
  // 系统相关
  systemConfig: string
  healthCheck: string
}

// ==========================================================================
// 响应式断点类型
// ==========================================================================

export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'

export interface ResponsiveConfig<T> {
  xs?: T
  sm?: T
  md?: T
  lg?: T
  xl?: T
  '2xl'?: T
}

// ==========================================================================
// 工具类型
// ==========================================================================

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequireField<T, K extends keyof T> = T & Required<Pick<T, K>>

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type ValueOf<T> = T[keyof T]

export type NonEmptyArray<T> = [T, ...T[]]