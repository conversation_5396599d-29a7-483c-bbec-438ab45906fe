<!-- iOS风格弹窗演示页面 -->
<view class="demo-container">
  <view class="demo-header">
    <text class="demo-title">iOS风格弹窗演示</text>
    <text class="demo-subtitle">对比原生弹窗与iOS风格弹窗的差异</text>
  </view>

  <view class="demo-section">
    <view class="section-title">原生弹窗（复杂难看）</view>
    <button class="demo-button old" bindtap="showOldModal">显示原生复杂弹窗</button>
  </view>

  <view class="demo-section">
    <view class="section-title">iOS风格弹窗（高级简约）</view>
    <button class="demo-button new" bindtap="showNewModal">显示iOS风格弹窗</button>
  </view>

  <view class="demo-features">
    <view class="feature-title">✨ iOS风格弹窗优势</view>
    <view class="feature-list">
      <view class="feature-item">🎨 高级视觉设计，符合现代审美</view>
      <view class="feature-item">🌊 丝滑动画效果，流畅的用户体验</view>
      <view class="feature-item">📱 毛玻璃背景，真实的iOS质感</view>
      <view class="feature-item">📋 结构化内容展示，清晰易读</view>
      <view class="feature-item">⚡ 数据驱动，易于维护和扩展</view>
      <view class="feature-item">🎯 支持列表交互，功能更丰富</view>
    </view>
  </view>
</view>

<!-- iOS风格弹窗组件 -->
<ios-modal
  show="{{showModal}}"
  title="数据备份"
  icon="💾"
  description="即将创建完整数据备份"
  items="{{backupItems}}"
  showCheckmarks="{{true}}"
  features="{{backupFeatures}}"
  confirmText="开始备份"
  cancelText="稍后再说"
  bind:confirm="onModalConfirm"
  bind:cancel="onModalCancel"
/>
