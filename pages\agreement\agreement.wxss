/* 协议文档页面样式 */

.agreement-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

/* 页面标题 */
.page-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
}

.page-title {
  font-size: 44rpx;
  font-weight: bold;
  color: #ffffff;
}

/* 标签切换 */
.tab-container {
  display: flex;
  margin: 0 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50rpx;
  padding: 8rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 42rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.9);
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.tab-item.active .tab-text {
  color: #333;
  font-weight: bold;
}

/* 内容区域 */
.content-container {
  flex: 1;
  margin: 0 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 0;
  overflow: hidden;
}

.content-section {
  padding: 40rpx;
}

.content-text {
  font-size: 26rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 底部操作栏 */
.bottom-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.action-button {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button.primary {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
}

.action-button.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.action-button:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6rpx;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3rpx;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3rpx;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}
