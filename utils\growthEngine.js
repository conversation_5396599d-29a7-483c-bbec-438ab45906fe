/**
 * 用户增长引擎 - 独立模块，不影响现有功能
 * 专注于500UV增长目标，提供分享、留存、转化功能
 */

// 【修复】导入新版系统信息工具
const { getSystemInfo, getPlatform } = require('./systemInfo');

// 增长配置 - 可随时开启/关闭功能
const GROWTH_CONFIG = {
  enabled: true,                    // 总开关
  shareReward: true,               // 分享奖励
  newUserGuide: true,              // 新用户引导
  userTracking: true,              // 用户行为跟踪
  viralFeatures: true,             // 病毒传播功能
  retentionFeatures: true          // 留存功能
}

// 用户行为统计
class GrowthTracker {
  constructor() {
    this.userId = this.getUserId()
    this.sessionId = this.generateSessionId()
  }

  // 获取用户ID（基于微信openid或生成唯一ID）
  getUserId() {
    try {
      // 先检查是否已有growth_user_id
      let userId = wx.getStorageSync('growth_user_id')
      if (userId) {
        return userId
      }

      // 尝试使用微信用户信息（如果可用），但不修改原有userInfo
      try {
        const userInfo = wx.getStorageSync('userInfo')
        if (userInfo && userInfo.openid) {
          // 使用openid作为用户ID，但不修改原userInfo存储
          const growthUserId = 'growth_' + userInfo.openid
          wx.setStorageSync('growth_user_id', growthUserId)
          return growthUserId
        }
      } catch (e) {
        console.warn('无法获取微信用户信息，使用生成ID')
      }
      
      // 生成新的唯一ID
      userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      wx.setStorageSync('growth_user_id', userId)
      console.log('[Growth] 生成新用户ID:', userId)
      return userId
    } catch (error) {
      console.error('获取用户ID失败:', error)
      // 降级方案：返回临时ID
      return 'temp_' + Date.now()
    }
  }

  // 生成会话ID
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  // 记录用户行为
  trackEvent(eventName, eventData = {}) {
    if (!GROWTH_CONFIG.enabled || !GROWTH_CONFIG.userTracking) return

    try {
      const event = {
        userId: this.userId,
        sessionId: this.sessionId,
        eventName,
        eventData,
        timestamp: Date.now(),
        date: new Date().toLocaleDateString(),
        page: getCurrentPages().length > 0 ? getCurrentPages().slice(-1)[0].route : 'unknown'
      }

      // 保存到本地存储
      this.saveEventToLocal(event)
      
      // 异步上传到云端（不影响用户体验）
      this.uploadEventToCloud(event)
      
      console.log('[Growth] 记录事件:', eventName, eventData)
    } catch (error) {
      console.error('[Growth] 事件记录失败:', error)
    }
  }

  // 保存事件到本地
  saveEventToLocal(event) {
    try {
      let events = wx.getStorageSync('growth_events') || []
      events.push(event)
      
      // 只保留最近100个事件，避免存储过大
      if (events.length > 100) {
        events = events.slice(-100)
      }
      
      wx.setStorageSync('growth_events', events)
    } catch (error) {
      console.error('保存事件到本地失败:', error)
    }
  }

  // 异步上传事件到云端
  async uploadEventToCloud(event) {
    try {
      // 使用现有的云服务上传数据
      wx.cloud.callFunction({
        name: 'reportMonitoring',
        data: {
          type: 'user_behavior',
          event: event
        }
      }).catch(error => {
        console.warn('[Growth] 云端上传失败，不影响功能:', error)
      })
    } catch (error) {
      console.warn('[Growth] 云端上传异常:', error)
    }
  }
}

// 智能分享引导引擎
class ShareEngine {
  constructor() {
    this.tracker = new GrowthTracker()
    this.shareHistory = this.loadShareHistory()
    this.userStats = this.loadUserStats()
  }

  // 智能分享内容生成 - 基于用户行为和成就
  getShareContent(page, context = {}) {
    if (!GROWTH_CONFIG.enabled) {
      return this.getDefaultShare()
    }

    // 获取用户统计数据
    const stats = this.getUserEfficiencyStats()
    const achievements = this.getUserAchievements()
    
    // 根据用户成就选择最佳分享内容
    const shareContent = this.selectOptimalShareContent(page, stats, achievements, context)
    
    // 记录分享行为
    this.tracker.trackEvent('share_triggered', {
      page: page,
      shareTitle: shareContent.title,
      shareType: shareContent.type,
      userStats: stats,
      context: context
    })

    return shareContent
  }

  // 选择最优分享内容
  selectOptimalShareContent(page, stats, achievements, context) {
    // 基于效率成就的分享内容
    if (stats.timeSaved > 60) { // 节省超过1小时
      return {
        type: 'efficiency_achievement',
        title: `用AI助手节省了${Math.floor(stats.timeSaved/60)}小时！期末评语3分钟搞定 ⚡`,
        desc: `已为${stats.commentsGenerated}个学生生成专业评语`,
        imageUrl: '/images/share/efficiency-share.jpg',
        path: `${this.getPagePath(page)}?from=share_efficiency&stats=${stats.timeSaved}`
      }
    }

    // 基于质量成就的分享内容
    if (stats.qualityScore > 8.5) {
      return {
        type: 'quality_achievement', 
        title: `AI评语质量评分${stats.qualityScore}分！专业度大幅提升 🏆`,
        desc: '让每个学生都能收到个性化、专业的评语',
        imageUrl: '/images/share/quality-share.jpg',
        path: `${this.getPagePath(page)}?from=share_quality&score=${stats.qualityScore}`
      }
    }

    // 基于数量成就的分享内容
    if (stats.commentsGenerated >= 50) {
      return {
        type: 'quantity_achievement',
        title: `已生成${stats.commentsGenerated}条AI评语！教师工作效率神器 📝`,
        desc: '告别熬夜写评语，拥抱智能教育时代',
        imageUrl: '/images/share/quantity-share.jpg',
        path: `${this.getPagePath(page)}?from=share_quantity&count=${stats.commentsGenerated}`
      }
    }

    // 基于页面的默认分享内容
    return this.getPageBasedShareContent(page, context)
  }

  // 基于页面的分享内容
  getPageBasedShareContent(page, context) {
    const shareTemplates = {
      'pages/index/index': {
        type: 'homepage',
        title: '发现了教师必备神器！AI评语助手让工作轻松3倍 ✨',
        desc: '3分钟生成专业评语，期末不再熬夜',
        imageUrl: '/images/share/home-share.jpg',
        path: '/pages/index/index?from=share_home'
      },
      'pages/comment/generate/generate': {
        type: 'generate_tool',
        title: '这个AI评语生成器太强了！老师们的救星 🤖',
        desc: '智能分析学生表现，生成个性化专业评语',
        imageUrl: '/images/share/generate-share.jpg', 
        path: '/pages/comment/generate/generate?from=share_generate'
      },
      'pages/student/list/list': {
        type: 'student_management',
        title: '班级管理从未如此简单！学生信息一目了然 📊',
        desc: '高效管理学生信息，智能生成成长记录',
        imageUrl: '/images/share/student-share.jpg',
        path: '/pages/student/list/list?from=share_student'
      },
      'pages/comment/list/list': {
        type: 'comment_showcase',
        title: '看看我用AI生成的专业评语！质量超出预期 📋',
        desc: '个性化评语让每个学生都感受到关注',
        imageUrl: '/images/share/comments-share.jpg',
        path: '/pages/comment/list/list?from=share_comments'
      }
    }

    return shareTemplates[page] || this.getDefaultShare()
  }

  // 获取用户效率统计
  getUserEfficiencyStats() {
    try {
      const savedComments = wx.getStorageSync('savedComments') || []
      const recentComments = wx.getStorageSync('recentComments') || []
      const allComments = [...savedComments, ...recentComments]
      
      // 计算统计数据
      const commentsGenerated = allComments.length
      const timeSaved = commentsGenerated * 3 // 每条评语节省3分钟
      
      // 计算质量评分（基于评语长度和多样性）
      let qualityScore = 0
      if (commentsGenerated > 0) {
        const avgLength = allComments.reduce((sum, comment) => 
          sum + (comment.content?.length || comment.comment?.length || 0), 0) / commentsGenerated
        const styleVariety = new Set(allComments.map(c => c.style)).size
        qualityScore = Math.min(10, Math.max(6, (avgLength * 0.05) + (styleVariety * 1.5)))
      }

      return {
        commentsGenerated,
        timeSaved,
        qualityScore: Math.round(qualityScore * 10) / 10
      }
    } catch (error) {
      console.error('获取用户统计失败:', error)
      return { commentsGenerated: 0, timeSaved: 0, qualityScore: 0 }
    }
  }

  // 获取用户成就
  getUserAchievements() {
    const stats = this.getUserEfficiencyStats()
    const achievements = []

    // 效率成就
    if (stats.timeSaved >= 180) achievements.push('time_master') // 节省3小时+
    if (stats.timeSaved >= 60) achievements.push('efficiency_expert') // 节省1小时+
    
    // 质量成就
    if (stats.qualityScore >= 9) achievements.push('quality_master')
    if (stats.qualityScore >= 8) achievements.push('quality_expert')
    
    // 数量成就
    if (stats.commentsGenerated >= 100) achievements.push('comment_master')
    if (stats.commentsGenerated >= 50) achievements.push('comment_expert')
    if (stats.commentsGenerated >= 10) achievements.push('comment_starter')

    // 连续使用成就
    const visitHistory = wx.getStorageSync('visit_history') || []
    if (visitHistory.length >= 7) achievements.push('loyal_user')
    if (visitHistory.length >= 3) achievements.push('regular_user')

    return achievements
  }

  // 获取页面路径
  getPagePath(page) {
    return `/${page}`
  }

  // 分享成功回调 - 增强版
  onShareSuccess(shareResult) {
    const shareType = shareResult.type || 'default'
    
    this.tracker.trackEvent('share_success', {
      ...shareResult,
      shareType: shareType,
      timestamp: Date.now()
    })
    
    // 记录分享历史
    this.recordShareHistory(shareResult)
    
    // 分享成功奖励
    if (GROWTH_CONFIG.shareReward) {
      this.giveShareReward(shareType)
    }

    // 显示个性化成功提示
    this.showPersonalizedShareSuccess(shareType)
  }

  // 记录分享历史
  recordShareHistory(shareResult) {
    try {
      this.shareHistory.push({
        ...shareResult,
        timestamp: Date.now(),
        date: new Date().toLocaleDateString()
      })

      // 只保留最近50次分享记录
      if (this.shareHistory.length > 50) {
        this.shareHistory = this.shareHistory.slice(-50)
      }

      wx.setStorageSync('share_history', this.shareHistory)
    } catch (error) {
      console.error('记录分享历史失败:', error)
    }
  }

  // 个性化分享奖励
  giveShareReward(shareType) {
    try {
      let freeCount = wx.getStorageSync('free_ai_count') || 10
      let rewardCount = 2 // 默认奖励

      // 根据分享类型给予不同奖励
      const rewardMap = {
        'efficiency_achievement': 5,  // 效率成就分享奖励更多
        'quality_achievement': 4,    // 质量成就分享
        'quantity_achievement': 3,   // 数量成就分享
        'default': 2                 // 普通分享
      }

      rewardCount = rewardMap[shareType] || 2
      freeCount += rewardCount
      
      wx.setStorageSync('free_ai_count', freeCount)

      // 记录奖励
      this.tracker.trackEvent('share_reward_given', { 
        shareType: shareType,
        rewardCount: rewardCount,
        totalFreeCount: freeCount
      })

    } catch (error) {
      console.error('分享奖励失败:', error)
    }
  }

  // 显示个性化分享成功提示
  showPersonalizedShareSuccess(shareType) {
    const messages = {
      'efficiency_achievement': '🎉 效率达人！分享成功，获得5次免费AI生成',
      'quality_achievement': '🏆 质量专家！分享成功，获得4次免费AI生成', 
      'quantity_achievement': '📝 生产力之星！分享成功，获得3次免费AI生成',
      'default': '✨ 分享成功！获得2次免费AI生成'
    }

    const message = messages[shareType] || messages.default

    wx.showToast({
      title: message,
      icon: 'success',
      duration: 3000
    })
  }

  // 加载分享历史
  loadShareHistory() {
    try {
      return wx.getStorageSync('share_history') || []
    } catch (error) {
      return []
    }
  }

  // 加载用户统计
  loadUserStats() {
    try {
      return wx.getStorageSync('user_stats') || {}
    } catch (error) {
      return {}
    }
  }

  // 默认分享内容
  getDefaultShare() {
    return {
      type: 'default',
      title: '评语灵感君 - 教师专用AI评语助手，让教学更轻松 ✨',
      desc: '3分钟生成专业评语，告别熬夜写评语',
      imageUrl: '/images/share/default-share.jpg',
      path: '/pages/index/index?from=share_default'
    }
  }
}

// 新用户引导系统
class NewUserGuide {
  constructor() {
    this.tracker = new GrowthTracker()
  }

  // 检查是否是新用户
  isNewUser() {
    try {
      // 检查是否已经使用过应用
      const hasUsed = wx.getStorageSync('has_used_app')

      // 检查是否已经登录过（有用户信息或token）
      const userInfo = wx.getStorageSync('userInfo')
      const token = wx.getStorageSync('token')
      const isGuestMode = wx.getStorageSync('isGuestMode')

      // 如果已经登录过或使用过，就不是新用户
      if (hasUsed || userInfo || token || isGuestMode) {
        return false
      }

      return true // 只有完全没有使用痕迹才是新用户
    } catch (error) {
      return false // 出错时按老用户处理，避免重复提示
    }
  }

  // 显示新用户引导
  showNewUserGuide(currentPage) {
    if (!GROWTH_CONFIG.enabled || !GROWTH_CONFIG.newUserGuide) return

    // 严格检查是否是新用户
    if (!this.isNewUser()) {
      console.log('[新用户引导] 用户已使用过应用，跳过新用户引导')
      return
    }

    console.log('[新用户引导] 检测到新用户，显示引导')
    this.tracker.trackEvent('new_user_guide_start', { page: currentPage })

    // 给新用户额外的免费次数
    try {
      wx.setStorageSync('free_ai_count', 20) // 新用户20次免费
      wx.setStorageSync('has_used_app', true) // 标记已使用

      wx.showModal({
        title: '🎉 欢迎使用评语灵感君',
        content: '新用户专享20次免费AI评语生成！快来体验智能评语助手吧～',
        showCancel: false,
        confirmText: '立即体验',
        success: (res) => {
          this.tracker.trackEvent('new_user_guide_complete')
          console.log('[新用户引导] 用户确认完成引导')
        }
      })
    } catch (error) {
      console.error('新用户引导失败:', error)
    }
  }

  // 显示功能引导气泡
  showFeatureTips(tipType, targetSelector) {
    if (!GROWTH_CONFIG.enabled) return

    const tips = {
      'first_generate': '点击这里开始生成你的第一条AI评语！',
      'share_feature': '生成评语后可以分享给同事哦～',
      'student_manage': '在这里管理你的学生信息'
    }

    const tipContent = tips[tipType]
    if (!tipContent) return

    // 这里可以实现气泡提示组件
    console.log('[Growth] 显示功能提示:', tipContent)
  }
}

// 用户留存系统
class RetentionEngine {
  constructor() {
    this.tracker = new GrowthTracker()
  }

  // 记录用户访问
  recordVisit() {
    if (!GROWTH_CONFIG.enabled || !GROWTH_CONFIG.retentionFeatures) return

    try {
      const today = new Date().toDateString()
      let visitHistory = wx.getStorageSync('visit_history') || []
      
      // 如果今天还没记录访问，则记录
      if (!visitHistory.includes(today)) {
        visitHistory.push(today)
        wx.setStorageSync('visit_history', visitHistory)
        
        this.tracker.trackEvent('daily_visit', { 
          visitDays: visitHistory.length,
          isConsecutive: this.checkConsecutiveVisits(visitHistory)
        })

        // 连续访问奖励
        this.checkConsecutiveReward(visitHistory)
      }
    } catch (error) {
      console.error('记录访问失败:', error)
    }
  }

  // 检查连续访问天数
  checkConsecutiveVisits(visitHistory) {
    if (visitHistory.length < 2) return false
    
    const dates = visitHistory.map(d => new Date(d)).sort((a, b) => b - a)
    let consecutive = 1
    
    for (let i = 1; i < dates.length; i++) {
      const diffDays = (dates[i-1] - dates[i]) / (24 * 60 * 60 * 1000)
      if (diffDays === 1) {
        consecutive++
      } else {
        break
      }
    }
    
    return consecutive
  }

  // 连续访问奖励
  checkConsecutiveReward(visitHistory) {
    const consecutive = this.checkConsecutiveVisits(visitHistory)
    
    if (consecutive >= 3 && consecutive <= 7) {
      // 连续3-7天访问奖励
      let freeCount = wx.getStorageSync('free_ai_count') || 0
      freeCount += consecutive
      wx.setStorageSync('free_ai_count', freeCount)
      
      wx.showToast({
        title: `连续${consecutive}天使用！奖励${consecutive}次免费生成`,
        icon: 'success',
        duration: 3000
      })
      
      this.tracker.trackEvent('consecutive_visit_reward', { days: consecutive })
    }
  }
}

// 全局增长引擎实例
const growthEngine = {
  tracker: new GrowthTracker(),
  shareEngine: new ShareEngine(),
  newUserGuide: new NewUserGuide(),
  retentionEngine: new RetentionEngine(),

  // 初始化增长功能
  init(currentPage) {
    if (!GROWTH_CONFIG.enabled) return

    try {
      // 记录页面访问
      this.tracker.trackEvent('page_visit', { page: currentPage })
      
      // 记录用户访问（用于留存统计）
      this.retentionEngine.recordVisit()
      
      // 新用户引导
      this.newUserGuide.showNewUserGuide(currentPage)
      
      console.log('[Growth] 增长引擎初始化完成')
    } catch (error) {
      console.error('[Growth] 初始化失败:', error)
    }
  },

  // 获取优化的分享内容
  getShareContent(page, context = {}) {
    return this.shareEngine.getShareContent(page, context)
  },

  // 分享成功回调
  onShareSuccess(result) {
    this.shareEngine.onShareSuccess(result)
  },

  // 记录用户行为
  track(eventName, eventData = {}) {
    this.tracker.trackEvent(eventName, eventData)
  }
}

// 导出增长引擎
module.exports = {
  growthEngine,
  GROWTH_CONFIG,
  GrowthTracker // 导出类供测试使用
}