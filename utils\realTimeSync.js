/**
 * 数据实时同步管理器
 * 负责各模块间的数据实时同步和状态管理
 */

class RealTimeSyncManager {
  constructor() {
    this.listeners = new Map(); // 数据变更监听器
    this.cache = new Map(); // 数据缓存
    this.syncQueue = []; // 同步队列
    this.isOnline = true; // 网络状态
    
    this.init();
  }

  /**
   * 初始化
   */
  init() {
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.isOnline = res.isConnected;
      if (this.isOnline && this.syncQueue.length > 0) {
        this.processSyncQueue();
      }
    });

    // 监听小程序显示事件，进行数据同步
    wx.onAppShow(() => {
      this.syncAllData();
    });
  }

  /**
   * 注册数据变更监听器
   */
  subscribe(dataType, callback) {
    if (!this.listeners.has(dataType)) {
      this.listeners.set(dataType, new Set());
    }
    this.listeners.get(dataType).add(callback);
  }

  /**
   * 取消监听
   */
  unsubscribe(dataType, callback) {
    if (this.listeners.has(dataType)) {
      this.listeners.get(dataType).delete(callback);
    }
  }

  /**
   * 通知数据变更
   */
  notify(dataType, data, operation = 'update') {
    // 更新缓存
    this.updateCache(dataType, data, operation);

    // 通知所有监听器
    if (this.listeners.has(dataType)) {
      this.listeners.get(dataType).forEach(callback => {
        try {
          callback(data, operation);
        } catch (error) {
          console.error('数据变更通知失败:', error);
        }
      });
    }
  }

  /**
   * 更新缓存
   */
  updateCache(dataType, data, operation) {
    if (!this.cache.has(dataType)) {
      this.cache.set(dataType, []);
    }

    const cacheData = this.cache.get(dataType);

    switch (operation) {
      case 'create':
        cacheData.push(data);
        break;
      case 'update':
        const updateIndex = cacheData.findIndex(item => item._id === data._id);
        if (updateIndex !== -1) {
          cacheData[updateIndex] = { ...cacheData[updateIndex], ...data };
        }
        break;
      case 'delete':
        const deleteIndex = cacheData.findIndex(item => item._id === data._id);
        if (deleteIndex !== -1) {
          cacheData.splice(deleteIndex, 1);
        }
        break;
      case 'refresh':
        this.cache.set(dataType, Array.isArray(data) ? data : [data]);
        break;
    }
  }

  /**
   * 获取缓存数据
   */
  getCachedData(dataType) {
    return this.cache.get(dataType) || [];
  }

  /**
   * 同步数据到云端
   */
  async syncToCloud(dataType, data, operation) {
    if (!this.isOnline) {
      // 离线时加入同步队列
      this.syncQueue.push({ dataType, data, operation, timestamp: Date.now() });
      return;
    }

    try {
      const { cloudService } = require('../services/cloudService');
      
      switch (dataType) {
        case 'students':
          await this.syncStudentData(data, operation);
          break;
        case 'classes':
          await this.syncClassData(data, operation);
          break;
        case 'records':
          await this.syncRecordData(data, operation);
          break;
        default:

      }
    } catch (error) {
      console.error('数据同步失败:', error);
      // 同步失败时加入重试队列
      this.syncQueue.push({ dataType, data, operation, timestamp: Date.now(), retryCount: 1 });
    }
  }

  /**
   * 同步学生数据
   */
  async syncStudentData(data, operation) {
    const { cloudService } = require('../services/cloudService');
    
    switch (operation) {
      case 'create':
        await cloudService.addStudent(data);
        break;
      case 'update':
        await cloudService.updateStudent(data._id, data);
        break;
      case 'delete':
        await cloudService.deleteStudent(data._id);
        break;
    }
  }

  /**
   * 同步班级数据
   */
  async syncClassData(data, operation) {
    const { cloudService } = require('../services/cloudService');
    
    switch (operation) {
      case 'create':
        await cloudService.createClass(data);
        break;
      case 'update':
        await cloudService.updateClass(data._id, data);
        break;
      case 'delete':
        await cloudService.deleteClass(data._id);
        break;
    }
  }

  /**
   * 同步记录数据
   */
  async syncRecordData(data, operation) {
    const { cloudService } = require('../services/cloudService');
    
    switch (operation) {
      case 'create':
        await cloudService.createRecord(data);
        break;
      case 'update':
        await cloudService.updateRecord(data._id, data);
        break;
      case 'delete':
        await cloudService.deleteRecord(data._id);
        break;
    }
  }

  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    if (!this.isOnline || this.syncQueue.length === 0) {
      return;
    }

    const queue = [...this.syncQueue];
    this.syncQueue = [];

    for (const item of queue) {
      try {
        await this.syncToCloud(item.dataType, item.data, item.operation);
      } catch (error) {
        console.error('队列同步失败:', error);
        // 重试次数小于3次时重新加入队列
        if ((item.retryCount || 0) < 3) {
          this.syncQueue.push({
            ...item,
            retryCount: (item.retryCount || 0) + 1
          });
        }
      }
    }
  }

  /**
   * 同步所有数据
   */
  async syncAllData() {
    try {
      const { cloudService } = require('../services/cloudService');
      
      // 并行获取所有数据
      const [studentsResult, classesResult, recordsResult] = await Promise.all([
        cloudService.getStudentList(),
        cloudService.getClassList(),
        cloudService.getRecordList({ pageSize: 1000 })
      ]);

      // 更新缓存并通知监听器
      if (studentsResult.success) {
        this.notify('students', studentsResult.data, 'refresh');
      }

      if (classesResult.success) {
        this.notify('classes', classesResult.data, 'refresh');
      }

      if (recordsResult.success) {
        this.notify('records', recordsResult.data, 'refresh');
      }

    } catch (error) {
      console.error('全量数据同步失败:', error);
    }
  }

  /**
   * 清除缓存
   */
  clearCache(dataType = null) {
    if (dataType) {
      this.cache.delete(dataType);
    } else {
      this.cache.clear();
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      queueLength: this.syncQueue.length,
      cacheSize: this.cache.size
    };
  }
}

// 创建全局实例
const realTimeSyncManager = new RealTimeSyncManager();

module.exports = {
  realTimeSyncManager,
  RealTimeSyncManager
};
