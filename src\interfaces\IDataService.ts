/**
 * 统一数据访问接口定义
 * 为小程序端和管理后台提供一致的数据访问抽象层
 */

// 通用分页参数
export interface PaginationParams {
  page?: number
  limit?: number
  keyword?: string
  [key: string]: any
}

// 通用分页结果
export interface PaginatedResult<T> {
  list: T[]
  total: number
  page: number
  limit: number
  pages?: number
}

// 仪表板统计数据
export interface DashboardStats {
  totalUsers: number
  todayComments: number
  aiCalls: number
  satisfaction: number
  lastUpdated: string
}

// 活动记录
export interface ActivityRecord {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  action: string
  actionType: 'comment_generate' | 'data_import' | 'config_update' | 'report_export' | 'batch_operation'
  timestamp: string
  metadata?: Record<string, any>
}

// 学生信息
export interface Student {
  id: string
  name: string
  studentId?: string
  class?: string
  className?: string
  teacher?: string
  teacherName?: string
  commentsCount?: number
  lastUpdate?: string
  status?: 'active' | 'inactive'
  grade?: string
  age?: number
  gender?: string
  personality?: Record<string, any>
  subjects?: string[]
}

// 评语信息
export interface Comment {
  id: string
  studentId: string
  studentName: string
  teacherId: string
  teacherName: string
  content: string
  aiModel?: string
  tokensUsed?: number
  createTime: string
  subject?: string
  templateType?: string
  style?: string
  length?: string
}

// 系统性能指标
export interface SystemMetrics {
  cpu: number
  memory: number
  storage: number
  apiResponseTime: number
  activeConnections: number
  timestamp: string
}

// 连接测试结果
export interface ConnectionTestResult {
  success: boolean
  message: string
  collections: string[]
  sampleData: any
}

// 用户信息
export interface User {
  id: string
  openid: string
  role: 'teacher' | 'admin'
  profile: {
    nickName?: string
    avatarUrl?: string
    city?: string
    province?: string
    country?: string
    gender?: number
    [key: string]: any
  }
  createTime: string
  lastLoginTime: string
  loginCount: number
  status: 'active' | 'inactive'
}

// AI配置
export interface AIConfig {
  model: string
  apiKey: string
  baseUrl?: string
  temperature?: number
  maxTokens?: number
  systemPrompt?: string
  enabledFeatures: string[]
  rateLimit?: {
    requestsPerMinute: number
    tokensPerDay: number
  }
}

/**
 * 统一数据服务接口
 * 定义所有数据访问操作的标准契约
 */
export interface IDataService {
  // 基础数据查询
  getDashboardStats(): Promise<DashboardStats>
  getRecentActivities(limit?: number): Promise<ActivityRecord[]>
  getSystemMetrics(): Promise<SystemMetrics>
  testConnection(): Promise<ConnectionTestResult>

  // 学生数据管理
  getStudents(params?: PaginationParams): Promise<PaginatedResult<Student>>
  getStudent(id: string): Promise<Student | null>
  createStudent(student: Omit<Student, 'id'>): Promise<Student>
  updateStudent(id: string, updates: Partial<Student>): Promise<Student>
  deleteStudent(id: string): Promise<boolean>

  // 评语数据管理
  getComments(params?: PaginationParams): Promise<PaginatedResult<Comment>>
  getComment(id: string): Promise<Comment | null>
  createComment(comment: Omit<Comment, 'id'>): Promise<Comment>
  updateComment(id: string, updates: Partial<Comment>): Promise<Comment>
  deleteComment(id: string): Promise<boolean>

  // 用户管理
  getUsers(params?: PaginationParams): Promise<PaginatedResult<User>>
  getUser(id: string): Promise<User | null>
  createUser(user: Omit<User, 'id'>): Promise<User>
  updateUser(id: string, updates: Partial<User>): Promise<User>
  deleteUser(id: string): Promise<boolean>

  // 系统配置
  getAIConfig(): Promise<AIConfig>
  updateAIConfig(config: Partial<AIConfig>): Promise<AIConfig>
  
  // 数据统计
  getUserStats(): Promise<any>
  getCommentStats(): Promise<any>
  getAIStats(): Promise<any>
  getSystemHealth(): Promise<any>

  // 数据导出
  exportData(type: 'students' | 'comments' | 'users', format?: 'excel' | 'csv'): Promise<boolean>
}

/**
 * 认证服务接口
 */
export interface IAuthService {
  login(credentials?: any): Promise<{ token: string; user: User }>
  logout(): Promise<void>
  getCurrentUser(): Promise<User | null>
  refreshToken(): Promise<string>
  checkPermission(permission: string): Promise<boolean>
}

/**
 * 实时通信服务接口
 */
export interface IRealtimeService {
  connect(): Promise<void>
  disconnect(): void
  isConnected(): boolean
  
  sendMessage(type: string, payload: any): void
  onMessage(type: string, callback: (data: any) => void): void
  offMessage(type: string, callback: (data: any) => void): void
  
  getConnectionStats(): {
    connected: boolean
    reconnectAttempts: number
    messagesSent: number
    messagesReceived: number
    lastActivity: string | null
  }
}

/**
 * 缓存服务接口
 */
export interface ICacheService {
  get<T>(key: string): Promise<T | null>
  set<T>(key: string, value: T, ttl?: number): Promise<void>
  delete(key: string): Promise<void>
  clear(pattern?: string): Promise<void>
  
  // 批量操作
  mget<T>(keys: string[]): Promise<(T | null)[]>
  mset<T>(items: Array<{ key: string; value: T; ttl?: number }>): Promise<void>
  
  // 统计信息
  getStats(): Promise<{
    hitRate: number
    missRate: number
    totalRequests: number
    cacheSize: number
  }>
}

/**
 * 数据服务工厂接口
 * 用于创建不同平台的数据服务实例
 */
export interface IDataServiceFactory {
  createDataService(platform: 'miniprogram' | 'admin'): IDataService
  createAuthService(platform: 'miniprogram' | 'admin'): IAuthService
  createRealtimeService(config?: any): IRealtimeService
  createCacheService(config?: any): ICacheService
}

/**
 * 服务配置接口
 */
export interface ServiceConfig {
  apiBaseUrl?: string
  wsUrl?: string
  cloudEnv?: string
  appId?: string
  region?: string
  timeout?: number
  retryAttempts?: number
  cacheEnabled?: boolean
  logLevel?: 'debug' | 'info' | 'warn' | 'error'
}

export default IDataService