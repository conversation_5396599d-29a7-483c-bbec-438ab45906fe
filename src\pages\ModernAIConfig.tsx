import React, { useState } from 'react'
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Button, 
  Space, 
  Typography, 
  Tabs, 
  InputNumber,
  Switch,
  message,
  Table,
  Tag,
  Descriptions
} from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, EyeOutlined } from '@ant-design/icons'
import InlineDrawer from '../components/InlineDrawer'

const { Title, Text, Paragraph } = Typography
const { TextArea } = Input
const { TabPane } = Tabs

const ModernAIConfig: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [currentAI, setCurrentAI] = useState<any>(null)
  const [currentTemplate, setCurrentTemplate] = useState<any>(null)
  const [drawerMode, setDrawerMode] = useState<'model' | 'template' | 'test'>('model')

  const aiModels = [
    {
      id: '1',
      name: '豆包模型',
      provider: 'bytedance',
      model: 'doubao-pro-32k',
      status: 'active',
      apiKey: 'bce-v3-xxx',
      usage: 1234,
      cost: 45.67,
      lastUsed: '2024-01-15 14:30',
      responseTime: '1.2s',
      quality: '优秀'
    },
    {
      id: '2', 
      name: 'GPT-4',
      provider: 'openai',
      model: 'gpt-4-turbo',
      status: 'inactive',
      apiKey: 'sk-xxx',
      usage: 0,
      cost: 0,
      lastUsed: '-',
      responseTime: '-',
      quality: '待测试'
    }
  ]

  const promptTemplates = [
    {
      id: '1',
      name: '积极鼓励型评语',
      type: 'positive',
      preview: '你在课堂上总是积极举手发言，数学解题思路越来越清晰...',
      usage: 1256,
      parameters: ['学生基本信息', '具体表现', '家长期望'],
      example: '你在课堂上总是积极举手发言，数学解题思路越来越清晰，老师为你的进步感到骄傲！继续保持这份好奇心和努力，相信你会在语文阅读上也能取得同样的进步，加油！'
    },
    {
      id: '2',
      name: '客观建议型评语',
      type: 'objective',
      preview: '本学期你在数学方面表现稳定，课堂练习正确率达到85%...',
      usage: 894,
      parameters: ['学生成绩数据', '课堂表现记录', '作业完成情况'],
      example: '本学期你在数学方面表现稳定，课堂练习正确率达到85%，特别是在几何图形认知方面有明显优势。建议在语文阅读理解方面加强练习，可以尝试每天阅读20分钟课外书，并做好读书笔记。'
    }
  ]

  const onFinish = async (values: any) => {
    setLoading(true)
    try {
      
          min-height: 100vh;
          padding: 24px;
        }

        .config-header {
          padding: 24px 0;
          text-align: center;
          border-radius: 12px;
          background: white;
          margin-bottom: 24px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .config-card {
          border-radius: 12px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          margin-bottom: 24px;
        }

        .config-tabs .ant-tabs-nav {
          background: white;
          border-radius: 12px 12px 0 0;
          margin: 0;
          padding: 8px;
        }

        .modern-table .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 600;
        }

        @media (max-width: 768px) {
          .modern-ai-config {
            padding: 12px;
          }
        }
      `}</style>
    </div>
  )
}

export default ModernAIConfig