const cloud = require('wx-server-sdk');

class UserHandler {
  constructor(db) {
    this.db = db;
    this._ = db.command;
  }

  // 获取所有用户信息（带分页）
  async getAllUsers(event) {
    const { page = 1, limit = 20, searchText = '' } = event;
    const skip = (page - 1) * limit;

    try {
      let query = {};
      
      if (searchText) {
        query = {
          nickName: this.db.RegExp({
            regexp: searchText,
            options: 'i'
          })
        };
      }

      const [users, total] = await Promise.all([
        this.db.collection('users')
          .where(query)
          .skip(skip)
          .limit(limit)
          .orderBy('createdAt', 'desc')
          .get(),
        this.db.collection('users').where(query).count()
      ]);

      // 获取AI使用记录
      const usersWithStats = await Promise.all(
        users.data.map(async (user) => {
          const aiUsage = await this.getUserAIUsage(user._id);
          return {
            ...user,
            aiUsage: {
              totalCalls: aiUsage.total,
              currentMonth: aiUsage.monthly,
              lastUsed: aiUsage.lastUsed
            }
          };
        })
      );

      return {
        success: true,
        data: {
          list: usersWithStats,
          total: total.total,
          page,
          pages: Math.ceil(total.total / limit)
        }
      };
    } catch (error) {
      console.error('获取用户列表失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取用户AI使用统计
  async getUserAIUsage(userId) {
    try {
      const [usageRecords, recentRecords] = await Promise.all([
        this.db.collection('ai_usage').where({ userId }).get(),
        this.db.collection('ai_usage').where({ userId }).orderBy('createTime', 'desc').limit(10).get()
      ]);

      const monthlyUsage = usageRecords.data.filter(record => {
        const recordDate = new Date(record.createTime);
        const now = new Date();
        return now.getFullYear() === recordDate.getFullYear() && 
               now.getMonth() === recordDate.getMonth();
      });

      return {
        total: usageRecords.data.length,
        monthly: monthlyUsage.length,
        lastUsed: recentRecords.data[0]?.createTime || null,
        recent: recentRecords.data
      };
    } catch (error) {
      console.error('获取AI使用统计失败:', error);
      return { total: 0, monthly: 0, lastUsed: null, recent: [] };
    }
  }

  // 更新用户AI使用限制
  async updateUserLimit(event) {
    const { userId, limit } = event;
    
    try {
      await this.db.collection('user_limits').doc(userId).set({
        aiLimit: limit,
        updatedAt: new Date()
      });

      return { success: true, message: '更新成功' };
    } catch (error) {
      console.error('更新用户限制失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 同步微信用户信息
  async syncWxUserInfo(event) {
    const { openid, userInfo } = event;
    
    try {
      await this.db.collection('users').doc(openid).update({
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        gender: userInfo.gender,
        city: userInfo.city,
        province: userInfo.province,
        country: userInfo.country,
        wxSyncAt: new Date()
      });

      return { success: true, message: '同步成功' };
    } catch (error) {
      console.error('同步微信信息失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取用户数统计
  async getUserStats() {
    try {
      const [totalUsers, todayUsers, weekUsers, monthUsers] = await Promise.all([
        this.db.collection('users').count(),
        this.getPeriodUsers(1),
        this.getPeriodUsers(7),
        this.getPeriodUsers(30)
      ]);

      return {
        success: true,
        data: {
          totalUsers: totalUsers.total,
          todayUsers,
          weekUsers,
          monthUsers,
          activeRate: {
            daily: (todayUsers / totalUsers.total * 100).toFixed(2),
            weekly: (weekUsers / totalUsers.total * 100).toFixed(2),
            monthly: (monthUsers / totalUsers.total * 100).toFixed(2)
          }
        }
      };
    } catch (error) {
      console.error('获取用户统计失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取指定时间段的用户数
  async getPeriodUsers(days) {
    const startTime = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    const result = await this.db.collection('users')
      .where({
        createdAt: this._.gte(startTime)
      })
      .count();
    return result.total;
  }
}

module.exports = UserHandler;