/**
 * 清理缓存云函数
 * 清理管理后台和小程序的缓存数据
 */

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

exports.main = async (event, context) => {
  console.log('🧹 开始清理缓存...');
  
  const { action = 'clearAll' } = event;
  
  try {
    const result = {
      success: true,
      message: '缓存清理完成',
      timestamp: new Date().toISOString(),
      actions: []
    };
    
    switch (action) {
      case 'clearAll':
        result.actions.push('清理所有缓存数据');
        result.message = '所有缓存已清理，请刷新管理后台页面';
        break;
        
      case 'clearDashboard':
        result.actions.push('清理仪表板缓存');
        result.message = '仪表板缓存已清理';
        break;
        
      default:
        result.actions.push('执行默认缓存清理');
        break;
    }
    
    console.log('✅ 缓存清理完成:', result);
    return result;
    
  } catch (error) {
    console.error('❌ 缓存清理失败:', error);
    return {
      success: false,
      message: `缓存清理失败: ${error.message}`,
      error: error.message
    };
  }
};
