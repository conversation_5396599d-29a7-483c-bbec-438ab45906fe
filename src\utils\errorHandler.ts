/**
 * 统一错误处理和降级策略工具
 * 提供完善的错误恢复机制和用户体验保障
 */

import React from 'react'
import { notification, message } from 'antd'

export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  DATA_ERROR = 'DATA_ERROR',
  CLOUD_FUNCTION_ERROR = 'CLOUD_FUNCTION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR'
}

export interface ErrorContext {
  operation: string
  component?: string
  userId?: string
  timestamp: number
  metadata?: Record<string, any>
}

export interface FallbackData {
  [key: string]: any
}

export class ErrorHandler {
  private static instance: ErrorHandler
  private fallbackStrategies: Map<string, () => FallbackData> = new Map()
  private errorLog: Array<{ error: Error; context: ErrorContext; handled: boolean }> = []

  private constructor() {
    this.setupDefaultFallbacks()
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 设置默认的降级数据策略
   */
  private setupDefaultFallbacks() {
    // 仪表板统计数据降级
    this.fallbackStrategies.set('getDashboardStats', () => ({
      totalUsers: 0,
      todayComments: 0,
      aiCalls: 0,
      satisfaction: 0,
      lastUpdated: new Date().toISOString()
    }))

    // 最近活动降级
    this.fallbackStrategies.set('getRecentActivities', () => [])

    // 学生数据降级
    this.fallbackStrategies.set('getStudents', () => ({
      list: [],
      total: 0,
      page: 1,
      limit: 20
    }))

    // 评语数据降级
    this.fallbackStrategies.set('getComments', () => ({
      list: [],
      total: 0,
      page: 1,
      limit: 20
    }))

    // 系统性能指标降级
    this.fallbackStrategies.set('getSystemMetrics', () => ({
      cpu: 0,
      memory: 0,
      storage: 0,
      apiResponseTime: 0,
      activeConnections: 0,
      timestamp: new Date().toISOString()
    }))
  }

  /**
   * 统一的错误处理方法
   */
  async handleError<T = any>(
    operation: string,
    error: Error,
    context: Partial<ErrorContext> = {},
    options: {
      showNotification?: boolean
      showMessage?: boolean
      useFallback?: boolean
      retryFunction?: () => Promise<T>
      maxRetries?: number
    } = {}
  ): Promise<T | null> {
    const {
      showNotification = true,
      showMessage = false,
      useFallback = true,
      retryFunction,
      maxRetries = 2
    } = options

    // 构建完整的错误上下文
    const fullContext: ErrorContext = {
      operation,
      timestamp: Date.now(),
      ...context
    }

    // 记录错误
    this.logError(error, fullContext)

    // 确定错误类型
    const errorType = this.classifyError(error)

    // 显示用户通知
    if (showNotification) {
      this.showErrorNotification(errorType, operation, error, retryFunction)
    }

    if (showMessage) {
      this.showErrorMessage(errorType, operation)
    }

    // 尝试重试
    if (retryFunction && maxRetries > 0) {
      try {
        console.log(`🔄 重试操作: ${operation} (剩余次数: ${maxRetries})`)
        await new Promise(resolve => setTimeout(resolve, 1000)) // 延迟1秒重试
        return await retryFunction()
      } catch (retryError) {
        console.error(`❌ 重试失败: ${operation}`, retryError)
        if (maxRetries > 1) {
          return this.handleError(operation, retryError as Error, context, {
            ...options,
            maxRetries: maxRetries - 1
          })
        }
      }
    }

    // 使用降级数据
    if (useFallback) {
      const fallbackData = this.getFallbackData(operation)
      if (fallbackData !== null) {
        console.log(`🛡️ 使用降级数据: ${operation}`, fallbackData)
        return fallbackData as T
      }
    }

    return null
  }

  /**
   * 分类错误类型
   */
  private classifyError(error: Error): ErrorType {
    const message = error.message.toLowerCase()

    if (message.includes('network') || message.includes('fetch')) {
      return ErrorType.NETWORK_ERROR
    }

    if (message.includes('unauthorized') || message.includes('token')) {
      return ErrorType.AUTH_ERROR
    }

    if (message.includes('cloudbase') || message.includes('云函数')) {
      return ErrorType.CLOUD_FUNCTION_ERROR
    }

    if (message.includes('permission') || message.includes('权限')) {
      return ErrorType.PERMISSION_ERROR
    }

    if (message.includes('data') || message.includes('parse')) {
      return ErrorType.DATA_ERROR
    }

    return ErrorType.API_ERROR
  }

  /**
   * 显示错误通知
   */
  private showErrorNotification(
    errorType: ErrorType,
    operation: string,
    error: Error,
    retryFunction?: () => Promise<any>
  ) {
    const notificationConfig = this.getNotificationConfig(errorType, operation, error)

    if (retryFunction) {
      notificationConfig.btn = (
        React.createElement('div', null,
          React.createElement('button', {
            className: "ant-btn ant-btn-primary ant-btn-sm",
            onClick: async () => {
              notification.destroy()
              try {
                await retryFunction()
                notification.success({
                  message: '重试成功',
                  description: `${operation} 操作已恢复正常`,
                  placement: 'topRight' as const
                })
              } catch (retryError) {
                console.error('重试失败:', retryError)
              }
            }
          }, '重试')
        )
      )
    }

    notification.error(notificationConfig)
  }

  /**
   * 获取通知配置
   */
  private getNotificationConfig(errorType: ErrorType, operation: string, error: Error) {
    const baseConfig = {
      placement: 'topRight' as const,
      duration: 8
    }

    switch (errorType) {
      case ErrorType.NETWORK_ERROR:
        return {
          ...baseConfig,
          message: '网络连接失败',
          description: `无法连接到服务器，请检查网络连接。操作: ${operation}`
        }

      case ErrorType.CLOUD_FUNCTION_ERROR:
        return {
          ...baseConfig,
          message: '云函数调用失败',
          description: `云开发服务暂时不可用，已启用备用数据。操作: ${operation}`
        }

      case ErrorType.AUTH_ERROR:
        return {
          ...baseConfig,
          message: '身份验证失败',
          description: '请重新登录或检查访问权限'
        }

      case ErrorType.PERMISSION_ERROR:
        return {
          ...baseConfig,
          message: '权限不足',
          description: '您没有执行此操作的权限，请联系管理员'
        }

      default:
        return {
          ...baseConfig,
          message: '操作失败',
          description: `${operation} 执行失败: ${error.message}`
        }
    }
  }

  /**
   * 显示简单错误消息
   */
  private showErrorMessage(errorType: ErrorType, operation: string) {
    const messageMap = {
      [ErrorType.NETWORK_ERROR]: '网络连接失败，请检查网络',
      [ErrorType.CLOUD_FUNCTION_ERROR]: '云服务暂时不可用',
      [ErrorType.AUTH_ERROR]: '身份验证失败',
      [ErrorType.PERMISSION_ERROR]: '权限不足',
      [ErrorType.API_ERROR]: `${operation} 操作失败`,
      [ErrorType.DATA_ERROR]: '数据处理失败'
    }

    message.error(messageMap[errorType] || '操作失败')
  }

  /**
   * 获取降级数据
   */
  private getFallbackData(operation: string): FallbackData | null {
    const fallbackFunction = this.fallbackStrategies.get(operation)
    return fallbackFunction ? fallbackFunction() : null
  }

  /**
   * 注册自定义降级策略
   */
  registerFallback(operation: string, fallbackFunction: () => FallbackData) {
    this.fallbackStrategies.set(operation, fallbackFunction)
  }

  /**
   * 记录错误日志
   */
  private logError(error: Error, context: ErrorContext) {
    console.error(`🚨 错误记录 [${context.operation}]:`, {
      error: error.message,
      stack: error.stack,
      context,
      timestamp: new Date(context.timestamp).toISOString()
    })

    this.errorLog.push({
      error,
      context,
      handled: false
    })

    // 保持错误日志大小在合理范围内
    if (this.errorLog.length > 100) {
      this.errorLog = this.errorLog.slice(-50)
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const recent = this.errorLog.filter(log => 
      Date.now() - log.context.timestamp < 3600000 // 最近1小时
    )

    const errorTypes = recent.reduce((acc, log) => {
      const type = this.classifyError(log.error)
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {} as Record<ErrorType, number>)

    return {
      totalErrors: this.errorLog.length,
      recentErrors: recent.length,
      errorTypes,
      lastError: this.errorLog[this.errorLog.length - 1]
    }
  }

  /**
   * 清空错误日志
   */
  clearErrorLog() {
    this.errorLog = []
    console.log('🧹 错误日志已清空')
  }
}

// 创建单例实例
export const errorHandler = ErrorHandler.getInstance()

// 便捷的错误处理函数
export const handleApiError = async <T>(
  operation: string,
  apiCall: () => Promise<T>,
  options?: {
    fallbackData?: T
    showNotification?: boolean
    component?: string
  }
): Promise<T | null> => {
  try {
    return await apiCall()
  } catch (error) {
    return await errorHandler.handleError(
      operation,
      error as Error,
      { component: options?.component },
      {
        showNotification: options?.showNotification ?? true,
        useFallback: true
      }
    )
  }
}

export default errorHandler