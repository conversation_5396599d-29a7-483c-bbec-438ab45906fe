/**
 * AI调用优化器
 * 智能管理AI API调用，提高并发处理能力和稳定性
 */

class AICallOptimizer {
  constructor() {
    this.config = {
      // 动态并发控制
      maxConcurrent: {
        peak: 2,      // 高峰期
        normal: 5,    // 正常时期
        low: 8        // 低峰期
      },
      
      // 队列管理
      maxQueueSize: 100,
      queueTimeout: 60000, // 队列超时时间
      
      // 重试策略
      maxRetries: 3,
      retryDelay: 1000,
      backoffMultiplier: 2,
      
      // 降级策略
      enableFallback: true,
      fallbackProviders: ['openai', 'baidu'],
      
      // 缓存策略
      enableCache: true,
      cacheExpiry: 30 * 60 * 1000, // 30分钟
      
      // 监控配置
      enableMonitoring: true,
      alertThresholds: {
        queueSize: 50,
        errorRate: 0.1,
        avgResponseTime: 10000
      }
    };
    
    // 调用队列
    this.callQueue = [];
    this.processingQueue = [];
    
    // 统计信息
    this.stats = {
      totalCalls: 0,
      successCalls: 0,
      failedCalls: 0,
      avgResponseTime: 0,
      currentConcurrent: 0,
      queueSize: 0
    };
    
    // 缓存
    this.cache = new Map();
    
    // 启动队列处理
    this.startQueueProcessor();
    
    // 启动监控
    if (this.config.enableMonitoring) {
      this.startMonitoring();
    }
  }

  /**
   * 优化的AI调用接口
   */
  async callAI(params) {
    const callId = this.generateCallId();
    const startTime = Date.now();
    
    try {
      // 检查缓存
      if (this.config.enableCache) {
        const cached = this.getCachedResult(params);
        if (cached) {
          this.updateStats('cache_hit', Date.now() - startTime);
          return cached;
        }
      }
      
      // 添加到队列
      const result = await this.addToQueue(callId, params);
      
      // 缓存结果
      if (this.config.enableCache && result.success) {
        this.cacheResult(params, result);
      }
      
      this.updateStats('success', Date.now() - startTime);
      return result;
      
    } catch (error) {
      this.updateStats('error', Date.now() - startTime);
      throw error;
    }
  }

  /**
   * 添加到调用队列
   */
  async addToQueue(callId, params) {
    return new Promise((resolve, reject) => {
      // 检查队列大小
      if (this.callQueue.length >= this.config.maxQueueSize) {
        reject(new Error('AI调用队列已满，请稍后重试'));
        return;
      }
      
      const queueItem = {
        id: callId,
        params,
        resolve,
        reject,
        addedAt: Date.now(),
        retryCount: 0
      };
      
      this.callQueue.push(queueItem);
      this.stats.queueSize = this.callQueue.length;
      
      // 设置超时
      setTimeout(() => {
        if (queueItem.resolve) {
          queueItem.reject(new Error('AI调用队列超时'));
          this.removeFromQueue(callId);
        }
      }, this.config.queueTimeout);
    });
  }

  /**
   * 启动队列处理器
   */
  startQueueProcessor() {
    setInterval(() => {
      this.processQueue();
    }, 1000); // 每秒处理一次队列
  }

  /**
   * 处理队列
   */
  async processQueue() {
    if (this.callQueue.length === 0) return;
    
    const currentConcurrent = this.getCurrentConcurrentLimit();
    const availableSlots = currentConcurrent - this.processingQueue.length;
    
    if (availableSlots <= 0) return;
    
    // 取出可处理的任务
    const tasksToProcess = this.callQueue.splice(0, availableSlots);
    
    tasksToProcess.forEach(task => {
      this.processingQueue.push(task);
      this.processAICall(task);
    });
    
    this.stats.queueSize = this.callQueue.length;
    this.stats.currentConcurrent = this.processingQueue.length;
  }

  /**
   * 处理单个AI调用
   */
  async processAICall(task) {
    try {
      const result = await this.executeAICall(task.params);
      
      if (task.resolve) {
        task.resolve(result);
        task.resolve = null; // 防止重复调用
      }
      
    } catch (error) {
      // 重试逻辑
      if (task.retryCount < this.config.maxRetries) {
        task.retryCount++;
        const delay = this.config.retryDelay * Math.pow(this.config.backoffMultiplier, task.retryCount - 1);
        
        setTimeout(() => {
          this.callQueue.unshift(task); // 重新加入队列头部
        }, delay);
        
      } else {
        // 尝试降级
        if (this.config.enableFallback) {
          try {
            const fallbackResult = await this.tryFallbackProviders(task.params);
            if (task.resolve) {
              task.resolve(fallbackResult);
              task.resolve = null;
            }
          } catch (fallbackError) {
            if (task.reject) {
              task.reject(fallbackError);
              task.reject = null;
            }
          }
        } else {
          if (task.reject) {
            task.reject(error);
            task.reject = null;
          }
        }
      }
    } finally {
      // 从处理队列中移除
      this.removeFromProcessingQueue(task.id);
    }
  }

  /**
   * 执行AI调用
   */
  async executeAICall(params) {
    this.stats.totalCalls++;
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'callDoubaoAPI',
        data: params
      });
      
      if (result.result && result.result.success) {
        this.stats.successCalls++;
        return result.result;
      } else {
        throw new Error(result.result?.message || 'AI调用失败');
      }
      
    } catch (error) {
      this.stats.failedCalls++;
      throw error;
    }
  }

  /**
   * 尝试降级供应商
   */
  async tryFallbackProviders(params) {
    for (const provider of this.config.fallbackProviders) {
      try {
        console.log(`尝试降级到 ${provider} 供应商`);
        
        // 这里需要根据实际情况实现不同供应商的调用
        const result = await this.callFallbackProvider(provider, params);
        
        if (result && result.success) {
          return result;
        }
        
      } catch (error) {
        console.warn(`降级供应商 ${provider} 调用失败:`, error);
        continue;
      }
    }
    
    throw new Error('所有AI供应商都不可用');
  }

  /**
   * 调用降级供应商
   */
  async callFallbackProvider(provider, params) {
    // 简化实现，实际项目中需要实现具体的供应商调用逻辑
    switch (provider) {
      case 'openai':
        return await this.callOpenAI(params);
      case 'baidu':
        return await this.callBaiduAI(params);
      default:
        throw new Error(`不支持的供应商: ${provider}`);
    }
  }

  /**
   * OpenAI调用（示例）
   */
  async callOpenAI(params) {
    // 这里需要实现OpenAI的具体调用逻辑
    console.log('调用OpenAI API（示例实现）');
    return {
      success: true,
      data: {
        content: '这是OpenAI生成的示例评语',
        provider: 'openai'
      }
    };
  }

  /**
   * 百度AI调用（示例）
   */
  async callBaiduAI(params) {
    // 这里需要实现百度AI的具体调用逻辑
    console.log('调用百度AI API（示例实现）');
    return {
      success: true,
      data: {
        content: '这是百度AI生成的示例评语',
        provider: 'baidu'
      }
    };
  }

  /**
   * 获取当前并发限制
   */
  getCurrentConcurrentLimit() {
    const hour = new Date().getHours();
    
    // 高峰期：9-11点，14-16点，19-21点
    if ((hour >= 9 && hour <= 11) || (hour >= 14 && hour <= 16) || (hour >= 19 && hour <= 21)) {
      return this.config.maxConcurrent.peak;
    }
    
    // 低峰期：0-6点，22-24点
    if (hour <= 6 || hour >= 22) {
      return this.config.maxConcurrent.low;
    }
    
    // 正常时期
    return this.config.maxConcurrent.normal;
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(params) {
    const key = JSON.stringify({
      studentName: params.studentName,
      performanceMaterial: params.performanceMaterial,
      style: params.style,
      length: params.length
    });
    
    // 简单哈希
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
      const char = key.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return `ai_cache_${Math.abs(hash)}`;
  }

  /**
   * 获取缓存结果
   */
  getCachedResult(params) {
    const cacheKey = this.generateCacheKey(params);
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.config.cacheExpiry) {
      return cached.result;
    }
    
    if (cached) {
      this.cache.delete(cacheKey); // 清理过期缓存
    }
    
    return null;
  }

  /**
   * 缓存结果
   */
  cacheResult(params, result) {
    const cacheKey = this.generateCacheKey(params);
    
    this.cache.set(cacheKey, {
      result,
      timestamp: Date.now()
    });
    
    // 限制缓存大小
    if (this.cache.size > 1000) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  /**
   * 从队列中移除
   */
  removeFromQueue(callId) {
    this.callQueue = this.callQueue.filter(item => item.id !== callId);
    this.stats.queueSize = this.callQueue.length;
  }

  /**
   * 从处理队列中移除
   */
  removeFromProcessingQueue(callId) {
    this.processingQueue = this.processingQueue.filter(item => item.id !== callId);
    this.stats.currentConcurrent = this.processingQueue.length;
  }

  /**
   * 生成调用ID
   */
  generateCallId() {
    return `ai_call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 更新统计信息
   */
  updateStats(type, responseTime) {
    switch (type) {
      case 'success':
        this.stats.successCalls++;
        break;
      case 'error':
        this.stats.failedCalls++;
        break;
      case 'cache_hit':
        // 缓存命中不计入总调用数
        break;
    }
    
    // 更新平均响应时间
    if (responseTime) {
      const totalResponseTime = this.stats.avgResponseTime * (this.stats.totalCalls - 1) + responseTime;
      this.stats.avgResponseTime = Math.round(totalResponseTime / this.stats.totalCalls);
    }
  }

  /**
   * 启动监控
   */
  startMonitoring() {
    setInterval(() => {
      this.checkAlerts();
      this.logStats();
    }, 30000); // 每30秒检查一次
  }

  /**
   * 检查告警
   */
  checkAlerts() {
    const { alertThresholds } = this.config;
    
    // 队列大小告警
    if (this.stats.queueSize > alertThresholds.queueSize) {
      console.warn(`⚠️ AI调用队列过大: ${this.stats.queueSize}`);
    }
    
    // 错误率告警
    const errorRate = this.stats.totalCalls > 0 ? this.stats.failedCalls / this.stats.totalCalls : 0;
    if (errorRate > alertThresholds.errorRate) {
      console.warn(`⚠️ AI调用错误率过高: ${Math.round(errorRate * 100)}%`);
    }
    
    // 响应时间告警
    if (this.stats.avgResponseTime > alertThresholds.avgResponseTime) {
      console.warn(`⚠️ AI调用响应时间过长: ${this.stats.avgResponseTime}ms`);
    }
  }

  /**
   * 记录统计信息
   */
  logStats() {
    console.log('AI调用统计:', {
      总调用数: this.stats.totalCalls,
      成功率: this.stats.totalCalls > 0 ? Math.round((this.stats.successCalls / this.stats.totalCalls) * 100) + '%' : '0%',
      平均响应时间: this.stats.avgResponseTime + 'ms',
      当前并发: this.stats.currentConcurrent,
      队列大小: this.stats.queueSize,
      缓存大小: this.cache.size
    });
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      errorRate: this.stats.totalCalls > 0 ? this.stats.failedCalls / this.stats.totalCalls : 0,
      successRate: this.stats.totalCalls > 0 ? this.stats.successCalls / this.stats.totalCalls : 0,
      cacheSize: this.cache.size,
      currentConcurrentLimit: this.getCurrentConcurrentLimit()
    };
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.cache.clear();
    this.callQueue.length = 0;
    this.processingQueue.length = 0;
  }
}

// 创建全局AI调用优化器实例
const aiCallOptimizer = new AICallOptimizer();

module.exports = {
  AICallOptimizer,
  aiCallOptimizer
};
