# 依赖文件
node_modules/
.pnp
.pnp.js
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 环境变量文件 - 敏感信息
.env
.env.local
.env.*.local
.env.development.local
.env.test.local
.env.production.local

# 微信小程序相关
project.private.config.json

# 构建输出
dist/
build/
<<<<<<< HEAD
*.tsbuildinfo

# 环境变量文件 - 敏感信息
.env.local
.env.*.local

# 开发工具
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
*.lcov
.nyc_output

# 依赖管理
.npm
.eslintcache
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# 微信开发工具
.wechat_devtools/

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# 编辑器和IDE
*.sublime-project
*.sublime-workspace
.vscode/
.idea/
*.iml
*.ipr
*.iws

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 测试文件
.jest/
test-results/
playwright-report/
test-results/

# Storybook
storybook-static/

# Bundle 分析
stats.html
bundle-analyzer-report.html

# 生产环境调试文件
*.map

# Next.js
.next/
out/

# 缓存文件
.cache/
.parcel-cache/

# 备份文件
*.backup
*_backup.*
*.bak

# AI工具相关
.claude/
.promptx/

# 管理后台构建文件
admin-v2/build/
admin-v2/dist/

# 云函数配置文件（包含敏感信息）
cloudfunctions/*/config.json
cloudfunctions/*/cloudbaserc.json

# 数据库备份
*.sql
*.db
*.sqlite

# 图片缓存
*.cache

# 开发工具配置
.husky/
.prettierrc.js
.eslintrc.js

# 文档草稿
docs/draft/
*.draft.md

# 测试文件
test-*.md
test-*.html
test-*.js

# 紧急修复脚本
emergency-*.js
fix-*.js
immediate-*.js

# 手动文件
手动*.json
新建*.html
新版*.md

# 参考文件
参考*/
