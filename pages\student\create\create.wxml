<!--
  学生管理页面 - 支持单个添加和批量导入
-->
<view class="student-create-page">
  <!-- 页面标题区域 - 首页风格 -->
  <view class="page-header-section">
    <view class="header-content">
      <view class="page-title">
        <text class="title-main">学生管理</text>
        <text class="title-sub">添加、修改学生信息</text>
      </view>
    </view>
  </view>

  <!-- 顶部模式切换 -->
  <view class="mode-switch-section">
    <view class="mode-tabs">
      <view
        class="mode-tab {{mode === 'single' ? 'active' : ''}}"
        data-mode="single"
        bindtap="switchMode"
      >
        <van-icon name="user-o" size="20px" />
        <text class="tab-text">单个添加</text>
      </view>
      <view
        class="mode-tab {{mode === 'batch' ? 'active' : ''}}"
        data-mode="batch"
        bindtap="switchMode"
      >
        <van-icon name="friends-o" size="20px" />
        <text class="tab-text">批量导入</text>
      </view>
    </view>
  </view>

  <!-- 单个添加模式 -->
  <view wx:if="{{mode === 'single'}}" class="single-mode">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">学生信息</view>

      <!-- 学生信息表单 - 按照设计图布局 -->
      <view class="student-form-grid">
        <!-- 第一行：姓名 + 学号 -->
        <view class="form-row">
          <view class="form-field">
            <view class="field-label">
              <text class="label-text">姓名</text>
              <text class="required">*</text>
            </view>
            <view class="field-input">
              <input
                class="input-box"
                value="{{formData.name}}"
                placeholder=""
                maxlength="20"
                bindinput="onNameChange"
              />
            </view>
          </view>

          <view class="form-field">
            <view class="field-label">
              <text class="label-text">学号</text>
              <text class="required">*</text>
            </view>
            <view class="field-input">
              <input
                class="input-box"
                value="{{formData.studentNumber}}"
                placeholder=""
                maxlength="20"
                bindinput="onStudentNumberChange"
              />
            </view>
          </view>
        </view>

        <!-- 第二行：班级 + 性别 -->
        <view class="form-row">
          <view class="form-field">
            <view class="field-label">
              <text class="label-text">班级</text>
              <text class="required">*</text>
            </view>
            <view class="field-input">
              <input
                class="input-box"
                value="{{formData.className}}"
                placeholder=""
                maxlength="20"
                bindinput="onClassNameChange"
              />
            </view>
          </view>

          <view class="form-field">
            <view class="field-label">
              <text class="label-text">性别</text>
            </view>
            <view class="field-input">
              <view class="select-box" catchtap="showGenderPicker">
                <view class="select-content">
                  <text class="select-text {{!formData.genderText ? 'placeholder' : ''}}">
                    {{formData.genderText || '请选择性别'}}
                  </text>
                </view>
                <view class="select-icon">
                  <van-icon name="arrow-down" size="14px" color="#999" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 温馨提示 -->
      <view class="tips-section">
        <view class="tips-title">
          <van-icon name="info-o" size="16px" color="#4080FF" />
          <text class="tips-title-text">温馨提示</text>
        </view>
        <view class="tips-content">
          <view class="tip-item">
            <text class="tip-text">• 带 * 号的字段为必填项，请务必填写完整</text>
          </view>
          <view class="tip-item">
            <text class="tip-text">• 学号在系统中必须唯一，不能重复</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 批量导入模式 -->
  <view wx:if="{{mode === 'batch'}}" class="batch-mode">
    <!-- 导入说明 -->
    <view class="import-guide">
      <view class="guide-title">
        <van-icon name="info-o" size="20px" color="#4080FF" />
        <text class="title-text">批量导入说明</text>
      </view>
      <view class="guide-content">
        <view class="guide-item">1. 请先下载模板文件，按照格式填写学生信息</view>
        <view class="guide-item">2. 支持Excel (.xlsx) 和 CSV (.csv) 格式</view>
        <view class="guide-item">3. 必填字段：姓名、学号、班级</view>
        <view class="guide-item">4. 可选字段：性别、联系电话、备注</view>
        <view class="guide-item">5. 单次最多导入100名学生</view>
      </view>
    </view>

    <!-- 模板下载 -->
    <view class="template-section">
      <view class="section-title">模板下载</view>

      <!-- 模板下载按钮 -->
      <view class="single-template-card" bindtap="downloadTemplate">
        <view class="template-icon">
          <van-icon name="description" size="28px" color="#4080FF" />
        </view>
        <view class="template-info">
          <view class="template-name">
            <van-icon name="download" size="16px" color="#4080FF" style="margin-right: 8rpx;" />
            下载导入模板
          </view>
          <view class="template-desc">支持Excel和CSV格式，包含示例数据</view>
        </view>
        <view class="download-btn">
          <van-icon name="arrow" size="18px" color="#4080FF" />
        </view>
      </view>
    </view>

    <!-- 文件上传 -->
    <view class="upload-section">
      <view class="section-title">上传文件</view>
      <view class="upload-area" bindtap="chooseFile">
        <view wx:if="{{!uploadedFile}}" class="upload-placeholder">
          <van-icon name="upload" size="32px" color="#4080FF" />
          <view class="upload-text">点击上传文件</view>
          <view class="upload-hint">支持Excel(.xlsx)和CSV(.csv)格式，最大5MB</view>
        </view>

        <view wx:else class="uploaded-file">
          <view class="file-icon">
            <van-icon name="description" size="24px" color="#4080FF" />
          </view>
          <view class="file-info">
            <view class="file-name">{{uploadedFile.name}}</view>
            <view class="file-size">{{uploadedFile.size}}</view>
          </view>
          <view class="file-actions">
            <view class="action-btn" bindtap="previewFile">
              <van-icon name="eye-o" size="16px" color="#4080FF" />
            </view>
            <view class="action-btn" bindtap="removeFile">
              <van-icon name="delete-o" size="16px" color="#FF5247" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 预览数据 - 只在批量导入模式且文件上传成功后显示 -->
    <view wx:if="{{mode === 'batch' && uploadedFile && previewData.length > 0}}" class="preview-section">
      <view class="section-title">
        <text>数据预览</text>
        <text class="preview-count">共{{previewData.length}}条记录</text>
      </view>

      <view class="preview-table">
        <view class="table-header">
          <view class="table-cell">姓名</view>
          <view class="table-cell">学号</view>
          <view class="table-cell">班级</view>
          <view class="table-cell">性别</view>
          <view class="table-cell">电话</view>
          <view class="table-cell">状态</view>
        </view>

        <scroll-view class="table-body" scroll-y="{{true}}" style="height: 300rpx;">
          <view
            wx:for="{{previewData}}"
            wx:key="index"
            class="table-row {{item.hasError ? 'error' : ''}}"
          >
            <view class="table-cell">{{item.name}}</view>
            <view class="table-cell">{{item.studentNumber}}</view>
            <view class="table-cell">{{item.className}}</view>
            <view class="table-cell">{{item.gender}}</view>
            <view class="table-cell">{{item.phone}}</view>
            <view class="table-cell">
              <van-tag
                type="{{item.hasError ? 'danger' : 'success'}}"
                size="small"
              >
                {{item.hasError ? '错误' : '正常'}}
              </van-tag>
            </view>
          </view>
        </scroll-view>
      </view>

      <view wx:if="{{errorCount > 0}}" class="error-summary">
        <van-icon name="warning-o" size="16px" color="#FF5247" />
        <text class="error-text">发现 {{errorCount}} 条错误记录，请检查后重新上传</text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="btn btn-cancel" bindtap="onCancel">取消</view>

    <view
      wx:if="{{mode === 'single'}}"
      class="btn btn-save {{submitting ? 'disabled' : ''}}"
      bindtap="onSubmit"
    >
      {{submitting ? '保存中...' : (isEdit ? '保存' : '添加学生')}}
    </view>

    <view
      wx:if="{{mode === 'batch'}}"
      class="btn btn-save {{(importing || previewData.length === 0 || errorCount > 0) ? 'disabled' : ''}}"
      bindtap="onBatchImport"
    >
      {{importing ? '导入中...' : '导入学生 (' + (validCount || 0) + ')'}}
    </view>
  </view>





  <!-- 文件预览弹窗 - 只在批量导入模式下显示 -->
  <van-popup
    wx:if="{{mode === 'batch'}}"
    show="{{showFilePreview}}"
    position="bottom"
    custom-style="height: 80%"
    bind:close="hideFilePreview"
  >
    <view class="file-preview-popup">
      <view class="popup-header">
        <view class="popup-title">文件预览</view>
        <van-icon name="cross" size="18px" color="#999" bindtap="hideFilePreview" />
      </view>

      <view class="preview-content">
        <view class="preview-stats">
          <view class="stat-item">
            <view class="stat-number">{{previewData.length}}</view>
            <view class="stat-label">总记录</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{validCount}}</view>
            <view class="stat-label">有效记录</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{errorCount}}</view>
            <view class="stat-label">错误记录</view>
          </view>
        </view>

        <view class="preview-table-full">
          <view class="table-header">
            <view class="table-cell">序号</view>
            <view class="table-cell">姓名</view>
            <view class="table-cell">学号</view>
            <view class="table-cell">班级</view>
            <view class="table-cell">性别</view>
            <view class="table-cell">电话</view>
            <view class="table-cell">备注</view>
            <view class="table-cell">状态</view>
          </view>

          <scroll-view class="table-body" scroll-y="{{true}}">
            <view
              wx:for="{{previewData}}"
              wx:key="index"
              class="table-row {{item.hasError ? 'error' : ''}}"
            >
              <view class="table-cell">{{index + 1}}</view>
              <view class="table-cell">{{item.name}}</view>
              <view class="table-cell">{{item.studentNumber}}</view>
              <view class="table-cell">{{item.className}}</view>
              <view class="table-cell">{{item.gender}}</view>
              <view class="table-cell">{{item.phone}}</view>
              <view class="table-cell">{{item.remark}}</view>
              <view class="table-cell">
                <van-tag
                  type="{{item.hasError ? 'danger' : 'success'}}"
                  size="small"
                >
                  {{item.hasError ? '错误' : '正常'}}
                </van-tag>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- 导入结果弹窗 -->
  <van-dialog
    show="{{showImportResult}}"
    title="导入结果"
    message="{{importResultMessage}}"
    show-cancel-button="{{false}}"
    confirm-button-text="确定"
    bind:confirm="hideImportResult"
  />
</view>