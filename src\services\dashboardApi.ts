import cloudbaseService from '../utils/cloudbaseConfig'

// 仪表板统计数据接口
export interface DashboardStats {
  totalUsers: number
  todayComments: number
  aiCalls: number
  satisfaction: number
  lastUpdated: string
}

// 系统活动记录接口
export interface ActivityRecord {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  action: string
  actionType: 'comment_generate' | 'data_import' | 'config_update' | 'report_export' | 'batch_operation'
  timestamp: string
  metadata?: Record<string, any>
}

// 系统性能指标接口
export interface SystemMetrics {
  cpu: number
  memory: number
  storage: number
  apiResponseTime: number
  activeConnections: number
  timestamp: string
}

/**
 * 获取仪表板统计数据 - 使用云函数调用
 */
export const getDashboardStats = async (): Promise<DashboardStats> => {
  try {
    console.log('📊 获取仪表板统计数据 - 云函数调用')

    // 使用统一的云函数调用
    const result = await cloudbaseService.callFunction('dataQuery', {
      action: 'getDashboardStats'
    })
    
    if (result.code === 0 && result.data) {
      console.log('✅ 云函数仪表板统计数据:', result.data)
      return result.data
    } else {
      throw new Error(result.message || '云函数调用失败')
    }

  } catch (error) {
    console.error('❌ 云函数获取仪表板数据失败，尝试备用方案:', error)
    
    try {
      // 备用方案：使用数据桥接服务
      const dataBridge = getDataBridgeService()
      const stats = await dataBridge.getDashboardStats()
      console.log('✅ 备用方案获取到统计数据:', stats)
      return stats
    } catch (fallbackError) {
      console.error('备用方案也失败了:', fallbackError)
      
      // 返回默认空数据
      return {
        totalUsers: 0,
        todayComments: 0,
        aiCalls: 0,
        satisfaction: 0,
        lastUpdated: new Date().toISOString()
      }
    }
  }
}

/**
 * 获取最近活动记录 - 使用云函数调用
 */
export const getRecentActivities = async (limit = 10): Promise<ActivityRecord[]> => {
  try {
    console.log('📊 获取最近活动记录 - 云函数调用')

    // 优先使用云函数调用
    const result = await cloudFunctionService.getRealtimeData()
    
    if (result.code === 0 && result.data?.activities) {
      const activities = result.data.activities.slice(0, limit)
      console.log(`✅ 云函数获取到 ${activities.length} 条活动记录`)
      return activities
    } else {
      throw new Error(result.message || '云函数调用失败')
    }

  } catch (error) {
    console.error('❌ 云函数获取活动记录失败，尝试备用方案:', error)
    
    try {
      // 备用方案：使用数据桥接服务
      const dataBridge = getDataBridgeService()
      const activities = await dataBridge.getRecentActivities(limit)
      console.log(`✅ 备用方案获取到 ${activities.length} 条活动记录`)
      return activities
    } catch (fallbackError) {
      console.error('备用方案也失败了:', fallbackError)
      return []
    }
  }
}

/**
 * 获取系统性能指标 - 从真实小程序云环境获取
 */
export const getSystemMetrics = async (): Promise<SystemMetrics> => {
  try {
    console.log('📊 获取系统性能指标 - 连接小程序云环境数据')

    const dataBridge = getDataBridgeService()
    const metrics = await dataBridge.getSystemMetrics()

    console.log('✅ 真实系统性能指标:', metrics)
    return metrics
  } catch (error) {
    console.error('获取系统指标失败:', error)
    
    // 返回默认数据
    return {
      cpu: 0,
      memory: 0,
      storage: 0,
      apiResponseTime: 0,
      activeConnections: 0,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * 获取用户统计数据 - 使用云函数调用
 */
export const getUserStats = async () => {
  try {
    const result = await cloudFunctionService.getUsers(1, 1) // 只获取统计信息
    if (result.code === 0 && result.data) {
      return {
        totalUsers: result.data.total || 0,
        activeUsers: result.data.active || 0,
        newUsersToday: result.data.newToday || 0,
        userGrowthRate: result.data.growthRate || 0
      }
    }
    throw new Error(result.message || '获取用户统计失败')
  } catch (error) {
    console.error('❌ 云函数获取用户统计失败:', error)
    return {
      totalUsers: 0,
      activeUsers: 0,
      newUsersToday: 0,
      userGrowthRate: 0
    }
  }
}

/**
 * 获取评语生成统计 - 使用云函数调用
 */
export const getCommentStats = async () => {
  try {
    const result = await cloudFunctionService.getComments(1, 1) // 只获取统计信息
    if (result.code === 0 && result.data) {
      return {
        totalComments: result.data.total || 0,
        todayComments: result.data.todayCount || 0,
        averageLength: result.data.averageLength || 0,
        popularTemplates: result.data.popularTemplates || []
      }
    }
    throw new Error(result.message || '获取评语统计失败')
  } catch (error) {
    console.error('❌ 云函数获取评语统计失败:', error)
    return {
      totalComments: 0,
      todayComments: 0,
      averageLength: 0,
      popularTemplates: []
    }
  }
}

/**
 * 获取AI调用统计 - 使用云函数调用
 */
export const getAIStats = async () => {
  try {
    const result = await cloudFunctionService.getAIUsageStats('7d')
    if (result.code === 0 && result.data) {
      return {
        totalCalls: result.data.totalCalls || 0,
        todayCalls: result.data.todayCalls || 0,
        averageResponseTime: result.data.averageResponseTime || 0,
        successRate: result.data.successRate || 0,
        tokenUsage: result.data.tokenUsage || 0
      }
    }
    throw new Error(result.message || '获取AI统计失败')
  } catch (error) {
    console.error('❌ 云函数获取AI统计失败:', error)
    return {
      totalCalls: 0,
      todayCalls: 0,
      averageResponseTime: 0,
      successRate: 0,
      tokenUsage: 0
    }
  }
}

/**
 * 获取系统健康状态 - 使用云函数调用
 */
export const getSystemHealth = async () => {
  try {
    const result = await cloudFunctionService.getSystemConfig()
    if (result.code === 0 && result.data) {
      return {
        status: result.data.status || 'healthy',
        uptime: result.data.uptime || 0,
        version: result.data.version || '1.0.0',
        environment: result.data.environment || 'production',
        lastCheck: new Date().toISOString()
      }
    }
    throw new Error(result.message || '获取系统健康状态失败')
  } catch (error) {
    console.error('❌ 云函数获取系统健康状态失败:', error)
    return {
      status: 'unknown',
      uptime: 0,
      version: '1.0.0',
      environment: 'development',
      lastCheck: new Date().toISOString()
    }
  }
}

/**
 * 获取实时在线用户数 - 使用云函数调用
 */
export const getOnlineUsers = async () => {
  try {
    const result = await cloudFunctionService.getRealtimeData()
    if (result.code === 0 && result.data?.onlineUsers) {
      return {
        count: result.data.onlineUsers.count || 0,
        users: result.data.onlineUsers.users || []
      }
    }
    throw new Error(result.message || '获取在线用户失败')
  } catch (error) {
    console.error('❌ 云函数获取在线用户失败:', error)
    return {
      count: 0,
      users: []
    }
  }
}

/**
 * 导出仪表板数据 - 使用云函数调用
 */
export const exportDashboardData = async (format: 'excel' | 'csv' = 'excel') => {
  try {
    const result = await cloudFunctionService.exportData('dashboard', { format })
    
    if (result.code === 0 && result.data?.downloadUrl) {
      // 创建下载链接
      const link = document.createElement('a')
      link.href = result.data.downloadUrl
      link.setAttribute('download', `dashboard-data-${new Date().toISOString().split('T')[0]}.${format}`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      return true
    }
    
    throw new Error(result.message || '导出数据失败')
  } catch (error) {
    console.error('❌ 云函数导出数据失败:', error)
    return false
  }
}

// 实用工具函数
export const formatActivityAction = (activity: ActivityRecord): string => {
  const actionMap = {
    comment_generate: '生成了评语',
    data_import: '导入了数据',
    config_update: '更新了配置',
    report_export: '导出了报告',
    batch_operation: '执行了批量操作'
  }
  
  return actionMap[activity.actionType] || activity.action
}

export const getActivityIcon = (actionType: ActivityRecord['actionType']) => {
  const iconMap = {
    comment_generate: 'MessageOutlined',
    data_import: 'ImportOutlined',
    config_update: 'SettingOutlined',
    report_export: 'ExportOutlined',
    batch_operation: 'ThunderboltOutlined'
  }
  
  return iconMap[actionType] || 'InfoCircleOutlined'
}

export const getActivityColor = (actionType: ActivityRecord['actionType']) => {
  const colorMap = {
    comment_generate: 'bg-blue-500',
    data_import: 'bg-green-500',
    config_update: 'bg-purple-500',
    report_export: 'bg-orange-500',
    batch_operation: 'bg-pink-500'
  }
  
  return colorMap[actionType] || 'bg-gray-500'
}

export default {
  getDashboardStats,
  getRecentActivities,
  getSystemMetrics,
  getUserStats,
  getCommentStats,
  getAIStats,
  getSystemHealth,
  getOnlineUsers,
  exportDashboardData,
  formatActivityAction,
  getActivityIcon,
  getActivityColor
}