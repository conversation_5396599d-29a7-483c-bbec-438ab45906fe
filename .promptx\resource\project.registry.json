{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-28T13:07:42.403Z", "updatedAt": "2025-07-28T13:07:42.414Z", "resourceCount": 4}, "resources": [{"id": "ui-optimization-workflow", "source": "project", "protocol": "execution", "name": "Ui Optimization Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-frontend-designer/execution/ui-optimization-workflow.execution.md", "metadata": {"createdAt": "2025-07-28T13:07:42.408Z", "updatedAt": "2025-07-28T13:07:42.408Z", "scannedAt": "2025-07-28T13:07:42.408Z", "path": "role/ui-frontend-designer/execution/ui-optimization-workflow.execution.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-frontend-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-28T13:07:42.410Z", "updatedAt": "2025-07-28T13:07:42.410Z", "scannedAt": "2025-07-28T13:07:42.410Z", "path": "role/ui-frontend-designer/thought/design-thinking.thought.md"}}, {"id": "frontend-optimization", "source": "project", "protocol": "thought", "name": "Frontend Optimization 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-frontend-designer/thought/frontend-optimization.thought.md", "metadata": {"createdAt": "2025-07-28T13:07:42.411Z", "updatedAt": "2025-07-28T13:07:42.411Z", "scannedAt": "2025-07-28T13:07:42.411Z", "path": "role/ui-frontend-designer/thought/frontend-optimization.thought.md"}}, {"id": "ui-frontend-designer", "source": "project", "protocol": "role", "name": "Ui Frontend Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-frontend-designer/ui-frontend-designer.role.md", "metadata": {"createdAt": "2025-07-28T13:07:42.413Z", "updatedAt": "2025-07-28T13:07:42.413Z", "scannedAt": "2025-07-28T13:07:42.413Z", "path": "role/ui-frontend-designer/ui-frontend-designer.role.md"}}], "stats": {"totalResources": 4, "byProtocol": {"execution": 1, "thought": 2, "role": 1}, "bySource": {"project": 4}}}