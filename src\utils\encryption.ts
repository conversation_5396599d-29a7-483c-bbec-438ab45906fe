/**
 * 加密工具类
 * 用于安全存储敏感数据
 */

const ALGORITHM = 'AES-GCM'
const KEY_LENGTH = 256

export class EncryptionHelper {
  private static encoder = new TextEncoder()
  private static decoder = new TextDecoder()

  /**
   * 从环境变量生成加密密钥
   */
  private static async getKey(): Promise<CryptoKey> {
    const keyString = import.meta.env.VITE_ENCRYPTION_KEY
    if (!keyString) {
      throw new Error('ENCRYPTION_KEY not configured')
    }
    
    const keyBytes = new Uint8Array(32)
    const keyData = new TextEncoder().encode(keyString)
    keyBytes.set(keyData.slice(0, 32))
    
    return await crypto.subtle.importKey(
      'raw',
      keyBytes,
      { name: ALGORITHM },
      false,
      ['encrypt', 'decrypt']
    )
  }

  /**
   * 加密数据
   */
  static async encrypt(data: string): Promise<string> {
    try {
      const key = await this.getKey()
      const iv = crypto.getRandomValues(new Uint8Array(12))
      const encrypted = await crypto.subtle.encrypt(
        { name: ALGORITHM, iv },
        key,
        this.encoder.encode(data)
      )
      
      const combined = new Uint8Array(iv.length + encrypted.byteLength)
      combined.set(iv)
      combined.set(new Uint8Array(encrypted), iv.length)
      
      return btoa(String.fromCharCode(...combined))
    } catch (error) {
      console.error('Encryption failed:', error)
      throw new Error('Failed to encrypt data')
    }
  }

  /**
   * 解密数据
   */
  static async decrypt(encryptedData: string): Promise<string> {
    try {
      const key = await this.getKey()
      const combined = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      )
      
      const iv = combined.slice(0, 12)
      const data = combined.slice(12)
      
      const decrypted = await crypto.subtle.decrypt(
        { name: ALGORITHM, iv },
        key,
        data
      )
      
      return this.decoder.decode(decrypted)
    } catch (error) {
      console.error('Decryption failed:', error)
      throw new Error('Failed to decrypt data')
    }
  }

  /**
   * 安全哈希密码
   */
  static async hashPassword(password: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(password)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  /**
   * 验证密码
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    const passwordHash = await this.hashPassword(password)
    return passwordHash === hash
  }
}

/**
 * 安全存储工具
 */
export class SecureStorage {
  /**
   * 安全存储数据
   */
  static async setItem(key: string, value: string): Promise<void> {
    try {
      const encrypted = await EncryptionHelper.encrypt(value)
      localStorage.setItem(key, encrypted)
    } catch (error) {
      console.error('Failed to store data securely:', error)
      throw new Error('Failed to store data')
    }
  }

  /**
   * 安全读取数据
   */
  static async getItem(key: string): Promise<string | null> {
    try {
      const encrypted = localStorage.getItem(key)
      if (!encrypted) return null
      
      return await EncryptionHelper.decrypt(encrypted)
    } catch (error) {
      console.error('Failed to read data securely:', error)
      return null
    }
  }

  /**
   * 删除存储的数据
   */
  static removeItem(key: string): void {
    localStorage.removeItem(key)
  }

  /**
   * 清空所有存储
   */
  static clear(): void {
    localStorage.clear()
  }
}

// 导出名空间
export const Crypto = {
  encrypt: EncryptionHelper.encrypt.bind(EncryptionHelper),
  decrypt: EncryptionHelper.decrypt.bind(EncryptionHelper),
  hashPassword: EncryptionHelper.hashPassword.bind(EncryptionHelper),
  verifyPassword: EncryptionHelper.verifyPassword.bind(EncryptionHelper),
}

export const Secure = {
  setItem: SecureStorage.setItem.bind(SecureStorage),
  getItem: SecureStorage.getItem.bind(SecureStorage),
  removeItem: SecureStorage.removeItem.bind(SecureStorage),
  clear: SecureStorage.clear.bind(SecureStorage),
}