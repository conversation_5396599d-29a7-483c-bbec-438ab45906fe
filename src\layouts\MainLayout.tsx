import React, { useState, useEffect } from 'react'
import { Layout, Menu, Button, Dropdown, Avatar, Space, Typography, Badge, Tooltip, Input, Modal, List, notification } from 'antd'
import { 
  DashboardOutlined,
  RobotOutlined,
  DatabaseOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
  SearchOutlined,
  MoonOutlined,
  SunOutlined,
  FullscreenOutlined,
  HomeOutlined
} from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '../stores/authStore'
import { useThemeStore } from '../stores/themeStore'

const { Header, Sider, Content } = Layout
const { Text } = Typography

interface MainLayoutProps {
  children: React.ReactNode
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [notificationVisible, setNotificationVisible] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout } = useAuthStore()
  const { isDark, toggleTheme } = useThemeStore()

  useEffect(() => {
    // 只设置一次时间，停止每秒更新减少刷新
    setCurrentTime(new Date())
    console.log('⏱️ MainLayout时间停止更新，避免页面刷新')
    
    // const timer = setInterval(() => {
    //   setCurrentTime(new Date())
    // }, 1000)
    // return () => clearInterval(timer)
  }, [])


  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '智能大屏',
      onClick: () => navigate('/dashboard')
    },
    {
      key: '/ai-config',
      icon: <RobotOutlined />,
      label: 'AI配置',
      onClick: () => navigate('/ai-config')
    },
    {
      key: '/data-management',
      icon: <DatabaseOutlined />,
      label: '数据管理',
      onClick: () => navigate('/data-management')
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      onClick: () => navigate('/settings')
    }
  ]

  const handleLogout = () => {
    Modal.confirm({
      title: '确认退出',
      content: '您确定要退出当前账户吗？',
      okText: '确认退出',
      cancelText: '取消',
      onOk: () => {
        logout()
        notification.success({
          message: '退出成功',
          description: '您已安全退出系统'
        })
        navigate('/login', { replace: true })
      }
    })
  }

  const handleSearch = () => {
    // 如果在Dashboard页面，聚焦到搜索框
    if (location.pathname === '/dashboard') {
      const searchInput = document.querySelector('.dashboard-search input') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
        searchInput.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    } else {
      // 其他页面跳转到Dashboard
      navigate('/dashboard')
      setTimeout(() => {
        const searchInput = document.querySelector('.dashboard-search input') as HTMLInputElement
        if (searchInput) {
          searchInput.focus()
          searchInput.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }, 100)
    }
  }

  const handleNotification = () => {
    setNotificationVisible(true)
  }

  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        navigate('/settings?tab=profile')
        break
      case 'settings':
        navigate('/settings?tab=account')
        break
      case 'logout':
        handleLogout()
        break
      default:
        break
    }
  }

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置'
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '安全退出'
    }
  ]

  // 模拟通知数据
  const notifications = [
    {
      id: 1,
      title: '系统更新提醒',
      description: '系统将在今晚12点进行例行维护',
      time: '10分钟前',
      type: 'info'
    },
    {
      id: 2,
      title: 'AI配置变更',
      description: '李老师修改了AI模型参数配置',
      time: '1小时前',
      type: 'warning'
    },
    {
      id: 3,
      title: '数据备份完成',
      description: '今日数据备份已成功完成',
      time: '2小时前',
      type: 'success'
    }
  ]

  const getPageTitle = (pathname: string) => {
    const titles: Record<string, string> = {
      '/dashboard': '智能数据大屏',
      '/ai-config': 'AI模型配置',
      '/data-management': '数据管理中心',
      '/settings': '系统设置'
    }
    return titles[pathname] || '评语灵感君'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 transition-colors">
      {/* 现代化侧边栏 */}
      <div className={`fixed left-0 top-0 h-full z-50 transition-all duration-300 ${
        collapsed ? 'w-20' : 'w-72'
      }`}>
        <div className="h-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-r border-white/50 dark:border-gray-700/50 shadow-2xl transition-colors">
          {/* Logo区域 - 优化为高对比度紫色渐变背景 */}
          <div className="h-20 bg-gradient-to-r from-purple-600 to-indigo-700 px-4 flex items-center border-b border-white/10">
            {!collapsed ? (
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20 shrink-0">
                  <div className="text-white text-xl font-bold font-mono">评语</div>
                </div>
                <div className="min-w-0 flex-1">
                  <div className="text-xl font-black text-white truncate tracking-tight">评语灵感君</div>
                  <div className="text-sm text-white/80 font-medium">智能管理系统</div>
                </div>
              </div>
            ) : (
              <div className="flex justify-center">
                <div className="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center border border-white/20">
                  <div className="text-white text-xl font-bold font-mono">评语</div>
                </div>
              </div>
            )}
          </div>

          {/* 导航菜单 */}
          <div className="p-4">
            <div className="space-y-2">
              {menuItems.map((item) => {
                const isActive = location.pathname === item.key
                return (
                  <Tooltip title={collapsed ? item.label : ''} placement="right" key={item.key}>
                    <div
                      onClick={item.onClick}
                      className={`flex items-center space-x-4 px-4 py-3 rounded-2xl cursor-pointer transition-all duration-300 group ${
                        isActive
                          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105'
                          : 'hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 dark:hover:from-gray-700 dark:hover:to-gray-600 hover:shadow-md hover:transform hover:scale-102'
                      }`}
                    >
                      <div className={`text-xl ${isActive ? 'text-white' : 'text-gray-600 dark:text-gray-300 group-hover:text-blue-500'}`}>
                        {item.icon}
                      </div>
                      {!collapsed && (
                        <span className={`font-medium ${isActive ? 'text-white' : 'text-gray-700 dark:text-gray-200'}`}>
                          {item.label}
                        </span>
                      )}
                      {!collapsed && isActive && (
                        <div className="ml-auto w-2 h-2 bg-white rounded-full"></div>
                      )}
                    </div>
                  </Tooltip>
                )
              })}
            </div>
          </div>

          {/* 底部用户信息 */}
          {!collapsed && (
            <div className="absolute bottom-4 left-4 right-4">
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700 dark:to-gray-600 rounded-2xl p-4 border border-gray-100 dark:border-gray-600 transition-colors">
                <div className="flex items-center space-x-3">
                  <Avatar 
                    size={40} 
                    className="bg-gradient-to-r from-blue-500 to-purple-600" 
                    icon={<UserOutlined />} 
                  />
                  <div className="flex-1">
                    <div className="font-semibold text-gray-800 dark:text-gray-100 text-sm transition-colors">
                      {user?.name || '系统管理员'}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-300 transition-colors">
                      在线 • {currentTime.toLocaleTimeString()}
                    </div>
                  </div>
                  <Badge dot color="#52c41a" />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 主内容区域 */}
      <div className={`transition-all duration-300 ${collapsed ? 'ml-20' : 'ml-72'}`}>
        {/* 现代化头部 - 匹配侧边栏高度 */}
        <div className="h-20 bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-b border-white/50 dark:border-gray-700/50 shadow-lg sticky top-0 z-40 transition-colors">
          <div className="h-full flex items-center justify-between px-6">
            <div className="flex items-center space-x-6">
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                className="!w-12 !h-12 !rounded-xl hover:!bg-blue-50 hover:!text-blue-600 transition-all duration-300"
              />
              <div>
                <div className="nav-brand-title text-gray-800 dark:text-white transition-colors">
                  {getPageTitle(location.pathname)}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300 transition-colors">
                  {currentTime.toLocaleDateString('zh-CN', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric',
                    weekday: 'long' 
                  })}
                </div>
              </div>
            </div>

            <div className="flex items-center flex-grow max-w-lg mx-4">
              <Input.Search
                placeholder="全局搜索..."
                onSearch={handleSearch}
                className="w-full dashboard-search"
                size="large"
                enterButton={<Button icon={<SearchOutlined />} className="!border-none !bg-transparent"/>}
              />
            </div>
            
            <div className="flex items-center space-x-4">
              
              {/* 通知按钮 */}
              <Tooltip title="消息通知">
                <Badge count={notifications.length} size="small">
                  <Button
                    type="text"
                    icon={<BellOutlined />}
                    onClick={handleNotification}
                    className="!w-12 !h-12 !rounded-xl hover:!bg-blue-50 hover:!text-blue-600"
                  />
                </Badge>
              </Tooltip>

              {/* 全屏按钮 */}
              <Tooltip title="全屏显示">
                <Button
                  type="text"
                  icon={<FullscreenOutlined />}
                  className="!w-12 !h-12 !rounded-xl hover:!bg-blue-50 hover:!text-blue-600"
                  onClick={() => {
                    if (document.fullscreenElement) {
                      document.exitFullscreen()
                    } else {
                      document.documentElement.requestFullscreen()
                    }
                  }}
                />
              </Tooltip>

              {/* 主题切换 */}
              <Tooltip title={isDark ? '切换到亮色模式' : '切换到暗色模式'}>
                <Button
                  type="text"
                  icon={isDark ? <SunOutlined /> : <MoonOutlined />}
                  onClick={toggleTheme}
                  className="!w-12 !h-12 !rounded-xl hover:!bg-blue-50 hover:!text-blue-600 dark:hover:!bg-gray-700 dark:hover:!text-yellow-400"
                />
              </Tooltip>
              
              {/* 用户菜单 */}
              <Dropdown
                menu={{ items: userMenuItems, onClick: handleUserMenuClick }}
                placement="bottomRight"
                trigger={['click']}
              >
                <div className="flex items-center space-x-3 px-4 py-2 rounded-2xl hover:bg-blue-50 cursor-pointer transition-all duration-300">
                  <Avatar 
                    size={36} 
                    className="bg-gradient-to-r from-blue-500 to-purple-600" 
                    icon={<UserOutlined />} 
                  />
                  <div className="hidden md:block">
                    <div className="text-sm font-semibold text-gray-800">
                      {user?.name || '系统管理员'}
                    </div>
                    <div className="text-xs text-gray-500">超级管理员</div>
                  </div>
                </div>
              </Dropdown>
            </div>
          </div>
        </div>
        
        {/* 内容区域 */}
        <div className="min-h-[calc(100vh-5rem)]">
          {children}
        </div>
      </div>


      {/* 通知消息模态框 */}
      <Modal
        title="消息通知"
        open={notificationVisible}
        onCancel={() => setNotificationVisible(false)}
        footer={[
          <Button key="clear" type="text">
            清空全部
          </Button>,
          <Button key="close" onClick={() => setNotificationVisible(false)}>
            关闭
          </Button>
        ]}
        width={500}
        centered
      >
        <List
          itemLayout="horizontal"
          dataSource={notifications}
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    item.type === 'info' ? 'bg-blue-100 text-blue-600' :
                    item.type === 'warning' ? 'bg-orange-100 text-orange-600' :
                    'bg-green-100 text-green-600'
                  }`}>
                    <BellOutlined />
                  </div>
                }
                title={<span className="font-medium">{item.title}</span>}
                description={
                  <div>
                    <p className="text-gray-600 mb-1">{item.description}</p>
                    <p className="text-xs text-gray-400">{item.time}</p>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Modal>
    </div>
  )
}

export default MainLayout