/* 我的作品 - 专注评语管理 */
.morandi-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  padding: 40rpx 32rpx;
}

/* 顶部标题区域 */
.header-section {
  margin-bottom: 48rpx;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-left {
  flex: 1;
}

.page-title {
  font-size: 64rpx;
  font-weight: 700;
  color: #2C3E50;
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.1);
}

.page-subtitle {
  font-size: 28rpx;
  color: #606266;
  font-weight: 400;
  margin-bottom: 32rpx;
}

.title-right {
  margin-top: 16rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.clear-btn, .export-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2rpx solid transparent;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10rpx);
  min-width: 88rpx;
  justify-content: center;
}

.clear-btn {
  background: rgba(231, 76, 60, 0.08);
  border-color: rgba(231, 76, 60, 0.15);
}

.clear-btn:active {
  background: rgba(231, 76, 60, 0.15);
  transform: translateY(1rpx) scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(231, 76, 60, 0.2);
}

.clear-text {
  font-size: 26rpx;
  color: #e74c3c;
  margin-left: 8rpx;
  font-weight: 600;
}

.export-btn {
  background: rgba(84, 112, 198, 0.08);
  border-color: rgba(84, 112, 198, 0.15);
}

.export-btn:active {
  background: rgba(84, 112, 198, 0.15);
  transform: translateY(1rpx) scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(84, 112, 198, 0.2);
}

.export-text {
  font-size: 26rpx;
  color: #5470C6;
  margin-left: 8rpx;
  font-weight: 600;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 32rpx;
}

.search-container {
  margin-bottom: 24rpx;
}

.search-input {
  background: rgba(248, 249, 250, 0.8) !important;
  border-radius: 24rpx !important;
  border: 1rpx solid rgba(84, 112, 198, 0.1) !important;
}

.search-input:focus {
  border-color: #5470C6 !important;
  background: rgba(84, 112, 198, 0.05) !important;
}

.filter-container {
  display: flex;
  gap: 20rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 20rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #606266;
  border: 1rpx solid rgba(84, 112, 198, 0.1);
  transition: all 0.3s ease;
}

.filter-item:active {
  background: rgba(84, 112, 198, 0.1);
  border-color: #5470C6;
  transform: scale(0.95);
}

.filter-item.active {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  color: white;
  border-color: #5470C6;
}

/* 统计信息 */
.stats-section {
  padding: 32rpx;
}

.stats-card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-number {
  font-size: 72rpx;
  font-weight: 600;
  color: #5470C6;
  margin-bottom: 12rpx;
  line-height: 1;
  text-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.15);
}

.stats-label {
  font-size: 26rpx;
  color: #606266;
  font-weight: 500;
  letter-spacing: 1rpx;
}

.stats-divider {
  width: 1rpx;
  height: 80rpx;
  background: rgba(84, 112, 198, 0.2);
  margin: 0 20rpx;
  align-self: center;
}

/* 评语列表区域 */
.list-section {
  padding: 0 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 32rpx;
  text-align: left;
  letter-spacing: 1rpx;
}

.comment-items {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.comment-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(84, 112, 198, 0.08);
  border: 1rpx solid rgba(84, 112, 198, 0.06);
  backdrop-filter: blur(20rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.comment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #5470C6 0%, #91CC75 50%, #FAC858 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.comment-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(84, 112, 198, 0.15);
}

.comment-card:active::before {
  opacity: 1;
}

.comment-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.student-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.student-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 24rpx;
}

.student-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.student-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
}

.student-class {
  font-size: 24rpx;
  color: #909399;
  font-weight: 400;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 24rpx;
  color: #909399;
}

.comment-content {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 20rpx 0;
  border: 1rpx solid rgba(84, 112, 198, 0.05);
}

.comment-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: #2C3E50;
  font-weight: 400;
}

.comment-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
}

.comment-tags {
  display: flex;
  gap: 12rpx;
}

.comment-tag {
  padding: 8rpx 16rpx;
  background: rgba(84, 112, 198, 0.1);
  color: #5470C6;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.comment-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.edit-btn {
  background: rgba(145, 204, 117, 0.1);
  color: #91CC75;
  border: 1rpx solid rgba(145, 204, 117, 0.2);
}

.edit-btn:active {
  background: rgba(145, 204, 117, 0.2);
  transform: scale(0.95);
}

.delete-btn {
  background: rgba(238, 102, 102, 0.1);
  color: #EE6666;
  border: 1rpx solid rgba(238, 102, 102, 0.2);
}

.delete-btn:active {
  background: rgba(238, 102, 102, 0.2);
  transform: scale(0.95);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: white;
  border-radius: 20rpx;
  margin: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.empty-icon {
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

.empty-action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 32rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.empty-action-btn.primary {
  background: linear-gradient(135deg, #4080FF, #6B9FFF);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(64, 128, 255, 0.3);
}

.empty-action-btn.primary:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(64, 128, 255, 0.4);
}

.action-text {
  font-size: 28rpx;
}

/* 悬浮操作按钮 */
.fab-container {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 100;
}

.fab-button {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4080FF, #6B9FFF);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(64, 128, 255, 0.4);
  transition: all 0.3s ease;
}

.fab-button:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 16rpx rgba(64, 128, 255, 0.5);
}