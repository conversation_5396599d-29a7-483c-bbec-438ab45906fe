/**
 * 数据清理工具 - 清除所有测试数据
 * 确保生产环境中只有真实数据
 */

/**
 * 清理本地存储中的测试数据
 */
function clearLocalTestData() {
  try {

    // 清理可能的测试数据键
    const testDataKeys = [
      'testStudents',
      'testComments', 
      'testClasses',
      'testRecords',
      'mockData',
      'sampleData',
      'demoData',
      'debugData'
    ];
    
    testDataKeys.forEach(key => {
      try {
        wx.removeStorageSync(key);

      } catch (error) {

      }
    });
    
    // 清理包含测试标识的数据
    const allKeys = wx.getStorageInfoSync().keys || [];
    allKeys.forEach(key => {
      if (key.includes('test_') || key.includes('mock_') || key.includes('demo_')) {
        try {
          wx.removeStorageSync(key);

        } catch (error) {

        }
      }
    });

    return true;
  } catch (error) {
    console.error('❌ 本地数据清理失败:', error);
    return false;
  }
}

/**
 * 验证数据来源，确保没有测试数据
 */
function validateDataSource(data, dataType = 'unknown') {
  if (!data || !Array.isArray(data)) {
    return { isValid: true, testItems: [] };
  }
  
  const testItems = [];
  
  data.forEach((item, index) => {
    // 检查测试数据标识
    const testIndicators = [
      // ID标识
      item.id && (item.id.toString().startsWith('test_') || item.id.toString().startsWith('mock_')),
      // 姓名标识（常见测试姓名）
      item.name && ['张小明', '李小红', '王小刚', '陈小美', '刘小强', '赵小丽', '孙小军', '周小花', '吴小东', '郑小西'].includes(item.name),
      item.studentName && ['张小明', '李小红', '王小刚', '陈小美', '刘小强', '赵小丽', '孙小军', '周小花', '吴小东', '郑小西'].includes(item.studentName),
      // 班级标识
      item.className && item.className.match(/^(一|二|三|四|五|六)年级[1-9]班$/),
      // 学号标识
      item.studentNumber && item.studentNumber.match(/^00[0-9]$/),
      // 内容标识
      item.content && item.content.includes('同学在本学期表现优秀'),
      item.comment && item.comment.includes('同学在本学期表现优秀')
    ];
    
    if (testIndicators.some(indicator => indicator)) {
      testItems.push({
        index,
        item,
        reason: '包含测试数据特征'
      });
    }
  });
  
  return {
    isValid: testItems.length === 0,
    testItems,
    message: testItems.length > 0 ? `发现 ${testItems.length} 条疑似测试数据` : '数据验证通过'
  };
}

/**
 * 清理并验证页面数据
 */
function cleanPageData(pageInstance) {
  if (!pageInstance || !pageInstance.setData) {

    return false;
  }
  
  try {

    // 清理可能包含测试数据的字段
    const cleanData = {
      allStudents: [],
      filteredStudents: [],
      generatedComments: [],
      works: [],
      filteredWorks: [],
      testData: null,
      mockData: null,
      sampleData: null,
      showEmptyState: true,
      emptyMessage: '暂无数据，请添加真实数据'
    };
    
    pageInstance.setData(cleanData);

    return true;
  } catch (error) {
    console.error('❌ 页面数据清理失败:', error);
    return false;
  }
}

/**
 * 生产环境数据检查
 */
function checkProductionData() {

  const issues = [];
  
  // 检查本地存储
  try {
    const storageInfo = wx.getStorageInfoSync();
    const suspiciousKeys = storageInfo.keys.filter(key => 
      key.includes('test') || key.includes('mock') || key.includes('demo')
    );
    
    if (suspiciousKeys.length > 0) {
      issues.push({
        type: 'localStorage',
        message: `发现疑似测试数据键: ${suspiciousKeys.join(', ')}`
      });
    }
  } catch (error) {

  }
  
  // 检查全局数据
  const app = getApp();
  if (app && app.globalData) {
    const globalKeys = Object.keys(app.globalData);
    const suspiciousGlobalKeys = globalKeys.filter(key => 
      key.includes('test') || key.includes('mock') || key.includes('demo')
    );
    
    if (suspiciousGlobalKeys.length > 0) {
      issues.push({
        type: 'globalData',
        message: `发现疑似测试全局数据: ${suspiciousGlobalKeys.join(', ')}`
      });
    }
  }
  
  return {
    isClean: issues.length === 0,
    issues,
    message: issues.length === 0 ? '✅ 生产环境数据检查通过' : `❌ 发现 ${issues.length} 个问题`
  };
}

/**
 * 完整的数据清理流程
 */
function performFullCleanup(pageInstance = null, options = {}) {

  const { showToast = false } = options; // 默认不显示toast

  const results = {
    localCleanup: false,
    pageCleanup: false,
    validation: null
  };

  // 1. 清理本地存储
  results.localCleanup = clearLocalTestData();

  // 2. 清理页面数据
  if (pageInstance) {
    results.pageCleanup = cleanPageData(pageInstance);
  }

  // 3. 执行验证检查
  results.validation = checkProductionData();

  // 4. 显示结果（仅在明确要求时显示）
  const success = results.localCleanup && (pageInstance ? results.pageCleanup : true) && results.validation.isClean;

  if (showToast) {
    if (success) {

      wx.showToast({
        title: '测试数据已清理',
        icon: 'success'
      });
    } else {

      wx.showToast({
        title: '清理过程中发现问题',
        icon: 'none'
      });
    }
  } else {
    // 静默清理，只记录日志
    if (success) {

    } else {

    }
  }

  return results;
}

module.exports = {
  clearLocalTestData,
  validateDataSource,
  cleanPageData,
  checkProductionData,
  performFullCleanup
};
