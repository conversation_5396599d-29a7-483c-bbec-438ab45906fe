/* Tailwind CSS 基础样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 主题变量定义 */
:root {
  /* 日间模式颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --border-color: #e2e8f0;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* 品牌色 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  /* 品牌名称背景色 */
  --brand-bg-gradient: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  --brand-name-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 状态色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #06b6d4;
}

/* 夜间模式颜色 */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #ffffff;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
  --border-color: #374151;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  
  /* 夜间模式下的品牌色调整 */
  --primary-50: #1e3a8a;
  --primary-100: #1e40af;
  --primary-200: #2563eb;
  --primary-500: #60a5fa;
  --primary-600: #3b82f6;
  --primary-700: #2563eb;
  
  /* 品牌名称背景色 */
  --brand-bg-gradient: linear-gradient(135deg, var(--primary-700), var(--primary-600));
  --brand-name-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 基础样式 */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 容器样式 */
.theme-container {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

.theme-card {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-color) !important;
  box-shadow: var(--shadow) !important;
}

.theme-card-secondary {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
}

/* 文本样式 */
.theme-text-primary {
  color: var(--text-primary) !important;
}

.theme-text-secondary {
  color: var(--text-secondary) !important;
}

.theme-text-tertiary {
  color: var(--text-tertiary) !important;
}

/* 边框样式 */
.theme-border {
  border-color: var(--border-color) !important;
}

/* 阴影样式 */
.theme-shadow {
  box-shadow: var(--shadow);
}

.theme-shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* 渐变背景 */
.theme-gradient-light {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
}

[data-theme="dark"] .theme-gradient-light {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
}

/* Antd组件主题覆盖 */
[data-theme="dark"] .ant-layout {
  background: var(--bg-primary);
}

[data-theme="dark"] .ant-layout-content {
  background: var(--bg-primary);
}

[data-theme="dark"] .ant-card {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .ant-card-head {
  background: var(--bg-secondary);
  border-bottom-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .ant-card-head-title {
  color: var(--text-primary);
}

[data-theme="dark"] .ant-statistic-title {
  color: var(--text-secondary);
}

[data-theme="dark"] .ant-statistic-content {
  color: var(--text-primary);
}

[data-theme="dark"] .ant-typography {
  color: var(--text-primary);
}

[data-theme="dark"] .ant-typography.ant-typography-secondary {
  color: var(--text-secondary);
}

[data-theme="dark"] .ant-btn-text {
  color: var(--text-secondary);
}

[data-theme="dark"] .ant-btn-text:hover {
  color: var(--text-primary);
  background: var(--bg-tertiary);
}

[data-theme="dark"] .ant-alert {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .ant-alert-warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

[data-theme="dark"] .ant-skeleton-content .ant-skeleton-title,
[data-theme="dark"] .ant-skeleton-content .ant-skeleton-paragraph > li {
  background: linear-gradient(90deg, var(--bg-tertiary) 25%, rgba(255, 255, 255, 0.1) 37%, var(--bg-tertiary) 63%);
}

[data-theme="dark"] .ant-progress-bg {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .ant-badge-count,
[data-theme="dark"] .ant-badge-dot {
  background: var(--primary-500);
}

/* 输入框样式 */
.theme-input {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
  transition: all 0.3s ease;
}

.theme-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.theme-input::placeholder {
  color: var(--text-tertiary) !important;
}

[data-theme="dark"] .theme-input {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* 自定义组件样式 */
.theme-toggle {
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  transform: scale(1.1);
}

.theme-toggle.theme-dark {
  color: #fbbf24;
}

.theme-toggle.theme-light {
  color: #64748b;
}

/* 滚动条主题化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--border-color);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 动画效果 */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .theme-toggle {
    font-size: 14px;
  }
}

/* 品牌名称区域样式 */
.brand-name-title {
  background: var(--brand-name-bg);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.brand-name-bg {
  background: var(--brand-bg-gradient);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  box-shadow: var(--shadow);
}

.nav-brand-container {
  min-height: 80px;
  display: flex;
  align-items: center;
  margin-bottom: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-brand-title {
  font-size: 20px;
  font-weight: 600;
  letter-spacing: -0.025em;
  margin: 0;
  padding: 12px 0;
}

.nav-header-height {
  height: 80px; /* 统一高度 */
  min-height: 80px;
}

.nav-height-match {
  height: 80px; /* 匹配头部高度 */
  display: flex;
  align-items: center;
  padding: 0 24px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-secondary: #000000;
  }
  
  [data-theme="dark"] {
    --border-color: #ffffff;
    --text-secondary: #ffffff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}