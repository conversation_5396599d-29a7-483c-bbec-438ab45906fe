import React, { useState, useEffect, Suspense, lazy } from 'react'
import { Row, Col, Card, Statistic, Typography, Button, Alert, Space } from 'antd'
import { 
  UserOutlined, 
  MessageOutlined, 
  RobotOutlined, 
  TrophyOutlined,
  ThunderboltOutlined,
  EyeOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography

// 懒加载性能诊断工具
const PerformanceOptimizer = lazy(() => import('../components/PerformanceOptimizer'))

// 简化的统计卡片组件
const SimpleStatCard = React.memo<{
  title: string
  value: number
  icon: React.ReactNode
  suffix?: string
}>(({ title, value, icon, suffix = '' }) => (
  <Card hoverable className="transition-all duration-200">
    <Statistic
      title={title}
      value={value}
      suffix={suffix}
      valueStyle={{ color: '#1890ff' }}
      prefix={icon}
    />
  </Card>
))

SimpleStatCard.displayName = 'SimpleStatCard'

const FastDashboard: React.FC = () => {
  const [showPerformanceTools, setShowPerformanceTools] = useState(false)
  const [performanceIssues, setPerformanceIssues] = useState<string[]>([])
  const [frameRate, setFrameRate] = useState(60)

  // 实时性能监控
  useEffect(() => {
    let frameCount = 0
    let lastTime = performance.now()
    
    const monitorFrameRate = () => {
      frameCount++
      const currentTime = performance.now()
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        setFrameRate(fps)
        
        // 检测性能问题
        const issues: string[] = []
        if (fps < 30) {
          issues.push(`帧率过低: ${fps}fps`)
        }
        
        const memory = (performance as any).memory
        if (memory) {
          const usedMB = Math.round(memory.usedJSHeapSize / 1048576)
          if (usedMB > 100) {
            issues.push(`内存使用过高: ${usedMB}MB`)
          }
        }
        
        setPerformanceIssues(issues)
        
        frameCount = 0
        lastTime = currentTime
      }
      
      requestAnimationFrame(monitorFrameRate)
    }
    
    requestAnimationFrame(monitorFrameRate)
  }, [])

  // 简化的统计数据（避免复杂渲染）
  const stats = [
    { title: '总用户', value: 1234, icon: <UserOutlined />, suffix: '人' },
    { title: '今日评语', value: 89, icon: <MessageOutlined />, suffix: '条' },
    { title: 'AI调用', value: 456, icon: <RobotOutlined />, suffix: '次' },
    { title: '满意度', value: 95, icon: <TrophyOutlined />, suffix: '%' }
  ]

  return (
    <div className="fast-dashboard p-6">
      {/* 性能警告 */}
      {performanceIssues.length > 0 && (
        <Alert
          message="🐌 检测到性能问题"
          description={
            <div>
              {performanceIssues.map((issue, index) => (
                <div key={index}>• {issue}</div>
              ))}
              <Button 
                type="link" 
                size="small"
                onClick={() => setShowPerformanceTools(true)}
              >
                打开性能诊断工具
              </Button>
            </div>
          }
          type="warning"
          closable
          className="mb-4"
        />
      )}

      {/* 头部 */}
      <div className="flex justify-between items-center mb-6">
        <Title level={2} className="!mb-0">
          📊 快速仪表板
        </Title>
        <Space>
          <div className="text-sm text-gray-500">
            FPS: <span className={frameRate < 30 ? 'text-red-500' : 'text-green-500'}>
              {frameRate}
            </span>
          </div>
          <Button
            type="primary"
            icon={<ThunderboltOutlined />}
            onClick={() => setShowPerformanceTools(!showPerformanceTools)}
          >
            {showPerformanceTools ? '关闭' : '打开'}性能工具
          </Button>
        </Space>
      </div>

      {/* 性能诊断工具 */}
      {showPerformanceTools && (
        <div className="mb-6">
          <Suspense fallback={<Card loading />}>
            <PerformanceOptimizer />
          </Suspense>
        </div>
      )}

      {/* 简化的统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <SimpleStatCard
              title={stat.title}
              value={stat.value}
              icon={stat.icon}
              suffix={stat.suffix}
            />
          </Col>
        ))}
      </Row>

      {/* 性能提示卡片 */}
      <Card title="🚀 性能优化建议" className="mb-4">
        <div className="space-y-2">
          <div className="flex items-center text-green-600">
            <span className="mr-2">✅</span>
            <span>组件已优化：使用React.memo减少重渲染</span>
          </div>
          <div className="flex items-center text-green-600">
            <span className="mr-2">✅</span>
            <span>懒加载已启用：性能工具按需加载</span>
          </div>
          <div className="flex items-center text-green-600">
            <span className="mr-2">✅</span>
            <span>实时监控：帧率和内存使用监控</span>
          </div>
          <div className="flex items-center text-blue-600">
            <span className="mr-2">ℹ️</span>
            <span>建议：保持标签页数量在5个以内以获得最佳性能</span>
          </div>
        </div>
      </Card>

      {/* 调试信息 */}
      <Card size="small" title="🔍 实时性能数据">
        <Row gutter={16}>
          <Col span={8}>
            <Statistic 
              title="帧率" 
              value={frameRate} 
              suffix="fps"
              valueStyle={{ 
                color: frameRate >= 60 ? '#52c41a' : frameRate >= 30 ? '#faad14' : '#ff4d4f'
              }}
            />
          </Col>
          <Col span={8}>
            <Statistic 
              title="DOM节点" 
              value={document.querySelectorAll('*').length}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={8}>
            <Statistic 
              title="页面加载时间" 
              value={Math.round(performance.now())}
              suffix="ms"
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default FastDashboard