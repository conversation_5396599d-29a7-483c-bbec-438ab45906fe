/**
 * 腾讯云开发配置
 * 用于管理后台直接调用云函数，实现免费运行
 */

import tcb from '@cloudbase/js-sdk'

interface CloudbaseConfig {
  env: string
  region?: string
}

class CloudbaseService {
  private _app: tcb.CloudBase | null = null
  private config: CloudbaseConfig
  private isInitialized = false

  // 暴露app实例供直接数据库查询使用
  get app() {
    return this._app
  }

  constructor() {
    this.config = {
      env: 'cloud1-4g85f8xlb8166ff1', // 云开发环境ID
      region: 'ap-shanghai' // 上海区域
    }
  }

  /**
   * 初始化云开发应用
   */
  async initialize(): Promise<tcb.CloudBase> {
    if (this._app && this.isInitialized) {
      return this._app
    }

    try {
      this._app = tcb.init({
        env: this.config.env,
        region: this.config.region
      })

      // 管理员身份验证 - 使用自定义登录
      console.log('🔐 初始化云开发SDK...', {
        env: this.config.env,
        region: this.config.region
      })

      this.isInitialized = true
      return this._app
    } catch (error) {
      console.error('❌ 云开发初始化失败:', error)
      throw new Error('云开发SDK初始化失败')
    }
  }

  /**
   * 获取云开发应用实例
   */
  async getApp(): Promise<tcb.CloudBase> {
    if (!this._app || !this.isInitialized) {
      return await this.initialize()
    }
    return this._app
  }

  /**
   * 管理员身份验证
   * 管理后台使用自定义登录方式
   */
  async authenticateAdmin(token: string): Promise<boolean> {
    try {
      if (!this._app) {
        await this.initialize()
      }

      // 验证管理员token的有效性
      const result = await this.callFunction('validateToken', { token })
      
      if (result.code === 0 && result.data?.valid) {
        console.log('✅ 管理员身份验证成功')
        return true
      } else {
        console.warn('⚠️ 管理员身份验证失败:', result.message)
        return false
      }
    } catch (error) {
      console.error('❌ 管理员身份验证异常:', error)
      return false
    }
  }

  /**
   * 调用云函数（统一入口）
   */
  async callFunction(functionName: string, data: any = {}): Promise<any> {
    try {
      if (!this._app) {
        await this.initialize()
      }

      console.log(`📡 调用云函数: ${functionName}`, {
        timestamp: new Date().toISOString(),
        data: Object.keys(data)
      })

      // 🔥 强制匿名登录 - 确保Web端有权限调用云函数
      try {
        const auth = this._app!.auth()
        
        // 检查是否已登录
        const loginState = await auth.getLoginState()
        console.log('🔍 当前登录状态:', {
          isAnonymous: loginState?.isAnonymous,
          hasUser: !!loginState?.user,
          uid: loginState?.user?.uid
        })
        
        if (!loginState || (!loginState.isAnonymous && !loginState.user)) {
          console.log('🔐 开始匿名登录...')
          
          // 使用正确的匿名登录方法
          await auth.signInAnonymously()
          
          const newLoginState = await auth.getLoginState()
          console.log('✅ 匿名登录成功:', {
            isAnonymous: newLoginState?.isAnonymous,
            uid: newLoginState?.user?.uid
          })
        } else {
          console.log('✅ 已有登录状态，继续使用')
        }
      } catch (authError) {
        console.error('❌ 匿名登录失败:', authError)
        throw new Error('Web端身份验证失败，无法调用云函数')
      }

      // 调用云函数
      const result = await this._app!.callFunction({
        name: functionName,
        data
      })

      if (result.result) {
        console.log(`✅ 云函数调用成功: ${functionName}`)
        return result.result
      } else {
        console.warn(`⚠️ 云函数调用返回空结果: ${functionName}`)
        return { code: -1, message: '云函数返回空结果' }
      }
    } catch (error: any) {
      console.error(`❌ 云函数调用失败: ${functionName}`, error)
      
      // 如果直接调用失败，不使用HTTP代理，直接返回错误
      return {
        code: -1,
        message: error?.message || '云函数调用异常',
        error: error?.code || 'FUNCTION_CALL_FAILED'
      }
    }
  }

  /**
   * 通过HTTP代理调用云函数（备用方案）
   */
  private async callFunctionViaHTTP(functionName: string, data: any = {}): Promise<any> {
    try {
      // 调用adminAPI作为代理
      const response = await fetch('https://doubanshou-1gxvfdq5e6df0f64-1317374781.tcloudbaseapp.com/adminAPI', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WX-SOURCE': 'admin-dashboard-proxy'
        },
        body: JSON.stringify({
          action: 'proxy.cloudFunction',
          functionName,
          functionData: data,
          timestamp: Date.now()
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log(`✅ HTTP代理云函数调用成功: ${functionName}`)
      return result

    } catch (httpError: any) {
      console.error(`❌ HTTP代理调用也失败: ${functionName}`, httpError)
      return {
        code: -1,
        message: httpError?.message || 'HTTP代理调用失败',
        error: 'HTTP_PROXY_FAILED'
      }
    }
  }

  /**
   * 获取数据库实例
   */
  async getDatabase() {
    const app = await this.getApp()
    return app.database()
  }

  /**
   * 获取云存储实例
   */
  async getStorage() {
    const app = await this.getApp()
    return app.uploadFile
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.callFunction('adminAPI', {
        action: 'health',
        timestamp: Date.now()
      })

      return result.code === 0
    } catch (error) {
      console.error('❌ 云开发服务健康检查失败:', error)
      return false
    }
  }

  /**
   * 获取环境信息
   */
  getConfig(): CloudbaseConfig {
    return { ...this.config }
  }

  /**
   * 销毁实例
   */
  destroy() {
    this._app = null
    this.isInitialized = false
    console.log('🔄 云开发服务实例已销毁')
  }
}

// 单例模式
const cloudbaseService = new CloudbaseService()

export default cloudbaseService
export { CloudbaseService }
export type { CloudbaseConfig }