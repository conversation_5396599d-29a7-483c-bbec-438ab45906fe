/**
 * 模板系统工具类
 * Ultra-Think架构重构 - 阶段1：工具函数
 */

/**
 * 生成内容校验和
 */
function generateChecksum(content) {
  if (!content || typeof content !== 'string') {
    return ''
  }
  
  let hash = 0
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash).toString(16)
}

/**
 * 验证模板数据完整性
 */
function validateTemplate(template) {
  const requiredFields = ['id', 'name', 'type', 'content']
  const requiredVariables = ['studentName', 'performanceMaterial']
  
  // 字段完整性检查
  for (const field of requiredFields) {
    if (!template[field]) {
      throw new Error(`Missing required field: ${field}`)
    }
  }
  
  // 内容长度检查
  if (template.content.length < 100) {
    throw new Error('Template content too short (minimum 100 characters)')
  }
  
  if (template.content.length > 5000) {
    throw new Error('Template content too long (maximum 5000 characters)')
  }
  
  // 变量占位符检查
  for (const variable of requiredVariables) {
    if (!template.content.includes(`{${variable}}`)) {
      console.warn(`Template missing variable: ${variable}`)
    }
  }
  
  // 安全性检查：防止脚本注入
  const forbiddenPatterns = [
    /<script.*?>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi
  ]
  
  for (const pattern of forbiddenPatterns) {
    if (pattern.test(template.content)) {
      throw new Error('Template content contains forbidden patterns')
    }
  }
  
  return true
}

/**
 * 将遗留格式转换为统一格式
 */
function convertLegacyToUnified(legacyTemplate, type) {
  const baseId = `${type}_v1`
  const now = new Date().toISOString()
  
  // 如果是字符串格式（小程序硬编码）
  if (typeof legacyTemplate === 'string') {
    return {
      id: baseId,
      name: getTemplateDisplayName(type),
      type: type,
      category: 'term',
      content: legacyTemplate,
      variables: extractVariables(legacyTemplate),
      metadata: {
        author: 'system',
        description: `从硬编码迁移的${type}模板`,
        tags: [type, 'legacy'],
        usageCount: 0
      },
      version: 1,
      checksum: generateChecksum(legacyTemplate),
      enabled: true,
      timestamps: {
        createdAt: now,
        updatedAt: now
      }
    }
  }
  
  // 如果是对象格式（管理后台模拟数据）
  if (typeof legacyTemplate === 'object') {
    return {
      id: baseId,
      name: legacyTemplate.name || getTemplateDisplayName(type),
      type: type,
      category: 'term',
      content: legacyTemplate.content || legacyTemplate.template || '',
      variables: processVariables(legacyTemplate.variables),
      metadata: {
        author: 'admin',
        description: legacyTemplate.description || `管理后台${type}模板`,
        tags: [type, 'admin'],
        usageCount: 0
      },
      version: legacyTemplate.version || 1,
      checksum: generateChecksum(legacyTemplate.content || legacyTemplate.template),
      enabled: legacyTemplate.enabled !== false,
      timestamps: {
        createdAt: legacyTemplate.createdAt || now,
        updatedAt: legacyTemplate.updatedAt || now
      }
    }
  }
  
  throw new Error('Unsupported legacy template format')
}

/**
 * 获取模板显示名称
 */
function getTemplateDisplayName(type) {
  const displayNames = {
    'formal': '正式评语模板',
    'warm': '温馨评语模板',
    'encouraging': '鼓励评语模板',
    'detailed': '详细评语模板'
  }
  return displayNames[type] || `${type}模板`
}

/**
 * 从模板内容中提取变量
 */
function extractVariables(content) {
  if (!content) return []
  
  const variablePattern = /\{(\w+)\}/g
  const variables = []
  let match
  
  while ((match = variablePattern.exec(content)) !== null) {
    const varName = match[1]
    if (!variables.find(v => v.name === varName)) {
      variables.push({
        name: varName,
        type: 'string',
        required: true,
        description: getVariableDescription(varName),
        placeholder: `请输入${varName}`
      })
    }
  }
  
  return variables
}

/**
 * 处理变量数组
 */
function processVariables(variables) {
  if (!variables || !Array.isArray(variables)) {
    return []
  }
  
  return variables.map(variable => {
    if (typeof variable === 'string') {
      return {
        name: variable,
        type: 'string',
        required: true,
        description: getVariableDescription(variable),
        placeholder: `请输入${variable}`
      }
    }
    return variable
  })
}

/**
 * 获取变量描述
 */
function getVariableDescription(varName) {
  const descriptions = {
    'studentName': '学生姓名',
    'performanceMaterial': '学生表现材料',
    'className': '班级名称',
    'subjectName': '科目名称',
    'timeRange': '时间范围'
  }
  return descriptions[varName] || `${varName}参数`
}

/**
 * 替换模板变量
 */
function replaceTemplateVariables(template, variables) {
  if (!template || !variables) {
    return template
  }
  
  let result = template
  Object.keys(variables).forEach(key => {
    const value = variables[key] || ''
    const pattern = new RegExp(`\\{${key}\\}`, 'g')
    result = result.replace(pattern, value)
  })
  
  return result
}

/**
 * 比较两个模板是否相同
 */
function compareTemplates(template1, template2) {
  if (!template1 || !template2) {
    return false
  }
  
  return generateChecksum(template1.content) === generateChecksum(template2.content)
}

/**
 * 创建模板备份
 */
function createTemplateBackup(template) {
  return {
    id: `${template.id}_backup_${Date.now()}`,
    originalId: template.id,
    content: template.content,
    version: template.version,
    checksum: template.checksum,
    createdAt: new Date().toISOString(),
    reason: 'pre_update_backup'
  }
}

/**
 * 安全的模板更新
 */
function safeTemplateUpdate(currentTemplate, updates) {
  // 创建备份
  const backup = createTemplateBackup(currentTemplate)
  
  // 验证更新数据
  const updatedTemplate = {
    ...currentTemplate,
    ...updates,
    version: currentTemplate.version + 1,
    checksum: generateChecksum(updates.content || currentTemplate.content),
    timestamps: {
      ...currentTemplate.timestamps,
      updatedAt: new Date().toISOString()
    }
  }
  
  // 验证更新后的模板
  validateTemplate(updatedTemplate)
  
  return {
    updatedTemplate,
    backup
  }
}

// 导出工具函数
module.exports = {
  generateChecksum,
  validateTemplate,
  convertLegacyToUnified,
  getTemplateDisplayName,
  extractVariables,
  processVariables,
  replaceTemplateVariables,
  compareTemplates,
  createTemplateBackup,
  safeTemplateUpdate
}