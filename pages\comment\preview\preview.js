// 评语预览页面
const app = getApp();
const { showLoading, hideLoading, showSuccess, showError, showConfirm } = require('../../../utils/globalUtils');

// 安全获取云服务实例
function getCloudService() {
  try {
    if (typeof global !== 'undefined' && global.getCloudService) {
      return global.getCloudService();
    }
    
    const cloudService = app.globalData.cloudService;
    if (cloudService) {
      return cloudService;
    }
    
    return null;
  } catch (error) {
    console.error('获取云服务失败:', error);
    return null;
  }
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 评语列表
    commentList: [],
    
    // 统计信息
    totalCount: 0,
    successCount: 0,
    
    // 生成信息
    generateInfo: {
      style: '',
      styleText: '',
      length: '',
      lengthText: '',
      generateTime: ''
    },
    
    // 页面状态
    loading: false,
    saving: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 从页面参数或全局数据获取评语列表
    this.loadCommentData(options);
  },

  /**
   * 加载评语数据
   */
  loadCommentData(options) {
    try {

      // 从页面参数获取数据
      if (options.data) {

        const data = JSON.parse(decodeURIComponent(options.data));

        // 验证每条评语数据
        if (data.comments && data.comments.length > 0) {
          data.comments.forEach((comment, index) => {
            
            // 确保comment字段存在，如果没有则从content字段复制
            if (!comment.comment && comment.content) {
              comment.comment = comment.content;
            }
          });
        }
        
        this.setData({
          commentList: data.comments || [],
          totalCount: data.totalCount || 0,
          successCount: data.successCount || 0,
          generateInfo: data.generateInfo || {}
        });

      } else {

        // 从全局数据获取（如果有的话）
        const globalData = app.globalData.generatedComments;
        if (globalData) {

          this.setData({
            commentList: globalData.comments || [],
            totalCount: globalData.totalCount || 0,
            successCount: globalData.successCount || 0,
            generateInfo: globalData.generateInfo || {}
          });
          // 清除全局数据
          app.globalData.generatedComments = null;
        } else {

        }
      }

      // 输出前3条评语用于调试
      this.data.commentList.slice(0, 3).forEach((item, index) => {
        
      });
      
    } catch (error) {
      console.error('加载评语数据失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 编辑评语
   */
  editComment(e) {
    const { index } = e.currentTarget.dataset;
    const comment = this.data.commentList[index];

    if (!comment) {
      wx.showToast({
        title: '获取评语信息失败',
        icon: 'none'
      });
      return;
    }

    // 跳转到编辑页面
    wx.navigateTo({
      url: `/pages/comment/edit/edit?id=${comment._id || comment.id}`
    });
  },

  /**
   * 重新生成单条评语
   */
  async regenerateComment(e) {
    const { index } = e.currentTarget.dataset;
    const comment = this.data.commentList[index];

    if (!comment) {
      wx.showToast({
        title: '获取评语信息失败',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '重新生成评语',
      content: `确定要重新生成${comment.studentName}的评语吗？\n\n原评语内容将被覆盖，此操作不可撤销。`,
      confirmText: '重新生成',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 构建跳转参数，包含学生信息和重新生成标识（不使用URLSearchParams）
          const paramStr = [
            `mode=regenerate`,
            `commentId=${comment._id || comment.id}`,
            `studentId=${comment.studentId}`,
            `studentName=${encodeURIComponent(comment.studentName)}`,
            `className=${encodeURIComponent(comment.className || '')}`,
            `originalStyle=${comment.style || 'warm'}`,
            `originalLength=${comment.length || 'medium'}`
          ].join('&');

          wx.navigateTo({
            url: `/pages/comment/generate/generate?${paramStr}`
          });
        }
      }
    });
  },

  /**
   * 删除评语
   */
  async deleteComment(e) {
    const { index } = e.currentTarget.dataset;
    const comment = this.data.commentList[index];

    if (!comment) {
      wx.showToast({
        title: '获取评语信息失败',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除${comment.studentName}的评语吗？此操作不可撤销。`,
      confirmText: '删除',
      confirmColor: '#FF5247',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 如果评语已保存到云端，需要删除云端数据
            if (comment._id || comment.id) {
              const { cloudService } = require('../../../services/cloudService');
              const result = await cloudService.deleteComment(comment._id || comment.id);

              if (!result.success) {
                throw new Error(result.error || '删除失败');
              }
            }

            // 从本地列表中删除
            const commentList = [...this.data.commentList];
            commentList.splice(index, 1);

            this.setData({
              commentList,
              successCount: commentList.length
            });

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
          } catch (error) {
            console.error('删除评语失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 保存所有评语
   */
  async saveAllComments() {
    if (this.data.commentList.length === 0) {
      showError('没有可保存的评语');
      return;
    }

    const confirmed = await showConfirm(`确定要保存 ${this.data.commentList.length} 条评语到我的作品吗？`);
    if (!confirmed) return;

    this.setData({ saving: true });
    showLoading('保存中...');

    try {
      // 获取现有的保存评语
      const existingSavedComments = wx.getStorageSync('savedComments') || [];
      const existingRecentComments = wx.getStorageSync('recentComments') || [];
      
      // 准备要保存的评语数据
      const commentsToSave = this.data.commentList.map(comment => ({
        ...comment,
        id: comment.id || `saved_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        saveTime: Date.now(),
        isSaved: true
      }));
      
      // 合并到已保存的评语中
      const updatedSavedComments = [...existingSavedComments, ...commentsToSave];
      
      // 同时更新到最近评语中
      const updatedRecentComments = [...commentsToSave, ...existingRecentComments];
      
      // 保存到本地存储
      wx.setStorageSync('savedComments', updatedSavedComments);
      wx.setStorageSync('recentComments', updatedRecentComments);

      hideLoading();
      showSuccess('保存成功');
      
      // 保存成功后返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      
    } catch (error) {
      hideLoading();
      console.error('保存失败:', error);
      showError('保存失败');
    } finally {
      this.setData({ saving: false });
    }
  },

  /**
   * 返回重新生成
   */
  goBackToGenerate() {
    wx.navigateBack();
  }
});
