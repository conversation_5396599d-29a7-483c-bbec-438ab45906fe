/**
 * 安全登录组件
 * 替换原来的硬编码登录界面
 */

import React, { useState, useEffect } from 'react'
import { useAuth } from '../hooks/useAuth'
import { Lock, Eye, EyeOff, Shield, AlertCircle } from 'lucide-react'

export interface SecureLoginProps {
  onLoginSuccess?: () => void
  onLoginFailure?: (error: string) => void
}

export const SecureLogin: React.FC<SecureLoginProps> = ({
  onLoginSuccess,
  onLoginFailure
}) => {
  const { login, loading, error } = useAuth()
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [validationError, setValidationError] = useState('')
  const [attemptCount, setAttemptCount] = useState(0)
  const [isLocked, setIsLocked] = useState(false)
  const [lockoutTime, setLockoutTime] = useState<number | null>(null)

  // 自动填充测试账号（开发环境）
  useEffect(() => {
    if (import.meta.env.DEV) {
      setCredentials({
        username: 'admin',
        password: 'admin123'
      })
    }
  }, [])

  // 检查锁定状态
  useEffect(() => {
    const checkLockStatus = () => {
      const locked = localStorage.getItem('login_locked')
      if (locked) {
        const { until } = JSON.parse(locked)
        if (Date.now() < until) {
          setIsLocked(true)
          setLockoutTime(until)
        } else {
          localStorage.removeItem('login_locked')
          setIsLocked(false)
          setLockoutTime(null)
        }
      }
    }

    checkLockStatus()
    const interval = setInterval(checkLockStatus, 1000)
    return () => clearInterval(interval)
  }, [])

  // 计算剩余锁定时间
  const getRemainingTime = () => {
    if (!lockoutTime) return ''
    const remaining = Math.max(0, Math.floor((lockoutTime - Date.now()) / 1000))
    const minutes = Math.floor(remaining / 60)
    const seconds = remaining % 60
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // 表单验证
  const validateForm = () => {
    if (!credentials.username) {
      setValidationError('请输入用户名')
      return false
    }
    if (!credentials.password) {
      setValidationError('请输入密码')
      return false
    }
    if (credentials.password.length < 6) {
      setValidationError('密码长度至少6位')
      return false
    }
    setValidationError('')
    return true
  }

  // 处理登录
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (isLocked) return
    if (!validateForm()) return

    // 检查尝试次数限制
    if (attemptCount >= 3) {
      setIsLocked(true)
      const lockUntil = Date.now() + 5 * 60 * 1000 // 5分钟锁定
      localStorage.setItem('login_locked', JSON.stringify({ until: lockUntil }))
      setLockoutTime(lockUntil)
      return
    }

    try {
      const result = await login(credentials)
      
      if (result.success) {
        // 重置尝试次数
        setAttemptCount(0)
        localStorage.removeItem('login_attempts')
        onLoginSuccess?.()
      } else {
        setAttemptCount(prev => {
          const newCount = prev + 1
          localStorage.setItem('login_attempts', newCount.toString())
          return newCount
        })
        onLoginFailure?.(result.error || '登录失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误'
      onLoginFailure?.(errorMessage)
    }
  }

  // 密码强度指示器
  const getPasswordStrength = () => {
    const password = credentials.password
    let strength = 0
    
    if (password.length >= 8) strength += 25
    if (password.match(/[a-z]/)) strength += 25
    if (password.match(/[A-Z]/)) strength += 25
    if (password.match(/[0-9]/)) strength += 25
    if (password.match(/[^a-zA-Z0-9]/)) strength += 25
    
    return Math.min(strength, 100)
  }

  const getPasswordStrengthColor = (strength: number) => {
    if (strength < 25) return 'bg-red-500'
    if (strength < 50) return 'bg-orange-500'
    if (strength < 75) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* 安全徽章 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <Shield className="w-8 h-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            AI管理后台
          </h1>
          <p className="text-purple-200">
            安全加密登录系统
          </p>
        </div>

        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 space-y-6">
          {/* 安全提示 */}
          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3 flex items-start space-x-2">
            <AlertCircle className="w-5 h-5 text-yellow-300 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-yellow-200">
              <p className="font-medium mb-1">安全提示</p>
              <p>使用加密传输，请确保网络环境安全</p>
            </div>
          </div>

          {/* 锁定提示 */}
          {isLocked && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 text-center">
              <Lock className="w-8 h-8 text-red-400 mx-auto mb-2" />
              <p className="text-red-200 font-medium">账户暂时锁定</p>
              <p className="text-red-300 text-sm mt-1">
                {getRemainingTime()} 后重试
              </p>
            </div>
          )}

          {/* 尝试次数提示 */}
          {attemptCount > 0 && !isLocked && (
            <div className="bg-orange-500/10 border border-orange-500/30 rounded-lg p-3">
              <p className="text-orange-300 text-sm text-center">
                第 {attemptCount}/3 次尝试，{3 - attemptCount} 次后将锁定5分钟
              </p>
            </div>
          )}

          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                用户名
              </label>
              <input
                type="text"
                value={credentials.username}
                onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="请输入用户名"
                required
                disabled={isLocked || loading}
                maxLength={50}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">
                密码
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={credentials.password}
                  onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                  className="w-full px-4 py-3 pr-12 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="请输入密码"
                  required
                  disabled={isLocked || loading}
                  maxLength={100}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-300 hover:text-white"
                  disabled={isLocked || loading}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>

              {/* 密码强度条 */}
              {credentials.password && (
                <div className="mt-2">
                  <div className="flex items-center justify-between text-xs text-gray-300 mb-1">
                    <span>密码强度</span>
                    <span>{getPasswordStrength()}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className={`${getPasswordStrengthColor(getPasswordStrength())} h-2 rounded-full transition-all duration-300`} 
                      style={{ width: `${getPasswordStrength()}%` }}
                    />
                  </div>
                </div>
              )}
            </div>

            {validationError && (
              <div className="text-red-300 text-sm flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {validationError}
              </div>
            )}

            {error && (
              <div className="text-red-300 text-sm flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {error}
              </div>
            )}

            <button
              type="submit"
              disabled={isLocked || loading || !credentials.username || !credentials.password}
              className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  登录中...
                </div>
              ) : isLocked ? (
                <div className="flex items-center justify-center">
                  <Lock className="w-4 h-4 mr-2" />
                  账户锁定中...
                </div>
              ) : (
                '安全登录'
              )}
            </button>
          </form>

          {/* 安全声明 */}
          <div className="text-center">
            <p className="text-xs text-gray-400">
              使用军用级AES-256加密技术保护您的凭证
            </p>
          </div>

          {/* 测试用提示（开发环境） */}
          {import.meta.env.DEV && (
            <div className="text-center">
              <p className="text-xs text-purple-300">
                开发环境测试账号: admin / admin123
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default SecureLogin