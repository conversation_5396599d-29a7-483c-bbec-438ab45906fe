Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 页面标题
    title: {
      type: String,
      value: ''
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: false
    },
    // 是否显示品牌区域
    showBrand: {
      type: Boolean,
      value: false
    },
    // 品牌名称
    brandName: {
      type: String,
      value: '评语灵感君'
    },
    // 是否显示菜单按钮
    showMenu: {
      type: Boolean,
      value: false
    },
    // 是否显示右侧插槽
    showRightSlot: {
      type: Boolean,
      value: false
    },
    // 是否禁用返回默认行为
    disableBack: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: 0,
    navbarHeight: 88,
    menuButtonBounding: null
  },

  /**
   * 生命周期函数
   */
  attached() {
    this.initSystemInfo();
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 初始化系统信息
    initSystemInfo() {
      const systemInfo = wx.getSystemInfoSync();
      const menuButtonBounding = wx.getMenuButtonBoundingClientRect();

      const statusBarHeight = systemInfo.statusBarHeight;
      const navbarHeight = (menuButtonBounding.top - statusBarHeight) * 2 + menuButtonBounding.height;

      this.setData({
        statusBarHeight,
        navbarHeight: navbarHeight || 88,
        menuButtonBounding
      });
    },

    // 处理返回
    handleBack() {
      if (this.data.disableBack) {
        this.triggerEvent('backclick');
        return;
      }

      const pages = getCurrentPages();
      if (pages.length > 1) {
        wx.navigateBack({
          delta: 1,
          fail: () => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }
        });
      } else {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }
    },

    // 处理菜单点击
    handleMenu() {
      this.triggerEvent('menuclick');
    },

    // 设置页面标题
    setTitle(title) {
      this.setData({
        title: title
      });
    },

    // 显示返回按钮
    showBackButton() {
      this.setData({
        showBack: true
      });
    },

    // 隐藏返回按钮
    hideBackButton() {
      this.setData({
        showBack: false
      });
    }
  }
});