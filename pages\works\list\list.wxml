<!--我的评语页面 - 参考首页莫兰迪风格-->
<view class="morandi-page">
  <!-- 页面头部 - 按照原型图设计 -->
  <view class="page-header">
    <view class="header-title">我的作品</view>
    <view class="header-actions" wx:if="{{totalWorks > 0}}">
      <view class="header-action secondary" bindtap="onClearAllWorks">清空</view>
      <view class="header-action primary" bindtap="onSmartShare">批量导出</view>
    </view>
  </view>

  <!-- 统计概览 - 按照原型图蓝色渐变设计 -->
  <view class="stats-overview">
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{totalWorks || 0}}</text>
        <text class="stat-label">总作品数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{avgScore || '0.0'}}</text>
        <text class="stat-label">平均评分</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{excellentRate || '0%'}}</text>
        <text class="stat-label">优秀率</text>
      </view>
    </view>
  </view>

  <!-- 搜索筛选 - 按照原型图设计 -->
  <view class="search-section">
    <view class="search-bar">
      <input
        class="search-input"
        placeholder="搜索学生姓名或评语内容..."
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
      />
      <view class="search-icon">🔍</view>
    </view>
    
    <view class="filter-tabs">
      <view
        wx:for="{{filterTabs || []}}"
        wx:key="value"
        class="filter-tab {{currentFilter === item.value ? 'active' : ''}}"
        data-filter="{{item.value}}"
        bindtap="onFilterChange"
      >{{item.name}}</view>
    </view>
  </view>

  <!-- 作品列表 - 按照原型图设计 -->
  <view class="works-list">
    <view
      wx:for="{{filteredWorks}}"
      wx:key="id"
      class="work-item"
      data-work="{{item}}"
    >
      <view class="work-header">
        <view class="work-info">
          <view class="work-student">{{item.studentName}}</view>
          <view class="work-meta">
            <text class="time-text">⏰ {{item.createTimeText}}</text>
            <view class="quality-tag {{item.qualityLevel || 'quality-good'}}">{{item.qualityText || '良好'}}</view>
          </view>
        </view>
        <view class="work-score">{{item.score || '8.5'}}分</view>
      </view>
      
      <view class="work-content">
        {{item.contentPreview || item.content}}
      </view>
      
      <view class="work-actions">
        <view
          class="action-btn"
          data-work="{{item}}"
          catchtap="onEditWork"
        >
          ✏️ 编辑
        </view>
        <view
          class="action-btn"
          data-work="{{item}}"
          catchtap="onCopyWork"
        >
          📋 复制
        </view>
        <view
          class="action-btn"
          data-work="{{item}}"
          catchtap="onShareWork"
        >
          🔗 分享
        </view>
        <view
          class="action-btn primary"
          data-work="{{item}}"
          catchtap="onExportWork"
        >
          💾 导出
        </view>
      </view>
    </view>

  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredWorks.length === 0 && !loading}}">
    <view class="empty-icon">📝</view>
    <text class="empty-title">{{searchKeyword ? '没有找到相关作品' : '还没有创建任何作品'}}</text>
    <text class="empty-desc">{{searchKeyword ? '尝试调整搜索关键词' : '去生成页面开始创建您的第一个作品'}}</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <van-loading size="24px" color="#5470C6" />
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 隐藏的canvas用于生成分享图片 -->
  <canvas
    canvas-id="shareCanvas"
    class="share-canvas"
    style="width: 375px; height: 667px; position: fixed; top: -2000px; left: -1000px; z-index: -999;"
  ></canvas>
</view>
