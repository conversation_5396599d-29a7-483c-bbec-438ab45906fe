import React, { Suspense, lazy } from 'react'
import { Row, Col, Card, Skeleton } from 'antd'
import { useOptimizedData, usePerformanceMonitor } from '@/hooks/useOptimizedData'

// 懒加载组件
const LazyCharts = lazy(() => import('./charts/ModernChart'))
const LazyDataTable = lazy(() => import('./DataTable'))

// 统计卡片组件（已优化）
const StatCard = React.memo<{
  title: string
  value: number
  trend?: { value: number; type: 'up' | 'down' }
  icon: React.ReactNode
  color: string
}>(({ title, value, trend, icon, color }) => {
  return (
    <Card 
      className={`stat-card ${color}`}
      bodyStyle={{ padding: '24px' }}
      hoverable
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-500 dark:text-gray-300 text-sm mb-2">{title}</p>
          <h3 className="text-2xl font-bold mb-0">{value.toLocaleString()}</h3>
          {trend && (
            <p className={`text-sm mb-0 ${trend.type === 'up' ? 'text-green-500' : 'text-red-500'}`}>
              {trend.type === 'up' ? '↗' : '↘'} {trend.value}%
            </p>
          )}
        </div>
        <div className="text-3xl opacity-80">
          {icon}
        </div>
      </div>
    </Card>
  )
})

StatCard.displayName = 'StatCard'

// 优化后的Dashboard组件
const OptimizedDashboard: React.FC = () => {
  const { measureAction } = usePerformanceMonitor('Dashboard')
  
  // 使用优化的数据获取
  const { data: dashboardData, loading, refresh } = useOptimizedData(
    'dashboard',
    async () => {
      const response = await fetch('/api/dashboard')
      return response.json()
    },
    {
      refreshInterval: 30000, // 30秒刷新一次
      revalidateOnFocus: false
    }
  )

  const { data: chartData } = useOptimizedData(
    'charts',
    async () => {
      const response = await fetch('/api/charts')
      return response.json()
    },
    {
      refreshInterval: 60000 // 图表数据1分钟刷新
    }
  )

  const handleRefresh = () => {
    measureAction('dashboard-refresh', () => {
      refresh()
    })
  }

  if (loading && !dashboardData) {
    return (
      <div className="p-6">
        <Row gutter={[16, 16]}>
          {[1, 2, 3, 4].map(i => (
            <Col xs={24} sm={12} lg={6} key={i}>
              <Card>
                <Skeleton active paragraph={{ rows: 2 }} />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    )
  }

  const stats = dashboardData?.stats || []

  return (
    <div className="dashboard-container p-6">
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        {stats.map((stat: any, index: number) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <StatCard
              title={stat.title}
              value={stat.value}
              trend={stat.trend}
              icon={stat.icon}
              color={stat.color}
            />
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Suspense fallback={
            <Card>
              <Skeleton active paragraph={{ rows: 8 }} />
            </Card>
          }>
            <LazyCharts
              title="用户增长趋势"
              type="line"
              data={chartData?.userGrowth}
              height={300}
            />
          </Suspense>
        </Col>
        
        <Col xs={24} lg={12}>
          <Suspense fallback={
            <Card>
              <Skeleton active paragraph={{ rows: 8 }} />
            </Card>
          }>
            <LazyCharts
              title="评语生成分布"
              type="pie"
              data={chartData?.commentDistribution}
              height={300}
            />
          </Suspense>
        </Col>
      </Row>

      {/* 数据表格 */}
      <Row>
        <Col span={24}>
          <Suspense fallback={
            <Card>
              <Skeleton active paragraph={{ rows: 10 }} />
            </Card>
          }>
            <LazyDataTable />
          </Suspense>
        </Col>
      </Row>

      {/* 刷新按钮 */}
      <div className="fixed bottom-6 right-6">
        <button
          onClick={handleRefresh}
          className="bg-blue-500 hover:bg-blue-600 text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:shadow-xl"
        >
          🔄
        </button>
      </div>
    </div>
  )
}

export default OptimizedDashboard