import React, { useState, useEffect } from 'react'
import { Form, Input, Button, Typography, message, Space } from 'antd'
import { UserOutlined, LockOutlined, LoginOutlined, EyeInvisibleOutlined, EyeTwoTone, RobotOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useAuthStore } from '../stores/authStore'
import '../styles/login.css'

const { Title, Text } = Typography

interface LoginForm {
  username: string
  password: string
}

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()
  const login = useAuthStore((state) => state.login)

  const backgroundStyle = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'

  const onFinish = async (values: LoginForm) => {
    setLoading(true)
    try {
      await login(values.username, values.password)
      message.success('🎉 登录成功，欢迎回来！')
      navigate('/dashboard')
    } catch (error) {
      message.error('❌ 登录失败，请检查用户名和密码')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden"
         style={{ background: backgroundStyle }}>
      
      {/* 动态背景装饰 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-4 -left-4 w-72 h-72 bg-white opacity-10 rounded-full mix-blend-multiply filter blur-xl animate-bounce" style={{ animationDelay: '0s', animationDuration: '6s' }}></div>
        <div className="absolute -top-4 -right-4 w-72 h-72 bg-white opacity-10 rounded-full mix-blend-multiply filter blur-xl animate-bounce" style={{ animationDelay: '2s', animationDuration: '6s' }}></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-white opacity-10 rounded-full mix-blend-multiply filter blur-xl animate-bounce" style={{ animationDelay: '4s', animationDuration: '6s' }}></div>
      </div>

      {/* 主登录卡片 */}
      <div className="relative z-10 w-full max-w-md">
        <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-white/20">
          
          {/* Logo 和标题区域 */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mb-4 shadow-lg">
              <RobotOutlined className="text-2xl text-white" />
            </div>
            <Title level={2} className="!mb-2 !text-gray-800 font-bold">
              评语灵感君
            </Title>
            <Text className="text-gray-600 text-base">智能评语生成管理系统</Text>
            <div className="w-12 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mt-3"></div>
          </div>

          {/* 登录表单 */}
          <Form
            name="login"
            onFinish={onFinish}
            autoComplete="off"
            layout="vertical"
            className="login-form space-y-4"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名!' }]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入用户名"
                size="large"
                className="!rounded-xl !border-gray-200 !py-3 hover:!border-blue-400 focus:!border-blue-500 transition-colors"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码!' }]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                size="large"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                className="!rounded-xl !border-gray-200 !py-3 hover:!border-blue-400 focus:!border-blue-500 transition-colors"
              />
            </Form.Item>

            <Form.Item className="!mb-0">
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                size="large"
                icon={<LoginOutlined />}
                className="!w-full !h-12 !rounded-xl !border-none !shadow-lg hover:!shadow-xl transition-all duration-300 !text-base font-semibold"
                style={{ 
                  background: loading ? '#94a3b8' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  transform: loading ? 'scale(0.98)' : 'scale(1)'
                }}
              >
                {loading ? '登录中...' : '立即登录'}
              </Button>
            </Form.Item>
          </Form>





          {/* 版权信息 */}
          <div className="text-center mt-6 pt-4 border-t border-gray-100">
            <Text className="text-gray-400 text-xs">
              © 2024 评语灵感君管理后台 v3.0
            </Text>
          </div>
        </div>

        {/* 浮动装饰元素 */}
        <div className="absolute -z-10 top-4 left-4 w-8 h-8 bg-white/20 rounded-full animate-pulse"></div>
        <div className="absolute -z-10 bottom-4 right-4 w-6 h-6 bg-white/20 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute -z-10 top-1/2 -left-2 w-4 h-4 bg-white/20 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>
    </div>
  )
}

export default Login