/**
 * 监控数据上报云函数
 * 2025年企业级监控系统
 */

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { type, data, sessionId, timestamp } = event
  
  try {
    console.log('监控数据上报:', { type, sessionId, timestamp })
    
    // 验证输入参数
    if (!type || !data) {
      return {
        success: false,
        error: '缺少必要参数'
      }
    }

    // 获取用户信息
    const { OPENID } = cloud.getWXContext()
    
    // 构建监控记录
    const monitoringRecord = {
      type,
      data,
      sessionId,
      userId: OPENID,
      timestamp: new Date(timestamp || Date.now()),
      createTime: new Date(),
      environment: process.env.NODE_ENV || 'production'
    }

    // 根据类型处理不同的监控数据
    switch (type) {
      case 'error':
        await handleErrorReport(monitoringRecord)
        break
      case 'performance':
        await handlePerformanceReport(monitoringRecord)
        break
      case 'event':
        await handleEventReport(monitoringRecord)
        break
      default:
        console.warn('未知的监控类型:', type)
    }

    // 保存到数据库
    await db.collection('monitoring_logs').add({
      data: monitoringRecord
    })

    return {
      success: true,
      message: '监控数据上报成功'
    }

  } catch (error) {
    console.error('监控数据上报失败:', error)
    return {
      success: false,
      error: error.message || '监控数据上报失败'
    }
  }
}

/**
 * 处理错误报告
 */
async function handleErrorReport(record) {
  const { data } = record
  
  // 错误分类和优先级判断
  let priority = 'low'
  if (data.level === 'error') {
    priority = 'high'
  } else if (data.level === 'warning') {
    priority = 'medium'
  }

  // 检查是否为关键错误
  const criticalKeywords = ['crash', 'fatal', 'critical', '崩溃', '致命']
  const isCritical = criticalKeywords.some(keyword => 
    data.message.toLowerCase().includes(keyword)
  )

  if (isCritical) {
    priority = 'critical'
    // 发送告警通知
    await sendAlert('critical_error', {
      message: data.message,
      userId: record.userId,
      timestamp: record.timestamp
    })
  }

  // 更新错误统计
  await updateErrorStats(data.category, priority)
  
  console.log('错误报告处理完成:', { priority, isCritical })
}

/**
 * 处理性能报告
 */
async function handlePerformanceReport(record) {
  const { data } = record
  
  // 性能阈值检查
  const thresholds = {
    page_load: 3000,    // 页面加载 3秒
    api_call: 2000,     // API调用 2秒
    user_action: 1000,  // 用户操作 1秒
    render: 500         // 渲染 0.5秒
  }

  const threshold = thresholds[data.type] || 1000
  
  if (data.duration > threshold) {
    // 性能问题告警
    await sendAlert('performance_issue', {
      type: data.type,
      name: data.name,
      duration: data.duration,
      threshold,
      userId: record.userId
    })
  }

  // 更新性能统计
  await updatePerformanceStats(data.type, data.duration, data.success)
  
  console.log('性能报告处理完成:', { 
    type: data.type, 
    duration: data.duration, 
    threshold 
  })
}

/**
 * 处理事件报告
 */
async function handleEventReport(record) {
  const { data } = record
  
  // 用户行为分析
  if (data.event === 'page_view') {
    await updatePageViewStats(data.properties?.page)
  } else if (data.event === 'user_action') {
    await updateUserActionStats(data.properties?.action)
  }

  // 检查异常行为模式
  await checkAnomalousPatterns(record.userId, data.event)
  
  console.log('事件报告处理完成:', { event: data.event })
}

/**
 * 发送告警通知
 */
async function sendAlert(type, data) {
  try {
    // 这里可以集成多种告警渠道
    // 1. 微信消息推送
    // 2. 邮件通知
    // 3. 短信告警
    // 4. 钉钉/企业微信机器人
    
    console.log('发送告警:', { type, data })
    
    // 保存告警记录
    await db.collection('alerts').add({
      data: {
        type,
        data,
        createTime: new Date(),
        status: 'pending'
      }
    })

    // 如果是关键告警，立即处理
    if (type === 'critical_error') {
      // 可以在这里调用外部API发送紧急通知
      console.log('🚨 关键错误告警:', data.message)
    }

  } catch (error) {
    console.error('发送告警失败:', error)
  }
}

/**
 * 更新错误统计
 */
async function updateErrorStats(category, priority) {
  try {
    const today = new Date().toISOString().split('T')[0]
    const statsKey = `error_stats_${today}`
    
    // 获取今日统计
    const stats = await db.collection('daily_stats')
      .where({ key: statsKey })
      .get()

    if (stats.data.length === 0) {
      // 创建新的统计记录
      await db.collection('daily_stats').add({
        data: {
          key: statsKey,
          type: 'error',
          date: today,
          categories: { [category]: 1 },
          priorities: { [priority]: 1 },
          total: 1,
          createTime: new Date()
        }
      })
    } else {
      // 更新现有统计
      const stat = stats.data[0]
      const categories = stat.categories || {}
      const priorities = stat.priorities || {}
      
      categories[category] = (categories[category] || 0) + 1
      priorities[priority] = (priorities[priority] || 0) + 1
      
      await db.collection('daily_stats').doc(stat._id).update({
        data: {
          categories,
          priorities,
          total: stat.total + 1,
          updateTime: new Date()
        }
      })
    }
  } catch (error) {
    console.error('更新错误统计失败:', error)
  }
}

/**
 * 更新性能统计
 */
async function updatePerformanceStats(type, duration, success) {
  try {
    const today = new Date().toISOString().split('T')[0]
    const statsKey = `performance_stats_${today}`
    
    const stats = await db.collection('daily_stats')
      .where({ key: statsKey })
      .get()

    const newData = {
      type,
      duration,
      success,
      count: 1
    }

    if (stats.data.length === 0) {
      await db.collection('daily_stats').add({
        data: {
          key: statsKey,
          type: 'performance',
          date: today,
          metrics: { [type]: newData },
          createTime: new Date()
        }
      })
    } else {
      const stat = stats.data[0]
      const metrics = stat.metrics || {}
      
      if (metrics[type]) {
        metrics[type].duration = (metrics[type].duration + duration) / 2 // 平均值
        metrics[type].count += 1
        metrics[type].success = metrics[type].success && success
      } else {
        metrics[type] = newData
      }
      
      await db.collection('daily_stats').doc(stat._id).update({
        data: {
          metrics,
          updateTime: new Date()
        }
      })
    }
  } catch (error) {
    console.error('更新性能统计失败:', error)
  }
}

/**
 * 更新页面访问统计
 */
async function updatePageViewStats(page) {
  if (!page) return
  
  try {
    const today = new Date().toISOString().split('T')[0]
    const statsKey = `pageview_stats_${today}`
    
    const stats = await db.collection('daily_stats')
      .where({ key: statsKey })
      .get()

    if (stats.data.length === 0) {
      await db.collection('daily_stats').add({
        data: {
          key: statsKey,
          type: 'pageview',
          date: today,
          pages: { [page]: 1 },
          total: 1,
          createTime: new Date()
        }
      })
    } else {
      const stat = stats.data[0]
      const pages = stat.pages || {}
      pages[page] = (pages[page] || 0) + 1
      
      await db.collection('daily_stats').doc(stat._id).update({
        data: {
          pages,
          total: stat.total + 1,
          updateTime: new Date()
        }
      })
    }
  } catch (error) {
    console.error('更新页面访问统计失败:', error)
  }
}

/**
 * 更新用户操作统计
 */
async function updateUserActionStats(action) {
  if (!action) return
  
  try {
    const today = new Date().toISOString().split('T')[0]
    const statsKey = `action_stats_${today}`
    
    const stats = await db.collection('daily_stats')
      .where({ key: statsKey })
      .get()

    if (stats.data.length === 0) {
      await db.collection('daily_stats').add({
        data: {
          key: statsKey,
          type: 'action',
          date: today,
          actions: { [action]: 1 },
          total: 1,
          createTime: new Date()
        }
      })
    } else {
      const stat = stats.data[0]
      const actions = stat.actions || {}
      actions[action] = (actions[action] || 0) + 1
      
      await db.collection('daily_stats').doc(stat._id).update({
        data: {
          actions,
          total: stat.total + 1,
          updateTime: new Date()
        }
      })
    }
  } catch (error) {
    console.error('更新用户操作统计失败:', error)
  }
}

/**
 * 检查异常行为模式
 */
async function checkAnomalousPatterns(userId, event) {
  try {
    // 检查用户在短时间内的行为频率
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    
    const recentEvents = await db.collection('monitoring_logs')
      .where({
        userId,
        type: 'event',
        timestamp: db.command.gte(fiveMinutesAgo)
      })
      .count()

    // 如果5分钟内事件超过100个，可能是异常行为
    if (recentEvents.total > 100) {
      await sendAlert('anomalous_behavior', {
        userId,
        eventCount: recentEvents.total,
        timeWindow: '5分钟',
        lastEvent: event
      })
    }
  } catch (error) {
    console.error('检查异常行为模式失败:', error)
  }
}
