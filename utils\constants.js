/**
 * 应用常量定义
 * 统一管理所有硬编码的常量值
 */

// 应用信息
export const APP_INFO = {
  NAME: '评语灵感君',
  VERSION: '3.0.0',
  DESCRIPTION: 'AI智能评语生成助手'
};

// 用户角色
export const USER_ROLES = {
  TEACHER: 'teacher',
  ADMIN: 'admin',
  STUDENT: 'student'
};

// 错误代码
export const ERROR_CODES = {
  USER_CANCEL_AUTH: 'USER_CANCEL_AUTH',
  UNSUPPORTED_API: 'UNSUPPORTED_API',
  GET_USER_INFO_FAILED: 'GET_USER_INFO_FAILED',
  NETWORK_ERROR: 'NETWORK_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

// 时间常量（毫秒）
export const TIMING = {
  LOGIN_SUCCESS_DELAY: 1500,
  TOAST_DURATION_SHORT: 1500,
  TOAST_DURATION_NORMAL: 2000,
  TOAST_DURATION_LONG: 3000,
  LOADING_TIMEOUT: 10000
};

// 默认用户信息
export const DEFAULT_USER_INFO = {
  name: '未设置姓名',
  nickName: '用户',
  avatarUrl: '',
  avatar: '',
  avatarText: '用',
  school: '',
  subject: '',
  grade: '',
  phone: '',
  email: ''
};

// 页面路径
export const PAGE_PATHS = {
  INDEX: '/pages/index/index',
  LOGIN: '/pages/login/login',
  SETTINGS: '/pages/settings/settings',
  PROFILE: '/pages/profile/profile'
};

// 存储键名
export const STORAGE_KEYS = {
  USER_INFO: 'userInfo',
  TOKEN: 'token',
  USER_AGREEMENT_ACCEPTED: 'userAgreementAccepted',
  AGREEMENT_ACCEPT_TIME: 'agreementAcceptTime',
  AI_CONFIG: 'aiConfig',
  SYSTEM_SETTINGS: 'systemSettings',
  IS_GUEST_MODE: 'isGuestMode',
  GUEST_MODE_TIME: 'guestModeTime'
};

// AI配置
export const AI_CONFIG = {
  COMMENT_STYLES: {
    WARM: 'warm',
    FORMAL: 'formal',
    ENCOURAGING: 'encouraging',
    DETAILED: 'detailed'
  },
  STYLE_NAMES: {
    warm: '温暖亲切',
    formal: '正式规范',
    encouraging: '鼓励激励',
    detailed: '详细具体'
  }
};

// 数据库集合名称
export const COLLECTIONS = {
  USERS: 'users',
  STUDENTS: 'students',
  CLASSES: 'classes',
  COMMENTS: 'comments',
  RECORDS: 'records',
  AI_CONFIGS: 'ai_configs',
  SETTINGS: 'settings'
};

// 网络请求配置
export const NETWORK = {
  TIMEOUT: 10000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000
};

// 文件大小限制
export const FILE_LIMITS = {
  AVATAR_MAX_SIZE: 2 * 1024 * 1024, // 2MB
  EXPORT_MAX_SIZE: 10 * 1024 * 1024 // 10MB
};

// 正则表达式
export const REGEX = {
  CHINESE: /[\u4e00-\u9fa5]/,
  ENGLISH: /^[a-zA-Z]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
};

// 复姓列表
export const COMPOUND_SURNAMES = [
  '欧阳', '太史', '端木', '上官', '司马', '东方', '独孤', '南宫', '万俟', '闻人',
  '夏侯', '诸葛', '尉迟', '公羊', '赫连', '澹台', '皇甫', '宗政', '濮阳', '公冶',
  '太叔', '申屠', '公孙', '慕容', '仲孙', '钟离', '长孙', '宇文', '司徒', '鲜于'
];

// 导出类型
export const EXPORT_TYPES = {
  ALL_DATA: 'all_data',
  STUDENTS: 'students',
  CLASSES: 'classes',
  COMMENTS: 'comments'
};

// 备份配置
export const BACKUP_CONFIG = {
  MAX_BACKUPS: 10,
  AUTO_BACKUP_INTERVAL: 7 * 24 * 60 * 60 * 1000, // 7天
  BACKUP_PREFIX: 'backup_',
  AUTO_BACKUP_PREFIX: 'auto_backup_'
};
