/**
 * UI交互辅助工具
 * 统一管理加载状态、错误处理、用户反馈等
 */

class UIHelper {
  constructor() {
    this.loadingStack = []; // 加载状态栈
  }

  /**
   * 显示加载状态
   */
  showLoading(title = '加载中...', mask = true) {
    const loadingId = Date.now().toString();
    this.loadingStack.push(loadingId);
    
    wx.showLoading({
      title,
      mask
    });
    
    return loadingId;
  }

  /**
   * 隐藏加载状态
   */
  hideLoading(loadingId = null) {
    if (loadingId) {
      // 移除指定的加载状态
      const index = this.loadingStack.indexOf(loadingId);
      if (index !== -1) {
        this.loadingStack.splice(index, 1);
      }
    } else {
      // 移除最后一个加载状态
      this.loadingStack.pop();
    }

    // 如果没有其他加载状态，则隐藏loading
    if (this.loadingStack.length === 0) {
      wx.hideLoading();
    }
  }

  /**
   * 显示成功提示
   */
  showSuccess(title, duration = 2000) {
    wx.showToast({
      title,
      icon: 'success',
      duration
    });
  }

  /**
   * 显示错误提示
   */
  showError(title, duration = 2000) {
    wx.showToast({
      title,
      icon: 'none',
      duration
    });
  }

  /**
   * 显示警告提示
   */
  showWarning(title, duration = 2000) {
    wx.showToast({
      title,
      icon: 'none',
      duration
    });
  }

  /**
   * 显示确认对话框
   */
  showConfirm(options) {
    const defaultOptions = {
      title: '提示',
      content: '',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      cancelColor: '#999999',
      confirmColor: '#4080FF'
    };

    return new Promise((resolve) => {
      wx.showModal({
        ...defaultOptions,
        ...options,
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }

  /**
   * 显示操作菜单
   */
  showActionSheet(itemList, title = '') {
    return new Promise((resolve) => {
      wx.showActionSheet({
        itemList,
        title,
        success: (res) => {
          resolve(res.tapIndex);
        },
        fail: () => {
          resolve(-1);
        }
      });
    });
  }

  /**
   * 统一错误处理
   */
  handleError(error, context = '') {
    console.error(`${context}错误:`, error);
    
    let errorMessage = '操作失败，请重试';
    
    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && error.message) {
      errorMessage = error.message;
    } else if (error && error.errMsg) {
      errorMessage = error.errMsg;
    }

    // 特殊错误处理
    if (errorMessage.includes('network')) {
      errorMessage = '网络连接失败，请检查网络';
    } else if (errorMessage.includes('timeout')) {
      errorMessage = '请求超时，请重试';
    } else if (errorMessage.includes('permission')) {
      errorMessage = '权限不足，请联系管理员';
    }

    this.showError(errorMessage);
    return errorMessage;
  }

  /**
   * 异步操作包装器
   */
  async withLoading(asyncFn, loadingTitle = '处理中...') {
    const loadingId = this.showLoading(loadingTitle);
    
    try {
      const result = await asyncFn();
      return result;
    } catch (error) {
      this.handleError(error);
      throw error;
    } finally {
      this.hideLoading(loadingId);
    }
  }

  /**
   * 防抖函数
   */
  debounce(func, wait = 300) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * 节流函数
   */
  throttle(func, limit = 300) {
    let inThrottle;
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    const now = new Date();
    const time = new Date(timeStr);
    const diff = now - time;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else if (diff < 604800000) { // 1周内
      return `${Math.floor(diff / 86400000)}天前`;
    } else {
      return time.toLocaleDateString();
    }
  }

  /**
   * 复制到剪贴板
   */
  copyToClipboard(text) {
    return new Promise((resolve) => {
      wx.setClipboardData({
        data: text,
        success: () => {
          this.showSuccess('已复制到剪贴板');
          resolve(true);
        },
        fail: () => {
          this.showError('复制失败');
          resolve(false);
        }
      });
    });
  }

  /**
   * 预览图片
   */
  previewImages(urls, current = 0) {
    wx.previewImage({
      urls,
      current: urls[current]
    });
  }

  /**
   * 选择图片
   */
  chooseImages(count = 1, sizeType = ['compressed'], sourceType = ['album', 'camera']) {
    return new Promise((resolve) => {
      wx.chooseImage({
        count,
        sizeType,
        sourceType,
        success: (res) => {
          resolve(res.tempFilePaths);
        },
        fail: () => {
          this.showError('选择图片失败');
          resolve([]);
        }
      });
    });
  }

  /**
   * 页面跳转
   */
  navigateTo(url, params = {}) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    wx.navigateTo({
      url: fullUrl,
      fail: () => {
        this.showError('页面跳转失败');
      }
    });
  }

  /**
   * 返回上一页
   */
  navigateBack(delta = 1) {
    wx.navigateBack({
      delta,
      fail: () => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }
    });
  }
}

// 创建全局实例
const uiHelper = new UIHelper();

module.exports = {
  uiHelper,
  UIHelper
};
