import React from 'react'
import { create } from 'zustand'
import { subscribeWithSelector, devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// 🔥 1. 细粒度状态分割 - 避免大状态导致的全局重渲染

// 用户状态
interface UserState {
  user: {
    id: string
    name: string
    avatar: string
    role: string
  } | null
  isAuthenticated: boolean
  loading: boolean
  // 🔥 选择器 - 组件只订阅需要的部分
  actions: {
    setUser: (user: UserState['user']) => void
    logout: () => void
    setLoading: (loading: boolean) => void
  }
}

export const useUserStore = create<UserState>()(
  devtools(
    subscribeWithSelector(
      immer((set) => ({
        user: null,
        isAuthenticated: false,
        loading: false,
        
        actions: {
          setUser: (user) => set((state) => {
            state.user = user
            state.isAuthenticated = !!user
          }),
          
          logout: () => set((state) => {
            state.user = null
            state.isAuthenticated = false
          }),
          
          setLoading: (loading) => set((state) => {
            state.loading = loading
          })
        }
      }))
    ),
    { name: 'user-store' }
  )
)

// 仪表板状态 - 分离业务逻辑
interface DashboardState {
  stats: {
    totalUsers: number
    todayComments: number
    aiCalls: number
    satisfaction: number
  }
  activities: Array<{
    id: string
    user: string
    action: string
    time: string
    type: string
  }>
  systemMetrics: {
    cpu: number
    memory: number
    storage: number
    api: number
  }
  
  // 🔥 性能相关状态
  performance: {
    fps: number
    memoryUsage: number
    showAlert: boolean
    showTools: boolean
  }
  
  // 🔥 UI状态分离
  ui: {
    loading: boolean
    refreshing: boolean
    lastUpdated: Date
  }
  
  actions: {
    updateStats: (stats: Partial<DashboardState['stats']>) => void
    addActivity: (activity: DashboardState['activities'][0]) => void
    updateSystemMetrics: (metrics: Partial<DashboardState['systemMetrics']>) => void
    updatePerformance: (performance: Partial<DashboardState['performance']>) => void
    setUIState: (ui: Partial<DashboardState['ui']>) => void
    resetDashboard: () => void
  }
}

export const useDashboardStore = create<DashboardState>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        stats: {
          totalUsers: 0,
          todayComments: 0,
          aiCalls: 0,
          satisfaction: 0
        },
        
        activities: [],
        
        systemMetrics: {
          cpu: 0,
          memory: 0,
          storage: 0,
          api: 0
        },
        
        performance: {
          fps: 60,
          memoryUsage: 0,
          showAlert: false,
          showTools: false
        },
        
        ui: {
          loading: false,
          refreshing: false,
          lastUpdated: new Date()
        },
        
        actions: {
          updateStats: (newStats) => set((state) => {
            Object.assign(state.stats, newStats)
            state.ui.lastUpdated = new Date()
          }),
          
          addActivity: (activity) => set((state) => {
            // 🔥 性能优化：限制活动数量，避免内存泄漏
            state.activities.unshift(activity)
            if (state.activities.length > 50) {
              state.activities = state.activities.slice(0, 50)
            }
          }),
          
          updateSystemMetrics: (metrics) => set((state) => {
            Object.assign(state.systemMetrics, metrics)
          }),
          
          updatePerformance: (performance) => set((state) => {
            Object.assign(state.performance, performance)
            
            // 🔥 自动性能警告逻辑
            if (performance.fps !== undefined && performance.fps < 30) {
              state.performance.showAlert = true
            } else if (performance.fps !== undefined && performance.fps >= 30) {
              state.performance.showAlert = false
            }
          }),
          
          setUIState: (ui) => set((state) => {
            Object.assign(state.ui, ui)
          }),
          
          resetDashboard: () => set((state) => {
            // 重置除了用户状态外的所有状态
            state.activities = []
            state.performance = {
              fps: 60,
              memoryUsage: 0,
              showAlert: false,
              showTools: false
            }
            state.ui = {
              loading: false,
              refreshing: false,
              lastUpdated: new Date()
            }
          })
        }
      }))
    ),
    { name: 'dashboard-store' }
  )
)

// 🔥 2. 选择器优化 - 防止不必要的重渲染
export const dashboardSelectors = {
  // 只选择统计数据
  stats: (state: DashboardState) => state.stats,
  
  // 只选择活动列表
  activities: (state: DashboardState) => state.activities,
  
  // 只选择系统指标
  systemMetrics: (state: DashboardState) => state.systemMetrics,
  
  // 只选择性能数据
  performance: (state: DashboardState) => state.performance,
  
  // 只选择UI状态
  ui: (state: DashboardState) => state.ui,
  
  // 动作选择器
  actions: (state: DashboardState) => state.actions,
  
  // 🔥 组合选择器 - 减少重复计算
  performanceStatus: (state: DashboardState) => ({
    fps: state.performance.fps,
    showAlert: state.performance.showAlert,
    memoryUsage: state.performance.memoryUsage
  }),
  
  // 🔥 条件选择器
  shouldShowPerformanceAlert: (state: DashboardState) => 
    state.performance.fps < 30 || state.performance.memoryUsage > 100,
}

// 🔥 3. 高性能Hooks - 避免重复订阅
export const useDashboardStats = () => 
  useDashboardStore(dashboardSelectors.stats)

export const useDashboardActivities = () => 
  useDashboardStore(dashboardSelectors.activities)

export const useDashboardSystemMetrics = () => 
  useDashboardStore(dashboardSelectors.systemMetrics)

export const useDashboardPerformance = () => 
  useDashboardStore(dashboardSelectors.performance)

export const useDashboardUI = () => 
  useDashboardStore(dashboardSelectors.ui)

export const useDashboardActions = () => 
  useDashboardStore(dashboardSelectors.actions)

// 🔥 4. 高级选择器Hooks
export const usePerformanceStatus = () => 
  useDashboardStore(dashboardSelectors.performanceStatus)

export const useShouldShowPerformanceAlert = () => 
  useDashboardStore(dashboardSelectors.shouldShowPerformanceAlert)

// 🔥 5. 批量更新Hook - 减少重渲染次数
export const useBatchDashboardUpdate = () => {
  const actions = useDashboardActions()
  
  return (updates: {
    stats?: Partial<DashboardState['stats']>
    metrics?: Partial<DashboardState['systemMetrics']>
    performance?: Partial<DashboardState['performance']>
    ui?: Partial<DashboardState['ui']>
  }) => {
    // 使用immer的批量更新
    useDashboardStore.setState((state) => {
      if (updates.stats) {
        Object.assign(state.stats, updates.stats)
      }
      if (updates.metrics) {
        Object.assign(state.systemMetrics, updates.metrics)
      }
      if (updates.performance) {
        Object.assign(state.performance, updates.performance)
      }
      if (updates.ui) {
        Object.assign(state.ui, updates.ui)
      }
      
      // 更新时间戳
      state.ui.lastUpdated = new Date()
    })
  }
}

// 🔥 6. 持久化状态（可选）
const persistConfig = {
  name: 'dashboard-storage',
  partialize: (state: DashboardState) => ({
    // 只持久化需要的状态
    stats: state.stats,
    systemMetrics: state.systemMetrics
    // 不持久化activities和performance，因为它们是实时数据
  })
}

// 🔥 7. 开发工具集成
if (process.env.NODE_ENV === 'development') {
  // 添加状态变化日志
  useDashboardStore.subscribe(
    (state) => state,
    (state, prevState) => {
      console.group('🏪 Dashboard Store Update')
      console.log('Previous:', prevState)
      console.log('Current:', state)
      console.groupEnd()
    }
  )
}

// 🔥 8. 性能监控集成
export const useStorePerformanceMonitor = () => {
  const [renderCount, setRenderCount] = React.useState(0)
  const [lastRenderTime, setLastRenderTime] = React.useState(Date.now())
  
  // 监控组件渲染次数
  React.useEffect(() => {
    setRenderCount(prev => prev + 1)
    setLastRenderTime(Date.now())
  })
  
  return {
    renderCount,
    timeSinceLastRender: Date.now() - lastRenderTime,
    
    // 重置计数器
    resetMonitor: () => {
      setRenderCount(0)
      setLastRenderTime(Date.now())
    }
  }
}

// 导出所有优化后的状态管理工具
export default {
  useUserStore,
  useDashboardStore,
  dashboardSelectors,
  useDashboardStats,
  useDashboardActivities,
  useDashboardSystemMetrics,
  useDashboardPerformance,
  useDashboardUI,
  useDashboardActions,
  usePerformanceStatus,
  useShouldShowPerformanceAlert,
  useBatchDashboardUpdate,
  useStorePerformanceMonitor
}