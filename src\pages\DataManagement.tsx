import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Space,
  Typography,
  Input,
  Select,
  DatePicker,
  Card,
  Tabs,
  Upload,
  message,
  Modal,
  Tag,
  Drawer,
  Descriptions,
  Row,
  Col,
  InputNumber,
  Form,
  notification,
  Spin,
  Alert
} from 'antd'
import {
  SearchOutlined,
  DownloadOutlined,
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  DatabaseOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { RangePickerProps } from 'antd/es/date-picker'
import dayjs from 'dayjs'
import * as XLSX from 'xlsx'
import { dataService } from '../services'

const { Title, Text } = Typography
const { Search, TextArea } = Input
const { Option } = Select
const { RangePicker } = DatePicker
const { confirm } = Modal

interface Student {
  id: string
  name: string
  class: string
  teacher: string
  commentsCount: number
  lastUpdate: string
  status: 'active' | 'inactive'
}

interface Comment {
  id: string
  studentName: string
  className: string
  teacher: string
  content: string
  type: string
  createTime: string
  aiModel: string
  status: 'success' | 'failed'
}

const DataManagement: React.FC = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('students')
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [editingRecord, setEditingRecord] = useState<any>(null)
  const [editForm] = Form.useForm()
  
  // 数据和加载状态
  const [studentsData, setStudentsData] = useState<Student[]>([])
  const [commentsData, setCommentsData] = useState<Comment[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 筛选和搜索状态
  const [searchKeyword, setSearchKeyword] = useState('')
  const [selectedClass, setSelectedClass] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [classList, setClassList] = useState<Array<{id: string, name: string}>>([])

  // 评语管理相关的筛选数据
  const [teacherList, setTeacherList] = useState<Array<{id: string, name: string}>>([])
  const [commentTypeList, setCommentTypeList] = useState<Array<{id: string, name: string}>>([])
  const [selectedCommentClass, setSelectedCommentClass] = useState('')
  const [selectedTeacher, setSelectedTeacher] = useState('')
  const [selectedCommentType, setSelectedCommentType] = useState('')

  // 过滤后的数据
  const [filteredStudentsData, setFilteredStudentsData] = useState<Student[]>([])
  const [filteredCommentsData, setFilteredCommentsData] = useState<Comment[]>([])

  // 获取班级列表 - 从学生数据中提取
  const fetchClassList = async () => {
    try {
      // 先尝试从学生数据中提取班级
      const studentData = await dataService.getStudents({ limit: 1000 })
      console.log('✅ 获取学生数据用于提取班级:', studentData)

      if (studentData.list && studentData.list.length > 0) {
        // 从学生数据中提取唯一的班级
        const classSet = new Set<string>()
        studentData.list.forEach((student: any) => {
          if (student.className) {
            classSet.add(student.className)
          }
          if (student.class) {
            classSet.add(student.class)
          }
        })

        const classListFromStudents = Array.from(classSet).map((name, index) => ({
          id: `class_${index}`,
          name: name
        }))

        if (classListFromStudents.length > 0) {
          console.log('✅ 从学生数据提取的班级列表:', classListFromStudents)
          setClassList(classListFromStudents)
          return
        }
      }

      // 如果学生数据中没有班级信息，尝试从班级表获取
      const data = await dataService.getClasses()
      console.log('✅ 获取班级表数据:', data)

      let classList = []
      if (data.list && Array.isArray(data.list)) {
        classList = data.list
      } else if (data.classes && Array.isArray(data.classes)) {
        classList = data.classes
      } else if (Array.isArray(data)) {
        classList = data
      }

      const formattedClasses = classList.map((cls: any) => ({
        id: cls._id || cls.id,
        name: cls.className || cls.name
      }))

      console.log('✅ 格式化后的班级列表:', formattedClasses)
      setClassList(formattedClasses)
    } catch (error) {
      console.error('❌ 获取班级列表失败:', error)
      // 使用默认班级列表作为备用
      setClassList([
        { id: 'default1', name: '三年级一班' },
        { id: 'default2', name: '三年级二班' },
        { id: 'default3', name: '四年级一班' },
        { id: 'default4', name: '四年级二班' }
      ])
    }
  }

  // 获取教师列表 - 已废弃，现在直接从评语数据中提取
  const fetchTeacherList = async () => {
    console.log('⚠️ fetchTeacherList 已废弃，现在直接从评语数据中提取教师信息')
  }

  // 获取评语类型列表 - 已废弃，现在直接从评语数据中提取
  const fetchCommentTypeList = async () => {
    console.log('⚠️ fetchCommentTypeList 已废弃，现在直接从评语数据中提取类型信息')
  }

  // 获取学生数据
  const fetchStudentsData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const data = await dataService.getStudents({ limit: 50 })
      console.log('✅ 获取学生数据成功:', data)
      
      // 转换数据格式
      const formattedStudents = data.list.map((student: any) => ({
        id: student._id || student.id,
        name: student.name,
        class: student.class,
        teacher: student.teacher || '未分配',
        commentsCount: student.commentsCount || 0,
        lastUpdate: student.updateTime || student.createTime || new Date().toISOString(),
        status: student.status || 'active',
        grade: student.grade,
        gender: student.gender,
        age: student.age,
        phone: student.phone,
        parentName: student.parentName,
        enrollDate: student.enrollDate
      }))
      
      setStudentsData(formattedStudents)
      setFilteredStudentsData(formattedStudents) // 初始化过滤数据

    } catch (error) {
      console.error('❌ 获取学生数据失败:', error)
      setError(error instanceof Error ? error.message : '获取学生数据失败')
      
      notification.error({
        message: '学生数据获取失败',
        description: `无法获取学生信息: ${error instanceof Error ? error.message : '网络连接错误'}`,
        placement: 'topRight',
        duration: 8,
        btn: (
          <Button type="primary" size="small" onClick={fetchStudentsData}>
            重新获取
          </Button>
        )
      })
    } finally {
      setLoading(false)
    }
  }

  // 获取评语数据
  const fetchCommentsData = async () => {
    try {
      setLoading(true)
      setError(null)

      const data = await dataService.getComments({ limit: 50 })
      console.log('✅ 获取评语数据成功:', data)

      // 转换数据格式
      const formattedComments = data.list.map((comment: any) => ({
        id: comment._id || comment.id,
        studentName: comment.studentName,
        className: comment.className || comment.class,
        teacher: comment.teacherName || comment.teacher,
        content: comment.content,
        type: comment.type || '未分类',
        createTime: comment.createTime || new Date().toISOString(),
        aiModel: comment.aiModel || '未知模型',
        status: comment.status === 'success' || !comment.status ? 'success' : 'failed',
        tokens: comment.tokensUsed || 0
      }))

      setCommentsData(formattedComments)
      setFilteredCommentsData(formattedComments) // 初始化过滤数据

      // 评语数据加载完成后，立即提取教师和类型数据
      extractTeacherAndTypeData(formattedComments)

    } catch (error) {
      console.error('❌ 获取评语数据失败:', error)
      setError(error instanceof Error ? error.message : '获取评语数据失败')

      notification.error({
        message: '评语数据获取失败',
        description: `无法获取评语信息: ${error instanceof Error ? error.message : '网络连接错误'}`,
        placement: 'topRight',
        duration: 8,
        btn: (
          <Button type="primary" size="small" onClick={fetchCommentsData}>
            重新获取
          </Button>
        )
      })
    } finally {
      setLoading(false)
    }
  }

  // 从评语数据中提取教师和类型数据
  const extractTeacherAndTypeData = (comments: any[]) => {
    console.log('🔍 开始从评语数据中提取教师和类型信息:', comments)

    // 提取教师信息
    const teacherSet = new Set<string>()
    const typeSet = new Set<string>()

    comments.forEach((comment: any) => {
      if (comment.teacher) {
        teacherSet.add(comment.teacher)
      }
      if (comment.type && comment.type !== '未分类') {
        typeSet.add(comment.type)
      }
    })

    // 生成教师列表
    const teacherList = Array.from(teacherSet).map((name, index) => ({
      id: `teacher_${index}`,
      name: name
    }))

    // 生成类型列表，如果没有类型数据则使用默认类型
    let typeList = Array.from(typeSet).map((name, index) => ({
      id: `type_${index}`,
      name: name
    }))

    if (typeList.length === 0) {
      typeList = [
        { id: 'type_1', name: '温暖鼓励型' },
        { id: 'type_2', name: '建设性反馈型' },
        { id: 'type_3', name: '个性化指导型' },
        { id: 'type_4', name: '综合评价型' }
      ]
    }

    console.log('✅ 提取的教师列表:', teacherList)
    console.log('✅ 提取的类型列表:', typeList)

    setTeacherList(teacherList)
    setCommentTypeList(typeList)
  }

  // 数据过滤函数
  const filterStudentsData = () => {
    let filtered = [...studentsData]

    // 关键词搜索
    if (searchKeyword.trim()) {
      filtered = filtered.filter(student =>
        student.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        student.class.toLowerCase().includes(searchKeyword.toLowerCase())
      )
    }

    // 班级筛选
    if (selectedClass) {
      const selectedClassName = classList.find(cls => cls.id === selectedClass)?.name
      if (selectedClassName) {
        filtered = filtered.filter(student => student.class === selectedClassName)
      }
    }

    // 状态筛选
    if (selectedStatus) {
      filtered = filtered.filter(student => student.status === selectedStatus)
    }

    setFilteredStudentsData(filtered)
  }

  const filterCommentsData = () => {
    let filtered = [...commentsData]

    // 关键词搜索
    if (searchKeyword.trim()) {
      filtered = filtered.filter(comment =>
        comment.studentName.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        comment.content.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        (comment.className && comment.className.toLowerCase().includes(searchKeyword.toLowerCase()))
      )
    }

    // 班级筛选
    if (selectedCommentClass) {
      const selectedClassName = classList.find(cls => cls.id === selectedCommentClass)?.name
      if (selectedClassName) {
        filtered = filtered.filter(comment => comment.className === selectedClassName)
      }
    }

    // 教师筛选
    if (selectedTeacher) {
      const selectedTeacherName = teacherList.find(teacher => teacher.id === selectedTeacher)?.name
      if (selectedTeacherName) {
        filtered = filtered.filter(comment => comment.teacher === selectedTeacherName)
      }
    }

    // 类型筛选
    if (selectedCommentType) {
      const selectedTypeName = commentTypeList.find(type => type.id === selectedCommentType)?.name
      if (selectedTypeName) {
        filtered = filtered.filter(comment => comment.type === selectedTypeName)
      }
    }

    setFilteredCommentsData(filtered)
  }

  // 当筛选条件改变时重新过滤数据
  useEffect(() => {
    if (activeTab === 'students') {
      filterStudentsData()
    } else {
      filterCommentsData()
    }
  }, [searchKeyword, selectedClass, selectedStatus, selectedCommentClass, selectedTeacher, selectedCommentType, studentsData, commentsData, activeTab])

  // 获取当前tab对应的数据
  const fetchCurrentTabData = () => {
    if (activeTab === 'students') {
      fetchStudentsData()
    } else {
      fetchCommentsData()
      // 教师和类型数据现在直接从评语数据中提取，不需要单独调用
    }
  }

  // 组件加载时获取数据
  useEffect(() => {
    fetchClassList() // 先加载班级列表
    fetchCurrentTabData()
  }, [activeTab])

  // 原有的模拟评语数据作为备份
  const mockCommentsData: Comment[] = [
    {
      id: '1',
      studentName: '张小明',
      className: '三年级一班',
      teacher: '王老师',
      content: '张小明同学在本周表现优秀，数学作业完成质量很高...',
      type: '温暖鼓励型',
      createTime: '2024-01-27 10:30:00',
      aiModel: '豆包模型',
      status: 'success'
    },
    {
      id: '2',
      studentName: '李小红',
      className: '三年级一班',
      teacher: '王老师', 
      content: '李小红同学这周在语文课上积极发言，朗读能力有明显提升...',
      type: '客观分析型',
      createTime: '2024-01-26 15:20:00',
      aiModel: '豆包模型',
      status: 'success'
    }
  ]

  const studentColumns: ColumnsType<Student> = [
    {
      title: '学生姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120
    },
    {
      title: '班级',
      dataIndex: 'class',
      key: 'class',
      width: 120
    },
    {
      title: '任课教师',
      dataIndex: 'teacher',
      key: 'teacher',
      width: 100
    },
    {
      title: '评语数量',
      dataIndex: 'commentsCount',
      key: 'commentsCount',
      width: 120,
      sorter: (a, b) => a.commentsCount - b.commentsCount
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      width: 160,
      sorter: (a, b) => dayjs(a.lastUpdate).unix() - dayjs(b.lastUpdate).unix()
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '活跃' : '非活跃'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditStudent(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            size="small"
            danger
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  const commentColumns: ColumnsType<Comment> = [
    {
      title: '学生姓名',
      dataIndex: 'studentName',
      key: 'studentName',
      width: 100
    },
    {
      title: '班级',
      dataIndex: 'className',
      key: 'className',
      width: 120
    },
    {
      title: '教师',
      dataIndex: 'teacher',
      key: 'teacher',
      width: 80
    },
    {
      title: '评语内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (text: string) => text.length > 50 ? text.substring(0, 50) + '...' : text
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => <Tag color="blue">{type}</Tag>
    },
    {
      title: 'AI模型',
      dataIndex: 'aiModel',
      key: 'aiModel',
      width: 100
    },
    {
      title: '生成时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      sorter: (a, b) => dayjs(a.createTime).unix() - dayjs(b.createTime).unix()
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'success' ? 'success' : 'error'}>
          {status === 'success' ? '成功' : '失败'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditComment(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            size="small"
            danger
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  const handleViewDetail = (record: any) => {
    setSelectedRecord(record)
    setDetailDrawerVisible(true)
  }

  const handleEditStudent = (record: any) => {
    setEditingRecord(record)
    editForm.setFieldsValue({
      name: record.name,
      id: record.id,
      class: record.class,
      grade: record.grade,
      gender: record.gender,
      age: record.age,
      phone: record.phone,
      parentName: record.parentName,
      enrollDate: record.enrollDate,
      status: record.status
    })
    setEditModalVisible(true)
  }

  const handleEditComment = (record: any) => {
    setEditingRecord(record)
    editForm.setFieldsValue({
      studentName: record.studentName,
      class: record.class,
      content: record.content,
      teacher: record.teacher,
      aiModel: record.aiModel
    })
    setEditModalVisible(true)
  }

  const handleEditSubmit = async (values: any) => {
    try {
      // 模拟更新请求
      await new Promise(resolve => setTimeout(resolve, 1000))

      message.success('更新成功')
      setEditModalVisible(false)
      editForm.resetFields()
      setEditingRecord(null)

      // 这里可以刷新数据
    } catch (error) {
      message.error('更新失败，请重试')
    }
  }

  const handleDelete = (id: string) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '删除后数据将无法恢复，确定要删除吗？',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        message.success('删除成功')
      }
    })
  }

  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的数据')
      return
    }
    
    confirm({
      title: '批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRowKeys.length} 条数据吗？`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        setSelectedRowKeys([])
        message.success('批量删除成功')
      }
    })
  }

  const handleExport = () => {
    try {
      // 根据当前标签页决定导出的数据
      const dataToExport = activeTab === 'students' ? studentsData : commentsData
      const fileName = activeTab === 'students' ? '学生数据' : '评语数据'

      // 创建工作簿
      const wb = XLSX.utils.book_new()

      if (activeTab === 'students') {
        // 学生数据导出
        const studentExportData = studentsData.map(student => ({
          '学号': student.id,
          '姓名': student.name,
          '班级': student.class,
          '年级': student.grade,
          '性别': student.gender,
          '年龄': student.age,
          '联系电话': student.phone,
          '家长姓名': student.parentName,
          '入学时间': student.enrollDate,
          '状态': student.status === 'active' ? '在校' : '离校'
        }))

        const ws = XLSX.utils.json_to_sheet(studentExportData)
        XLSX.utils.book_append_sheet(wb, ws, '学生数据')
      } else {
        // 评语数据导出
        const commentExportData = commentsData.map(comment => ({
          '评语ID': comment.id,
          '学生姓名': comment.studentName,
          '班级': comment.class,
          '评语内容': comment.content,
          '生成时间': comment.createTime,
          '教师': comment.teacher,
          '状态': comment.status === 'success' ? '成功' : '失败',
          'AI模型': comment.aiModel || 'GPT-3.5',
          'Tokens消耗': comment.tokens || 0
        }))

        const ws = XLSX.utils.json_to_sheet(commentExportData)
        XLSX.utils.book_append_sheet(wb, ws, '评语数据')
      }

      // 导出文件
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
      XLSX.writeFile(wb, `${fileName}_${timestamp}.xlsx`)

      message.success(`${fileName}导出成功！`)
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败，请重试')
    }
  }

  // 下载学生导入模板 - 与小程序保持一致
  const handleDownloadTemplate = () => {
    try {
      // 创建模板数据 - 与小程序8字段格式保持一致
      const templateData = [
        {
          '姓名*': '张小明',
          '学号*': '2024001',
          '班级*': '三年级一班',
          '性别': '男',
          '联系电话': '13800138001',
          '家长电话': '13900139001',
          '地址': '北京市朝阳区',
          '备注': '学习认真，积极向上'
        },
        {
          '姓名*': '李小红',
          '学号*': '2024002',
          '班级*': '三年级一班',
          '性别': '女',
          '联系电话': '13800138002',
          '家长电话': '13900139002',
          '地址': '北京市海淀区',
          '备注': '活泼开朗，乐于助人'
        },
        {
          '姓名*': '王小华',
          '学号*': '2024003',
          '班级*': '三年级二班',
          '性别': '男',
          '联系电话': '13800138003',
          '家长电话': '13900139003',
          '地址': '北京市西城区',
          '备注': '思维敏捷，善于思考'
        }
      ]

      // 创建工作簿
      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(templateData)

      // 设置列宽
      const colWidths = [
        { wch: 12 }, // 姓名*
        { wch: 12 }, // 学号*
        { wch: 15 }, // 班级*
        { wch: 8 },  // 性别
        { wch: 15 }, // 联系电话
        { wch: 15 }, // 家长电话
        { wch: 20 }, // 地址
        { wch: 20 }  // 备注
      ]
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, '学生信息模板')

      // 添加说明工作表
      const instructionData = [
        { '字段名': '姓名*', '说明': '学生真实姓名（必填）', '是否必填': '是', '示例': '张小明' },
        { '字段名': '学号*', '说明': '学生唯一标识（必填）', '是否必填': '是', '示例': '2024001' },
        { '字段名': '班级*', '说明': '学生所在班级（必填）', '是否必填': '是', '示例': '三年级一班' },
        { '字段名': '性别', '说明': '学生性别，填写"男"或"女"', '是否必填': '否', '示例': '男' },
        { '字段名': '联系电话', '说明': '学生联系电话', '是否必填': '否', '示例': '13800138001' },
        { '字段名': '家长电话', '说明': '家长或监护人联系电话', '是否必填': '否', '示例': '13900139001' },
        { '字段名': '地址', '说明': '学生家庭住址', '是否必填': '否', '示例': '北京市朝阳区' },
        { '字段名': '备注', '说明': '其他备注信息', '是否必填': '否', '示例': '学习认真，积极向上' }
      ]

      const instructionWs = XLSX.utils.json_to_sheet(instructionData)
      instructionWs['!cols'] = [
        { wch: 12 }, // 字段名
        { wch: 30 }, // 说明
        { wch: 12 }, // 是否必填
        { wch: 15 }  // 示例
      ]
      XLSX.utils.book_append_sheet(wb, instructionWs, '填写说明')

      // 导出文件
      const fileName = `学生批量导入模板_${dayjs().format('YYYY-MM-DD_HHmm')}.xlsx`
      XLSX.writeFile(wb, fileName)

      message.success('模板下载成功！模板包含8个字段，与小程序导入格式完全一致')
    } catch (error) {
      console.error('模板下载失败:', error)
      message.error('模板下载失败，请重试')
    }
  }

  const uploadProps = {
    name: 'file',
    action: '/api/upload',
    onChange(info: any) {
      if (info.file.status === 'done') {
        message.success('数据导入成功')
      } else if (info.file.status === 'error') {
        message.error('数据导入失败')
      }
    }
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(selectedRowKeys)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6 transition-colors">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <Title level={1} className="!mb-2 theme-text-primary flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                <DatabaseOutlined className="text-2xl text-white" />
              </div>
              数据管理中心
            </Title>
            <Text className="theme-text-secondary text-lg">
              管理学生信息和评语数据
            </Text>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold theme-text-primary mb-1">
              {new Date().toLocaleTimeString()}
            </div>
            <div className="theme-text-secondary">
              {new Date().toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long' 
              })}
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-6">

        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-xl transition-colors">
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            type="card"
            items={[
              {
                key: 'students',
                label: '学生管理',
                children: (
            <Card>
              {/* 数据连接状态提示 */}
              {error && (
                <Alert
                  message="数据获取失败"
                  description={error}
                  type="error"
                  showIcon
                  className="mb-4"
                  action={
                    <Button size="small" onClick={fetchStudentsData}>
                      重新获取
                    </Button>
                  }
                />
              )}
              
              {/* 搜索和操作栏 */}
              <div className="flex flex-wrap justify-between items-center gap-4 mb-4">
                <Space wrap size="middle">
                  <Search
                    placeholder="搜索学生姓名"
                    style={{ width: 200 }}
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    allowClear
                  />
                  <Select
                    placeholder="选择班级"
                    style={{ width: 120 }}
                    value={selectedClass}
                    onChange={setSelectedClass}
                    allowClear
                  >
                    {classList.map(cls => (
                      <Option key={cls.id} value={cls.id}>{cls.name}</Option>
                    ))}
                  </Select>
                  <Select
                    placeholder="选择状态"
                    style={{ width: 100 }}
                    value={selectedStatus}
                    onChange={setSelectedStatus}
                    allowClear
                  >
                    <Option value="active">活跃</Option>
                    <Option value="inactive">非活跃</Option>
                  </Select>
                </Space>

                <Space wrap size="small">
                  <Upload {...uploadProps}>
                    <Button icon={<UploadOutlined />} size="middle">
                      导入学生
                    </Button>
                  </Upload>
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={handleDownloadTemplate}
                    type="dashed"
                    size="middle"
                  >
                    下载模板
                  </Button>
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={handleExport}
                    size="middle"
                  >
                    导出数据
                  </Button>
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    onClick={handleBatchDelete}
                    disabled={selectedRowKeys.length === 0}
                    size="middle"
                  >
                    批量删除
                  </Button>
                </Space>
              </div>

              <Spin spinning={loading} tip="正在获取数据...">
                <Table
                  columns={studentColumns}
                  dataSource={filteredStudentsData}
                  rowKey="id"
                  rowSelection={rowSelection}
                  pagination={{
                    total: filteredStudentsData.length,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }}
                  scroll={{ x: 800 }}
                  locale={{
                    emptyText: error ? '数据获取失败，请点击上方重新获取按钮' :
                              searchKeyword || selectedClass || selectedStatus ? '没有符合条件的学生数据' : '暂无学生数据'
                  }}
                />
              </Spin>
            </Card>
                )
              },
              {
                key: 'comments',
                label: '评语管理',
                children: (
            <Card>
              {/* 数据连接状态提示 */}
              {error && (
                <Alert
                  message="数据获取失败"
                  description={error}
                  type="error"
                  showIcon
                  className="mb-4"
                  action={
                    <Button size="small" onClick={fetchCommentsData}>
                      重新获取
                    </Button>
                  }
                />
              )}
              
              {/* 搜索和操作栏 */}
              <div className="flex flex-wrap justify-between items-center gap-4 mb-4">
                <Space wrap size="middle">
                  <Search
                    placeholder="搜索学生或内容"
                    style={{ width: 200 }}
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    allowClear
                  />
                  <Select
                    placeholder="选择班级"
                    style={{ width: 120 }}
                    value={selectedCommentClass}
                    onChange={setSelectedCommentClass}
                    allowClear
                  >
                    {classList.map(cls => (
                      <Option key={cls.id} value={cls.id}>{cls.name}</Option>
                    ))}
                  </Select>
                  <Select
                    placeholder="选择教师"
                    style={{ width: 120 }}
                    value={selectedTeacher}
                    onChange={setSelectedTeacher}
                    allowClear
                  >
                    {teacherList.map(teacher => (
                      <Option key={teacher.id} value={teacher.id}>{teacher.name}</Option>
                    ))}
                  </Select>
                  <Select
                    placeholder="评语类型"
                    style={{ width: 120 }}
                    value={selectedCommentType}
                    onChange={setSelectedCommentType}
                    allowClear
                  >
                    {commentTypeList.map(type => (
                      <Option key={type.id} value={type.id}>{type.name}</Option>
                    ))}
                  </Select>
                </Space>

                <Space wrap size="small">
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={handleExport}
                    size="middle"
                  >
                    导出评语
                  </Button>
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    onClick={handleBatchDelete}
                    disabled={selectedRowKeys.length === 0}
                    size="middle"
                  >
                    批量删除
                  </Button>
                </Space>
              </div>

              <Spin spinning={loading} tip="正在获取数据...">
                <Table
                  columns={commentColumns}
                  dataSource={filteredCommentsData}
                  rowKey="id"
                  rowSelection={rowSelection}
                  pagination={{
                    total: filteredCommentsData.length,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }}
                  scroll={{ x: 1000 }}
                  locale={{
                    emptyText: error ? '数据获取失败，请点击上方重新获取按钮' :
                              searchKeyword ? '没有符合条件的评语数据' : '暂无评语数据'
                  }}
                />
              </Spin>
            </Card>
                )
              }
            ]}
          />
        </div>
      </div>

      {/* 详情抽屉 */}
      <Drawer
        title="详细信息"
        placement="right"
        size="large"
        onClose={() => setDetailDrawerVisible(false)}
        open={detailDrawerVisible}
      >
        {selectedRecord && (
          <Descriptions column={1} bordered>
            {selectedRecord.name && (
              <Descriptions.Item label="学生姓名">
                {selectedRecord.name}
              </Descriptions.Item>
            )}
            {selectedRecord.studentName && (
              <Descriptions.Item label="学生姓名">
                {selectedRecord.studentName}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="班级">
              {selectedRecord.class || selectedRecord.className}
            </Descriptions.Item>
            <Descriptions.Item label="教师">
              {selectedRecord.teacher}
            </Descriptions.Item>
            {selectedRecord.content && (
              <Descriptions.Item label="评语内容">
                {selectedRecord.content}
              </Descriptions.Item>
            )}
            {selectedRecord.type && (
              <Descriptions.Item label="评语类型">
                <Tag color="blue">{selectedRecord.type}</Tag>
              </Descriptions.Item>
            )}
            {selectedRecord.commentsCount && (
              <Descriptions.Item label="评语数量">
                {selectedRecord.commentsCount}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="更新时间">
              {selectedRecord.lastUpdate || selectedRecord.createTime}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Drawer>

      {/* 编辑模态框 */}
      <Modal
        title={activeTab === 'students' ? '编辑学生信息' : '编辑评语信息'}
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false)
          editForm.resetFields()
          setEditingRecord(null)
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setEditModalVisible(false)
            editForm.resetFields()
            setEditingRecord(null)
          }}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={() => editForm.submit()}>
            保存
          </Button>
        ]}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditSubmit}
        >
          {activeTab === 'students' ? (
            // 学生编辑表单 - 与数据库字段保持一致
            <>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="姓名"
                    name="name"
                    rules={[{ required: true, message: '请输入姓名' }]}
                  >
                    <Input placeholder="请输入姓名" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="学号"
                    name="studentId"
                    rules={[{ required: true, message: '请输入学号' }]}
                  >
                    <Input placeholder="请输入学号" />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="班级"
                    name="className"
                    rules={[{ required: true, message: '请选择班级' }]}
                  >
                    <Select placeholder="请选择班级">
                      {classList.map(cls => (
                        <Option key={cls.id} value={cls.name}>{cls.name}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="性别"
                    name="gender"
                  >
                    <Select placeholder="请选择性别">
                      <Option value="male">男</Option>
                      <Option value="female">女</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="联系电话"
                    name="phone"
                  >
                    <Input placeholder="请输入联系电话" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="备注信息"
                    name="remark"
                  >
                    <Input placeholder="请输入备注信息" />
                  </Form.Item>
                </Col>
              </Row>
            </>
          ) : (
            // 评语编辑表单
            <>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="学生姓名"
                    name="studentName"
                    rules={[{ required: true, message: '请输入学生姓名' }]}
                  >
                    <Input placeholder="请输入学生姓名" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="班级"
                    name="class"
                    rules={[{ required: true, message: '请选择班级' }]}
                  >
                    <Select placeholder="请选择班级">
                      <Option value="三年级一班">三年级一班</Option>
                      <Option value="三年级二班">三年级二班</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item
                label="评语内容"
                name="content"
                rules={[{ required: true, message: '请输入评语内容' }]}
              >
                <TextArea rows={4} placeholder="请输入评语内容" />
              </Form.Item>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="教师"
                    name="teacher"
                  >
                    <Input placeholder="请输入教师姓名" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="AI模型"
                    name="aiModel"
                  >
                    <Select placeholder="请选择AI模型">
                      <Option value="GPT-3.5">GPT-3.5</Option>
                      <Option value="GPT-4">GPT-4</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </>
          )}
        </Form>
      </Modal>
    </div>
  )
}

export default DataManagement