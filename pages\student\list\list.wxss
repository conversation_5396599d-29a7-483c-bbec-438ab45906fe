/* 学生管理页面 - 莫兰迪设计风格 */
.morandi-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  padding: 40rpx 32rpx;
  padding-bottom: 120rpx;
  /* 优化滚动体验 */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 页面标题 */
.page-header {
  margin-bottom: 48rpx;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 24rpx;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 64rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #2C3E50 0%, #34495E 50%, #5470C6 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.1);
}

.page-subtitle {
  font-size: 28rpx;
  color: #73C0DE;
  font-weight: 600;
  letter-spacing: 2rpx;
  opacity: 0.9;
}

/* 头部操作按钮 */
.header-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 8rpx;
}

.header-action-btn {
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  min-width: 120rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.clear-btn {
  background: linear-gradient(135deg, #EE6666 0%, #FF8A80 100%);
  border: 1rpx solid rgba(238, 102, 102, 0.2);
}

.clear-btn:active {
  transform: translateY(2rpx);
  background: linear-gradient(135deg, #E53E3E 0%, #FC8181 100%);
  box-shadow: 0 2rpx 8rpx rgba(238, 102, 102, 0.3);
}

.action-btn-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 1rpx;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 32rpx;
}

.search-card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 24rpx;
  padding: 0;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  gap: 16rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #73C0DE;
}

.search-input {
  flex: 1;
  font-size: 30rpx;
  color: #2C3E50;
  background: transparent;
  border: none;
  outline: none;
}

.search-input::placeholder {
  color: rgba(115, 192, 222, 0.6);
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: rgba(115, 192, 222, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.search-clear.show {
  opacity: 1;
}

.clear-icon {
  font-size: 24rpx;
  color: #73C0DE;
  font-weight: 600;
}



/* 统计信息 - 复用首页样式 */
.stats-section-clean {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.stats-title-clean {
  font-size: 40rpx;
  font-weight: 600;
  color: #2C3E50;
  text-align: left;
  margin-bottom: 32rpx;
  letter-spacing: 1rpx;
}

.stats-row-clean {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-item-clean {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number-clean {
  font-size: 56rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8rpx;
  letter-spacing: 1rpx;
}

.stat-label-clean {
  font-size: 24rpx;
  color: #909399;
  font-weight: 500;
  letter-spacing: 1rpx;
}

.stat-divider {
  width: 2rpx;
  height: 80rpx;
  background: linear-gradient(180deg, transparent 0%, rgba(84, 112, 198, 0.2) 50%, transparent 100%);
  margin: 0 24rpx;
}

/* 学生列表 */
.student-list-container {
  margin-bottom: 32rpx;
}

.student-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 侧滑菜单容器 */
.student-swipe-item {
  position: relative;
  overflow: hidden;
  border-radius: 32rpx;
}

/* 侧滑操作按钮 - 真正的侧滑实现 */
.swipe-actions {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 320rpx;
  display: flex;
  z-index: 1;
}

.swipe-action-btn {
  width: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #2C3E50;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.edit-btn {
  background: linear-gradient(135deg, rgba(84, 112, 198, 0.6) 0%, rgba(115, 192, 222, 0.7) 100%);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.delete-btn {
  background: linear-gradient(135deg, rgba(180, 120, 120, 0.8) 0%, rgba(200, 140, 140, 0.9) 100%);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 32rpx;
  border-bottom-right-radius: 32rpx;
}



.swipe-action-text {
  font-size: 32rpx;
  letter-spacing: 1rpx;
  font-weight: 600;
  opacity: 0.9;
}

.swipe-action-btn:active {
  transform: scale(0.95);
}

.student-card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 2;
  width: 100%;
  box-sizing: border-box;
  /* 确保与凹形按钮完美对接 */
  overflow: visible;
  /* 优化触摸体验 */
  touch-action: pan-y; /* 只允许垂直滚动，除非被JavaScript处理 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 添加微妙的右侧渐变暗示，提示可以左滑 */
.student-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20rpx;
  height: 100%;
  background: linear-gradient(to left, rgba(84, 112, 198, 0.03) 0%, transparent 100%);
  border-radius: 0 32rpx 32rpx 0;
  pointer-events: none;
}

.student-card:active {
  box-shadow: 0 8rpx 32rpx rgba(84, 112, 198, 0.15);
}

.student-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.student-avatar-wrapper {
  margin-right: 24rpx;
}

.student-avatar-compact {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(84, 112, 198, 0.2);
}

.avatar-image-compact {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-placeholder-compact {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text-compact {
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.student-main-info {
  flex: 1;
}

.student-name-compact {
  font-size: 36rpx;
  font-weight: 700;
  color: #2C3E50;
  margin-bottom: 8rpx;
  letter-spacing: 1rpx;
}

.student-class-number-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.student-class-compact {
  font-size: 24rpx;
  color: #73C0DE;
  font-weight: 600;
}

.student-number-compact {
  font-size: 22rpx;
  color: #909399;
  font-weight: 500;
}



.student-card-stats {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.mini-stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 12rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.05);
}

.mini-stat-number {
  font-size: 28rpx;
  font-weight: 700;
  color: #5470C6;
  margin-bottom: 6rpx;
}

.mini-stat-time {
  font-size: 18rpx;
  font-weight: 500;
  color: #73C0DE;
  margin-bottom: 6rpx;
  text-align: center;
}

.mini-stat-label {
  font-size: 18rpx;
  color: #909399;
  font-weight: 500;
}

/* 空状态 */
.empty-state-clean {
  text-align: center;
  padding: 120rpx 40rpx;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.empty-icon-clean {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title-clean {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 16rpx;
  display: block;
}

.empty-desc-clean {
  font-size: 28rpx;
  color: #909399;
  line-height: 1.5;
  display: block;
}

/* 悬浮按钮 - 统一样式 */
.create-fab {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 32rpx rgba(84, 112, 198, 0.35);
  z-index: 100;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
}

.create-fab:active {
  transform: scale(0.9) translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(84, 112, 198, 0.4);
}

.fab-icon {
  font-size: 52rpx;
  color: white;
  font-weight: 300;
  line-height: 1;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}
