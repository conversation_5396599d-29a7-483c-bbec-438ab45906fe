import { apiClient } from './apiClient'
import { env } from '@/utils/env'
import cloudFunctionService from './cloudFunctionService'
import { settingsService } from './settingsService'

interface LoginCredentials {
  username: string
  password: string
}

interface User {
  id: string
  name: string
  username: string
  email?: string
  role: string
  permissions: string[]
  status: string
  profile?: {
    name: string
    avatar?: string
    phone?: string
    department?: string
  }
}

interface LoginResponse {
  user: User
  token: string
}

interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

export const authApi = {
  /**
   * 管理员登录
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // 如果启用模拟模式，使用模拟数据
      if (env.ENABLE_MOCK) {
        return await this.mockLogin(credentials)
      }

      // 使用云数据库登录
      const loginResult = await settingsService.adminLogin(credentials.username, credentials.password)

      if (loginResult.success && loginResult.data) {
        // 转换为标准的LoginResponse格式
        const user: User = {
          id: loginResult.data.id,
          name: '系统管理员',
          username: loginResult.data.username,
          role: loginResult.data.role,
          permissions: loginResult.data.role === 'super_admin' ? ['*'] : ['read', 'write'],
          status: 'active',
          profile: {
            name: '系统管理员',
            department: '系统管理部'
          }
        }

        return {
          user,
          token: loginResult.data.token
        }
      } else {
        throw new Error(loginResult.message || '登录失败')
      }
    } catch (error: any) {
      console.error('Login error:', error)

      if (error.message) {
        throw new Error(error.message)
      } else {
        throw new Error('登录失败，请检查网络连接')
      }
    }
  },

  /**
   * 退出登录
   */
  async logout(): Promise<void> {
    try {
      if (env.ENABLE_MOCK) {
        // 模拟退出登录
        await new Promise(resolve => setTimeout(resolve, 500))
        return
      }

      // 真实API调用
      await apiClient.post('', {
        action: 'auth.logout',
        timestamp: Date.now(),
        requestId: `logout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })
    } catch (error) {
      console.warn('Logout error:', error)
      // 即使退出登录失败，也不阻止用户
    }
  },

  /**
   * 验证Token有效性
   */
  async validateToken(token: string): Promise<boolean> {
    try {
      if (env.ENABLE_MOCK) {
        // 模拟Token验证
        return Promise.resolve(!!token && token.startsWith('mock_jwt_token_'))
      }

      if (!token) {
        return false
      }

      // 真实API调用
      const response = await apiClient.post<ApiResponse<{ valid: boolean }>>('', {
        action: 'auth.validateToken',
        token,
        timestamp: Date.now(),
        requestId: `validate_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      return response.data.code === 200 && response.data.data.valid
    } catch (error) {
      console.warn('Token validation error:', error)
      return false
    }
  },

  /**
   * 刷新Token
   */
  async refreshToken(): Promise<{ token: string; user: User }> {
    try {
      if (env.ENABLE_MOCK) {
        // 模拟Token刷新
        return {
          token: 'mock_jwt_token_' + Date.now(),
          user: {
            id: '1',
            name: '系统管理员',
            username: 'admin',
            email: '<EMAIL>',
            role: 'super_admin',
            permissions: ['*'],
            status: 'active'
          }
        }
      }

      // 真实API调用
      const response = await apiClient.post<ApiResponse<{ token: string; user: User }>>('', {
        action: 'auth.refreshToken',
        timestamp: Date.now(),
        requestId: `refresh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      if (response.data.code !== 200) {
        throw new Error(response.data.message || 'Token刷新失败')
      }

      return response.data.data
    } catch (error: any) {
      console.error('Token refresh error:', error)
      throw new Error(error.response?.data?.message || error.message || 'Token刷新失败')
    }
  },

  /**
   * 获取用户信息
   */
  async getUserInfo(): Promise<User> {
    try {
      if (env.ENABLE_MOCK) {
        // 模拟用户信息
        return {
          id: '1',
          name: '系统管理员',
          username: 'admin',
          email: '<EMAIL>',
          role: 'super_admin',
          permissions: ['*'],
          status: 'active',
          profile: {
            name: '系统管理员',
            department: '系统管理部'
          }
        }
      }

      // 真实API调用
      const response = await apiClient.post<ApiResponse<User>>('', {
        action: 'auth.getUserInfo',
        timestamp: Date.now(),
        requestId: `userinfo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      if (response.data.code !== 200) {
        throw new Error(response.data.message || '获取用户信息失败')
      }

      return response.data.data
    } catch (error: any) {
      console.error('Get user info error:', error)
      throw new Error(error.response?.data?.message || error.message || '获取用户信息失败')
    }
  },

  /**
   * 检查是否已有管理员
   */
  async checkAdminExists(): Promise<{ hasAdmin: boolean; count: number }> {
    try {
      // 真实API调用（不使用模拟数据，这个接口需要真实检查）
      const response = await apiClient.post<ApiResponse<{ hasAdmin: boolean; count: number }>>('', {
        action: 'auth.checkAdminExists',
        timestamp: Date.now(),
        requestId: `check_admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      if (response.data.code !== 200) {
        throw new Error(response.data.message || '检查管理员状态失败')
      }

      return response.data.data
    } catch (error: any) {
      console.error('Check admin exists error:', error)
      // 如果检查失败，默认认为已有管理员（安全策略）
      return { hasAdmin: true, count: 1 }
    }
  },

  /**
   * 初始化管理员账户
   */
  async initAdmin(adminData: {
    username: string
    password: string
    email?: string
    profile?: {
      name?: string
      phone?: string
      department?: string
    }
  }): Promise<{ id: string; username: string; message: string }> {
    try {
      // 真实API调用（不使用模拟数据）
      const response = await apiClient.post<ApiResponse<{ id: string; username: string; message: string }>>('', {
        action: 'auth.initAdmin',
        ...adminData,
        timestamp: Date.now(),
        requestId: `init_admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      if (response.data.code !== 200) {
        throw new Error(response.data.message || '初始化管理员失败')
      }

      return response.data.data
    } catch (error: any) {
      console.error('Init admin error:', error)
      throw new Error(error.response?.data?.message || error.message || '初始化管理员失败')
    }
  },

  /**
   * 模拟登录（开发和测试用）
   */
  async mockLogin(credentials: LoginCredentials): Promise<LoginResponse> {
    console.log('🔐 使用模拟登录:', credentials.username)

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    // 验证预设的管理员账户
    const validCredentials = [
      { username: 'admin', password: 'admin123' },
      { username: 'administrator', password: 'admin123' },
      { username: 'root', password: 'admin123' },
      { username: 'manager', password: 'manager123' }
    ]

    const isValid = validCredentials.some(
      cred => cred.username === credentials.username && cred.password === credentials.password
    )

    if (!isValid) {
      throw new Error('用户名或密码错误')
    }

    // 返回模拟的用户数据
    return {
      user: {
        id: '1',
        name: '系统管理员',
        username: credentials.username,
        email: '<EMAIL>',
        role: 'super_admin',
        permissions: ['*'],
        status: 'active',
        profile: {
          name: '系统管理员',
          department: '系统管理部'
        }
      },
      token: 'mock_jwt_token_' + Date.now()
    }
  }
}

// 云函数调用辅助函数 - 使用真实的云函数服务
async function callCloudFunction(name: string, data: any) {
  try {
    console.log(`[CloudFunction] 调用 ${name}:`, data)
    
    const result = await cloudFunctionService.callFunction(name, data)
    console.log(`[CloudFunction] ${name} 原始返回结果:`, result)
    console.log(`[CloudFunction] result.code:`, result.code)
    console.log(`[CloudFunction] result.data:`, result.data)
    console.log(`[CloudFunction] result.message:`, result.message)
    
    // 转换响应格式以保持兼容性
    if (result.code === 0) {
      const normalizedResult = {
        success: true,
        data: result.data,
        message: result.message
      }
      console.log(`[CloudFunction] ${name} 标准化后结果:`, normalizedResult)
      return normalizedResult
    } else {
      const errorResult = {
        success: false,
        error: result.message || '云函数调用失败',
        data: result.data
      }
      console.log(`[CloudFunction] ${name} 错误结果:`, errorResult)
      return errorResult
    }
  } catch (error) {
    console.error(`[CloudFunction] ${name} 调用异常:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }
  }
}

/**
 * 获取AI模型配置
 */
export const getAIModels = async () => {
  return mockRequest(() => ({
    models: [
      {
        id: '1',
        name: '豆包模型',
        provider: 'bytedance',
        model: 'doubao-pro-32k',
        status: 'active',
        usage: 1234,
        cost: 45.67
      }
    ]
  }))
}

/**
 * 获取提示词模板
 */
export const getPromptTemplates = async () => {
  try {
    console.log('🚀 调用 managePromptTemplates 云函数...')
    
    const result = await callCloudFunction('managePromptTemplates', {
      action: 'getAll'
    })
    
    console.log('🔍 云函数调用结果:', result)
    console.log('🔍 result.success:', result.success)
    console.log('🔍 result.data:', result.data)
    console.log('🔍 result.data type:', typeof result.data)
    console.log('🔍 result.data length:', Array.isArray(result.data) ? result.data.length : 'not array')
    
    // 处理云函数返回的数据格式
    let templateData = null
    
    if (result.success) {
      // 可能的数据结构：
      // 1. result.data 直接是数组
      // 2. result.data 是对象，其中 result.data.data 是数组
      // 3. result.data 是对象，其中 result.data 本身包含模板数据
      
      if (Array.isArray(result.data)) {
        console.log('📋 数据格式：直接数组')
        templateData = result.data
      } else if (result.data && Array.isArray(result.data.data)) {
        console.log('📋 数据格式：嵌套对象.data')
        templateData = result.data.data
      } else if (result.data && result.data.success && Array.isArray(result.data.data)) {
        console.log('📋 数据格式：双重嵌套')
        templateData = result.data.data
      }
      
      console.log('🔍 解析后的模板数据:', templateData)
      console.log('🔍 模板数量:', templateData ? templateData.length : 0)
    }
    
    if (templateData && templateData.length > 0) {
      console.log('✅ 使用云函数返回的模板数据')
      return {
        success: true,
        data: templateData
      }
    } else {
      console.log('⚠️ 云函数返回数据为空，使用硬编码备选模板')
      // 返回前端硬编码的模板作为备选
      return {
        success: true,
        data: [
          {
            id: '1',
            name: '温和亲切型',
            type: 'gentle',
            description: '充满关爱、如沐春风般温暖的评语',
            status: 'active',
            usage: 1256
          },
          {
            id: '2',
            name: '鼓励激励型',
            type: 'encouraging',
            description: '富有激情、能点燃学生内心斗志的评语',
            status: 'active',
            usage: 894
          },
          {
            id: '3',
            name: '详细具体型',
            type: 'detailed',
            description: '如"成长档案"般客观、精准的评语',
            status: 'active',
            usage: 567
          },
          {
            id: '4',
            name: '综合发展型',
            type: 'comprehensive',
            description: '全面评价学生各方面发展的综合型评语',
            status: 'active',
            usage: 732
          },
          {
            id: '5',
            name: '正式规范型',
            type: 'formal',
            description: '专业、规范、充满关怀的传统评语模式',
            status: 'active',
            usage: 456
          }
        ]
      }
    }
  } catch (error) {
    console.error('获取提示词模板失败:', error)
    // 返回前端硬编码的模板作为备选（统一四种风格）
    return {
      success: true,
      data: [
        {
          id: '1',
          name: '🤗 温暖亲切',
          type: 'warm',
          description: '语气温和，拉近师生距离，充满关爱的评语',
          status: 'active',
          usage: 1256
        },
        {
          id: '2',
          name: '📋 正式规范',
          type: 'formal',
          description: '适合正式场合，用词规范，专业权威的评语',
          status: 'active',
          usage: 894
        },
        {
          id: '3',
          name: '💪 鼓励激励',
          type: 'encouraging',
          description: '积极正面，激发学生潜能，充满正能量的评语',
          status: 'active',
          usage: 567
        },
        {
          id: '4',
          name: '🔍 详细具体',
          type: 'detailed',
          description: '内容详实，分析深入，全面细致的评语',
          status: 'active',
          usage: 732
        }
      ]
    }
  }
}

/**
 * 更新提示词模板
 */
export const updatePromptTemplate = async (templateData: any) => {
  try {
    console.log('🔧 updatePromptTemplate 调用，参数:', templateData)
    
    const result = await callCloudFunction('managePromptTemplates', {
      action: 'update',
      data: templateData
    })
    
    console.log('🔧 updatePromptTemplate 云函数返回:', result)
    
    if (result.success) {
      console.log('✅ 模板更新成功')
      return {
        success: true,
        message: result.message || '模板更新成功'
      }
    } else {
      console.log('❌ 模板更新失败:', result.error)
      throw new Error(result.error || '更新失败')
    }
  } catch (error) {
    console.error('❌ updatePromptTemplate 异常:', error)
    // 模拟成功（暂时）
    return {
      success: true,
      message: '模板更新成功（模拟）'
    }
  }
}

/**
 * 创建提示词模板
 */
export const createPromptTemplate = async (templateData: any) => {
  try {
    const result = await callCloudFunction('managePromptTemplates', {
      action: 'create',
      data: templateData
    })
    
    if (result.success) {
      return {
        success: true,
        data: result.data,
        message: result.message || '模板创建成功'
      }
    } else {
      throw new Error(result.error || '创建失败')
    }
  } catch (error) {
    console.error('创建提示词模板失败:', error)
    return {
      success: false,
      error: error.message || '创建失败'
    }
  }
}

/**
 * 删除提示词模板
 */
export const deletePromptTemplate = async (templateId: string) => {
  try {
    const result = await callCloudFunction('managePromptTemplates', {
      action: 'delete',
      data: { id: templateId }
    })
    
    if (result.success) {
      return {
        success: true,
        message: result.message || '模板删除成功'
      }
    } else {
      throw new Error(result.error || '删除失败')
    }
  } catch (error) {
    console.error('删除提示词模板失败:', error)
    return {
      success: false,
      error: error.message || '删除失败'
    }
  }
}

/**
 * 初始化默认模板
 */
export const initializeDefaultTemplates = async () => {
  try {
    const result = await callCloudFunction('managePromptTemplates', {
      action: 'initDefaults'
    })
    
    if (result.success) {
      return {
        success: true,
        message: result.message || '默认模板初始化成功',
        count: result.count
      }
    } else {
      throw new Error(result.error || '初始化失败')
    }
  } catch (error) {
    console.error('初始化默认模板失败:', error)
    return {
      success: false,
      error: error.message || '初始化失败'
    }
  }
}

/**
 * 获取使用统计数据
 */
export const getUsageStats = async () => {
  try {
    // 直接调用存在的云函数
    const result = await callCloudFunction('getUsageStats', {
      action: 'getStats'
    })
    
    if (result.success || result.code === 0) {
      return result.data || result
    } else {
      throw new Error(result.error || result.message || '获取统计数据失败')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 返回默认数据
    return {
      totalUsers: 0,
      activeUsers: 0,
      todayComments: 0,
      totalComments: 0,
      todayAICalls: 0,
      totalTokens: 0,
      avgTokensPerCall: 0,
      lastUpdated: Date.now()
    }
  }
}

/**
 * 获取教师使用统计
 * 使用dataQuery云函数获取教师AI使用统计数据
 */
export const getTeacherUsageStats = async () => {
  try {
    console.log('🔍 开始获取教师使用统计...')

    // 优先使用dataQuery云函数
    const result = await callCloudFunction('dataQuery', {
      action: 'getTeacherUsage'
    })

    console.log('📊 教师统计云函数响应:', result)

    if (result.code === 200 && result.data) {
      // dataQuery返回的数据格式: { list: [], total: 0 }
      const teacherList = result.data.list || result.data
      return Array.isArray(teacherList) ? teacherList : []
    } else if (result.success && result.data) {
      // getUsageStats返回的数据格式: { success: true, data: [] }
      return Array.isArray(result.data) ? result.data : []
    } else {
      console.warn('教师统计数据格式异常，尝试备用方案')

      // 备用方案：使用getUsageStats云函数
      const fallbackResult = await callCloudFunction('getUsageStats', {
        action: 'getTeacherUsage'
      })

      if (fallbackResult.success && fallbackResult.data) {
        return Array.isArray(fallbackResult.data) ? fallbackResult.data : []
      }

      throw new Error(result.error || result.message || '获取教师统计失败')
    }
  } catch (error) {
    console.error('❌ 获取教师统计失败:', error)
    return []
  }
}

/**
 * 获取AI使用统计
 */
export const getAIUsageStats = async () => {
  try {
    const result = await callCloudFunction('getUsageStats', {
      action: 'getAIUsage'
    })
    
    if (result.success) {
      return result.data
    } else {
      throw new Error(result.error || '获取AI统计失败')
    }
  } catch (error) {
    console.error('获取AI统计失败:', error)
    return {
      todayHourly: [],
      weeklyTrend: [],
      templateUsage: []
    }
  }
}

// 模拟请求辅助函数
async function mockRequest<T>(dataProvider: () => T): Promise<T> {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 500))
  
  // 模拟偶发的网络错误（5%几率）
  if (Math.random() < 0.05) {
    throw new Error('网络连接超时，请重试')
  }
  
  return dataProvider()
}