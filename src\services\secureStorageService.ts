/**
 * 安全存储服务
 * 管理API密钥、敏感配置和令牌的安全存储
 */

import { SecureStorage } from '../utils/encryption'

export interface APIConfig {
  apiKey: string
  baseUrl: string
  modelName: string
}

export interface AIProviderConfig {
  doubao: APIConfig
  kimi: APIConfig
  deepseek: APIConfig
  qwen: APIConfig
}

export interface SystemConfig {
  theme: 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
  notifications: boolean
  autoSave: boolean
  metricsEnabled: boolean
}

/**
 * 配置键枚举
 */
export enum StorageKeys {
  AI_PROVIDERS = 'ai_providers',
  SYSTEM_CONFIG = 'system_config',
  JWT_TOKEN = 'jwt_token',
  REFRESH_TOKEN = 'refresh_token',
  API_CALLS_HISTORY = 'api_calls_history',
  USER_PREFERENCES = 'user_preferences',
  LAST_LOGIN_INFO = 'last_login_info',
  BACKUP_CONFIG = 'backup_config'
}

/**
 * API密钥管理器
 */
export class APIKeyManager {
  private static instance: APIKeyManager
  private readonly STORAGE_PREFIX = 'secure_config_'

  /**
   * 获取单例实例
   */
  static getInstance(): APIKeyManager {
    if (!APIKeyManager.instance) {
      APIKeyManager.instance = new APIKeyManager()
    }
    return APIKeyManager.instance
  }

  /**
   * 保存API配置
   */
  async saveAPIConfig(provider: keyof AIProviderConfig, config: APIConfig): Promise<void> {
    try {
      const currentConfigs = await this.getAllAPIConfigs()
      const updatedConfigs = {
        ...currentConfigs,
        [provider]: {
          ...config,
          apiKey: await this.encryptSensitive(config.apiKey)
        }
      }
      await SecureStorage.setItem(
        `${this.STORAGE_PREFIX}${StorageKeys.AI_PROVIDERS}`,
        JSON.stringify(updatedConfigs)
      )
    } catch (error) {
      console.error('Failed to save API config:', error)
      throw new Error('保存API配置失败')
    }
  }

  /**
   * 获取API配置
   */
  async getAPIConfig(provider: keyof AIProviderConfig): Promise<APIConfig | null> {
    try {
      const configs = await this.getAllAPIConfigs()
      const config = configs[provider]
      
      if (!config) return null

      return {
        ...config,
        apiKey: await this.decryptSensitive(config.apiKey)
      }
    } catch (error) {
      console.error('Failed to get API config:', error)
      return null
    }
  }

  /**
   * 获取所有API配置
   */
  async getAllAPIConfigs(): Promise<AIProviderConfig> {
    try {
      const data = await SecureStorage.getItem(
        `${this.STORAGE_PREFIX}${StorageKeys.AI_PROVIDERS}`
      )
      
      if (!data) {
        return this.getDefaultAPIConfigs()
      }

      const configs = JSON.parse(data)
      return configs
    } catch (error) {
      console.error('Failed to load API configs:', error)
      return this.getDefaultAPIConfigs()
    }
  }

  /**
   * 删除API配置
   */
  async deleteAPIConfig(provider: keyof AIProviderConfig): Promise<void> {
    try {
      const configs = await this.getAllAPIConfigs()
      delete configs[provider]
      
      await SecureStorage.setItem(
        `${this.STORAGE_PREFIX}${StorageKeys.AI_PROVIDERS}`,
        JSON.stringify(configs)
      )
    } catch (error) {
      console.error('Failed to delete API config:', error)
      throw new Error('删除API配置失败')
    }
  }

  /**
   * 验证API密钥格式
   */
  async validateAPIKey(apiKey: string): Promise<{
    isValid: boolean
    provider?: string
    error?: string
  }> {
    if (!apiKey || apiKey.length < 10) {
      return { isValid: false, error: 'API密钥格式不正确' }
    }

    // 根据不同provider验证格式
    if (apiKey.startsWith('sk-')) {
      return { isValid: true, provider: 'OpenAI' }
    }
    
    if (apiKey.length === 32 && /^[a-zA-Z0-9]+$/.test(apiKey)) {
      return { isValid: true, provider: '通用' }
    }

    return { isValid: false, error: 'API密钥格式不符合要求' }
  }

  /**
   * 获取默认API配置
   */
  private getDefaultAPIConfigs(): AIProviderConfig {
    return {
      doubao: {
        apiKey: '',
        baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
        modelName: 'ep-20241123002057-k6rr4'
      },
      kimi: {
        apiKey: '',
        baseUrl: 'https://api.moonshot.cn/v1',
        modelName: 'moonshot-v1-8k'
      },
      deepseek: {
        apiKey: '',
        baseUrl: 'https://api.deepseek.com/v1',
        modelName: 'deepseek-chat'
      },
      qwen: {
        apiKey: '',
        baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
        modelName: 'qwen2-turbo'
      }
    }
  }

  /**
   * 加密敏感信息
   */
  private async encryptSensitive(data: string): Promise<string> {
    const { encrypt } = await import('../utils/encryption')
    return await encrypt(data)
  }

  /**
   * 解密敏感信息
   */
  private async decryptSensitive(encrypted: string): Promise<string> {
    const { decrypt } = await import('../utils/encryption')
    return await decrypt(encrypted)
  }
}

/**
 * 系统配置管理器
 */
export class ConfigManager {
  private static instance: ConfigManager
  private readonly STORAGE_PREFIX = 'secure_config_'

  /**
   * 获取单例实例
   */
  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager()
    }
    return ConfigManager.instance
  }

  /**
   * 保存系统配置
   */
  async saveSystemConfig(config: Partial<SystemConfig>): Promise<void> {
    try {
      const currentConfig = await this.getSystemConfig()
      const updatedConfig = { ...currentConfig, ...config }
      
      await SecureStorage.setItem(
        `${this.STORAGE_PREFIX}${StorageKeys.SYSTEM_CONFIG}`,
        JSON.stringify(updatedConfig)
      )
    } catch (error) {
      console.error('Failed to save system config:', error)
      throw new Error('保存系统配置失败')
    }
  }

  /**
   * 获取系统配置
   */
  async getSystemConfig(): Promise<SystemConfig> {
    try {
      const data = await SecureStorage.getItem(
        `${this.STORAGE_PREFIX}${StorageKeys.SYSTEM_CONFIG}`
      )
      
      if (!data) {
        return this.getDefaultSystemConfig()
      }

      return JSON.parse(data)
    } catch (error) {
      console.error('Failed to load system config:', error)
      return this.getDefaultSystemConfig()
    }
  }

  /**
   * 清除所有系统配置
   */
  async clearSystemConfig(): Promise<void> {
    try {
      await SecureStorage.removeItem(
        `${this.STORAGE_PREFIX}${StorageKeys.SYSTEM_CONFIG}`
      )
    } catch (error) {
      console.error('Failed to clear system config:', error)
      throw new Error('清除系统配置失败')
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultSystemConfig(): SystemConfig {
    return {
      theme: 'light',
      language: 'zh-CN',
      notifications: true,
      autoSave: true,
      metricsEnabled: true
    }
  }

  /**
   * 导出配置
   */
  async exportConfig(): Promise<string> {
    try {
      const apiConfigs = await APIKeyManager.getInstance().getAllAPIConfigs()
      const systemConfig = await this.getSystemConfig()
      
      const exportData = {
        timestamp: new Date().toISOString(),
        api_configs: apiConfigs,
        system_config: systemConfig,
        version: import.meta.env.VITE_APP_VERSION || '2.0.0'
      }

      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('Failed to export config:', error)
      throw new Error('导出配置失败')
    }
  }

  /**
   * 导入配置
   */
  async importConfig(configJson: string): Promise<void> {
    try {
      const data = JSON.parse(configJson)
      
      if (data.api_configs) {
        for (const [provider, config] of Object.entries(data.api_configs)) {
          await APIKeyManager.getInstance().saveAPIConfig(
            provider as keyof AIProviderConfig,
            config as APIConfig
          )
        }
      }
      
      if (data.system_config) {
        await this.saveSystemConfig(data.system_config)
      }
    } catch (error) {
      console.error('Failed to import config:', error)
      throw new Error('导入配置失败')
    }
  }
}

/**
 * 缓存管理器
 */
export class CacheManager {
  private static instance: CacheManager
  private cacheMap = new Map<string, { data: any; expires: number }>()

  /**
   * 获取单例实例
   */
  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager()
    }
    return CacheManager.instance
  }

  /**
   * 存储缓存数据
   */
  async set<T>(key: string, data: T, ttl: number = 300000): Promise<void> {
    const expires = Date.now() + ttl
    this.cacheMap.set(key, { data, expires })
  }

  /**
   * 获取缓存数据
   */
  async get<T>(key: string): Promise<T | null> {
    const cached = this.cacheMap.get(key)
    
    if (!cached) return null
    
    if (cached.expires < Date.now()) {
      this.cacheMap.delete(key)
      return null
    }

    return cached.data
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<void> {
    this.cacheMap.delete(key)
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    this.cacheMap.clear()
  }

  /**
   * 清理过期缓存
   */
  async cleanup(): Promise<void> {
    const now = Date.now()
    for (const [key, cached] of this.cacheMap.entries()) {
      if (cached.expires < now) {
        this.cacheMap.delete(key)
      }
    }
  }
}

// 导出名空间
export const SecureStorageService = {
  apiKeys: APIKeyManager.getInstance(),
  config: ConfigManager.getInstance(),
  cache: CacheManager.getInstance()
}

export default SecureStorageService