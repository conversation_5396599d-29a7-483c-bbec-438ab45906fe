<!--
  学生行为记录管理页面
-->
<view class="record-list-page">
  <!-- 头部操作区 -->
  <view class="header-section">
    <view class="page-title">
      <text class="title-text">记录管理</text>
      <text class="title-desc">查看和管理学生行为记录</text>
    </view>

    <!-- 操作按钮 -->
    <view class="header-actions">
      <view class="action-item filter-action" bindtap="showFilter">
        <view class="item-icon">
          <van-icon name="filter-o" size="20px" color="#4080FF" />
        </view>
        <text class="item-text">筛选</text>
        <view wx:if="{{hasActiveFilter}}" class="filter-dot"></view>
      </view>

      <view class="action-item export-action" bindtap="exportRecords">
        <view class="item-icon">
          <van-icon name="down" size="20px" color="#52C873" />
        </view>
        <text class="item-text">导出</text>
      </view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stat-item">
        <view class="stat-number">{{statistics.totalRecords}}</view>
        <view class="stat-label">总记录数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{statistics.todayRecords}}</view>
        <view class="stat-label">今日记录</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{statistics.avgScore}}</view>
        <view class="stat-label">平均评分</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{statistics.positiveRate}}%</view>
        <view class="stat-label">积极率</view>
      </view>
    </view>
  </view>

  <!-- 记录列表 -->
  <view class="record-list-container">
    <!-- 加载状态 -->
    <van-loading wx:if="{{loading && recordList.length === 0}}" type="spinner" color="#4080FF">
      加载中...
    </van-loading>

    <!-- 记录列表 -->
    <view wx:if="{{recordList.length > 0}}" class="record-list">
      <view
        wx:for="{{recordList}}"
        wx:key="id"
        class="record-item"
        data-record="{{item}}"
        bindtap="onRecordTap"
      >
        <view class="record-header">
          <view class="student-info">
            <view class="student-avatar">
              <text class="avatar-text">{{item.studentName.charAt(0)}}</text>
            </view>
            <view class="student-details">
              <view class="student-name">{{item.studentName}}</view>
              <view class="student-class">{{item.className}}</view>
            </view>
          </view>

          <view class="record-score" style="color: {{item.scoreColor}}">
            <text class="score-text">{{item.score}}分</text>
          </view>
        </view>

        <view class="record-content">
          <view class="behavior-info">
            <view class="behavior-type {{item.behaviorType}}">
              <text class="type-text">{{item.behaviorTypeText}}</text>
            </view>
            <view class="behavior-action">{{item.action}}</view>
          </view>

          <view wx:if="{{item.description}}" class="record-description">
            {{item.description}}
          </view>
        </view>

        <view class="record-footer">
          <view class="record-time">
            <van-icon name="clock-o" size="12px" color="#999" />
            <text class="time-text">{{item.createTimeText}}</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view wx:if="{{hasMore}}" class="load-more">
        <van-loading wx:if="{{loading}}" type="spinner" size="20px" color="#4080FF">
          加载中...
        </van-loading>
        <text wx:else class="load-more-text">上拉加载更多</text>
      </view>

      <view wx:else class="no-more">
        <text class="no-more-text">没有更多记录了</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <van-empty description="暂无记录数据">
        <van-button
          type="primary"
          size="small"
          bind:click="addRecord"
        >
          新增记录
        </van-button>
      </van-empty>
    </view>
  </view>
  <!-- 筛选面板 -->
  <van-popup
    show="{{showFilterSheet}}"
    position="bottom"
    round
    bind:close="hideFilter"
  >
    <view class="filter-panel">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <van-button
          type="default"
          size="mini"
          bind:click="resetFilter"
        >
          重置
        </van-button>
      </view>

      <view class="filter-content">
        <!-- 班级筛选 -->
        <view class="filter-group">
          <view class="filter-label">班级</view>
          <van-field
            value="{{filterOptions.classId}}"
            placeholder="选择班级"
            readonly
            is-link
            data-field="classId"
            bind:click="onFilterChange"
          />
        </view>

        <!-- 学生筛选 -->
        <view class="filter-group">
          <view class="filter-label">学生</view>
          <van-field
            value="{{filterOptions.studentId}}"
            placeholder="选择学生"
            readonly
            is-link
            data-field="studentId"
            bind:click="onFilterChange"
          />
        </view>

        <!-- 行为类型筛选 -->
        <view class="filter-group">
          <view class="filter-label">行为类型</view>
          <van-field
            value="{{filterOptions.behaviorType}}"
            placeholder="选择行为类型"
            readonly
            is-link
            data-field="behaviorType"
            bind:click="onFilterChange"
          />
        </view>
      </view>

      <view class="filter-footer">
        <van-button
          type="primary"
          block
          bind:click="applyFilter"
        >
          应用筛选
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- 操作面板 -->
  <van-action-sheet
    show="{{showActionSheet}}"
    actions="{{actionSheetActions}}"
    bind:close="hideActionSheet"
    bind:select="onActionSelect"
  />
</view>