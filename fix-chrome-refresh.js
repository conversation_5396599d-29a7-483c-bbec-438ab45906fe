/**
 * Chrome自动刷新问题修复脚本
 * 在管理后台页面的控制台中运行此脚本
 */

console.log('🔧 Chrome自动刷新修复脚本启动...');

// 1. 禁用Vite的HMR客户端自动重连
if (window.__vite_plugin_react_preamble_installed__) {
  console.log('🔄 检测到Vite React插件，尝试修复HMR...');
  
  // 重写WebSocket连接逻辑
  const originalWebSocket = window.WebSocket;
  window.WebSocket = function(url, protocols) {
    console.log('🌐 WebSocket连接尝试:', url);
    
    // 如果是HMR WebSocket，添加错误处理
    if (url.includes('8080') || url.includes('8081')) {
      const ws = new originalWebSocket(url, protocols);
      
      ws.addEventListener('error', (event) => {
        console.warn('⚠️ HMR WebSocket错误，但不刷新页面:', event);
        event.preventDefault();
        event.stopPropagation();
      });
      
      ws.addEventListener('close', (event) => {
        console.warn('⚠️ HMR WebSocket关闭，但不刷新页面:', event);
        // 不进行自动重连
      });
      
      return ws;
    }
    
    return new originalWebSocket(url, protocols);
  };
}

// 2. 拦截可能导致刷新的操作
const originalReload = window.location.reload;
window.location.reload = function(forcedReload) {
  console.warn('🚨 页面刷新被拦截！调用栈:', new Error().stack);
  
  // 询问用户是否真的要刷新
  if (confirm('检测到页面刷新请求，是否确认刷新？\n\n点击"取消"可以阻止自动刷新')) {
    originalReload.call(this, forcedReload);
  } else {
    console.log('✅ 用户取消了页面刷新');
  }
};

// 3. 监控并限制定时器
let timerCount = 0;
const originalSetInterval = window.setInterval;
const originalSetTimeout = window.setTimeout;

window.setInterval = function(callback, delay, ...args) {
  timerCount++;
  console.log(`⏰ 创建定时器 #${timerCount} (setInterval, ${delay}ms)`);
  
  // 如果定时器过多，发出警告
  if (timerCount > 10) {
    console.warn('⚠️ 定时器数量过多，可能导致性能问题');
  }
  
  return originalSetInterval.call(this, callback, delay, ...args);
};

window.setTimeout = function(callback, delay, ...args) {
  timerCount++;
  console.log(`⏰ 创建定时器 #${timerCount} (setTimeout, ${delay}ms)`);
  
  return originalSetTimeout.call(this, callback, delay, ...args);
};

// 4. 监控错误并阻止错误边界触发刷新
window.addEventListener('error', (event) => {
  console.warn('🚨 捕获到错误，但阻止自动刷新:', event.error);
  
  // 检查是否是React错误边界相关
  if (event.error && event.error.stack && event.error.stack.includes('ErrorBoundary')) {
    console.warn('🛡️ 检测到ErrorBoundary错误，阻止刷新');
    event.preventDefault();
  }
});

window.addEventListener('unhandledrejection', (event) => {
  console.warn('🚨 捕获到未处理的Promise拒绝:', event.reason);
  
  // 阻止可能导致刷新的Promise错误
  if (event.reason && typeof event.reason === 'string' && 
      (event.reason.includes('HMR') || event.reason.includes('WebSocket'))) {
    console.warn('🛡️ 检测到HMR相关错误，阻止刷新');
    event.preventDefault();
  }
});

// 5. 创建刷新监控面板
function createRefreshMonitor() {
  // 检查是否已存在
  if (document.getElementById('refresh-monitor')) {
    return;
  }
  
  const monitor = document.createElement('div');
  monitor.id = 'refresh-monitor';
  monitor.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    z-index: 10000;
    max-width: 300px;
  `;
  
  const startTime = Date.now();
  
  function updateMonitor() {
    const uptime = Math.floor((Date.now() - startTime) / 1000);
    const refreshCount = parseInt(localStorage.getItem('refreshCount') || '0');
    
    monitor.innerHTML = `
      <div>🔧 Chrome刷新修复已激活</div>
      <div>⏱️ 运行时长: ${uptime}秒</div>
      <div>🔄 历史刷新: ${refreshCount}次</div>
      <div>⏰ 活跃定时器: ${timerCount}</div>
      <button onclick="this.parentElement.remove()" style="margin-top: 5px; background: #ff4d4f; color: white; border: none; padding: 2px 5px; border-radius: 3px; cursor: pointer;">关闭</button>
    `;
  }
  
  updateMonitor();
  setInterval(updateMonitor, 1000);
  
  document.body.appendChild(monitor);
}

// 6. 检查并修复已知问题
function checkAndFix() {
  console.log('🔍 检查已知问题...');
  
  // 检查是否有过多的事件监听器
  const events = ['beforeunload', 'unload', 'pagehide'];
  events.forEach(eventType => {
    const listeners = getEventListeners ? getEventListeners(window)[eventType] : [];
    if (listeners && listeners.length > 2) {
      console.warn(`⚠️ ${eventType} 事件监听器过多: ${listeners.length}个`);
    }
  });
  
  // 检查localStorage中的问题数据
  try {
    const keys = Object.keys(localStorage);
    const problemKeys = keys.filter(key => 
      key.includes('refresh') || 
      key.includes('reload') || 
      key.includes('hmr')
    );
    
    if (problemKeys.length > 0) {
      console.warn('⚠️ 发现可能导致刷新的localStorage数据:', problemKeys);
    }
  } catch (e) {
    console.warn('⚠️ 无法检查localStorage:', e);
  }
}

// 7. 提供手动修复功能
window.fixChromeRefresh = {
  // 清除所有定时器
  clearAllTimers() {
    console.log('🧹 清除所有定时器...');
    for (let i = 1; i < 10000; i++) {
      clearInterval(i);
      clearTimeout(i);
    }
    timerCount = 0;
    console.log('✅ 定时器已清除');
  },
  
  // 重置刷新计数
  resetRefreshCount() {
    localStorage.setItem('refreshCount', '0');
    console.log('✅ 刷新计数已重置');
  },
  
  // 禁用所有自动刷新
  disableAutoRefresh() {
    this.clearAllTimers();
    
    // 尝试停止React的自动刷新
    if (window.__vite_plugin_react_preamble_installed__) {
      console.log('🛑 尝试禁用React自动刷新...');
    }
    
    console.log('✅ 自动刷新已禁用');
  },
  
  // 显示诊断信息
  diagnose() {
    console.log('📊 Chrome刷新问题诊断:');
    console.log('- 浏览器:', navigator.userAgent);
    console.log('- 当前URL:', window.location.href);
    console.log('- 定时器数量:', timerCount);
    console.log('- 刷新次数:', localStorage.getItem('refreshCount'));
    console.log('- Vite HMR:', !!window.__vite_plugin_react_preamble_installed__);
    
    checkAndFix();
  }
};

// 启动监控
createRefreshMonitor();
checkAndFix();

console.log('✅ Chrome自动刷新修复脚本已激活！');
console.log('💡 可用命令:');
console.log('  - fixChromeRefresh.clearAllTimers() // 清除所有定时器');
console.log('  - fixChromeRefresh.disableAutoRefresh() // 禁用自动刷新');
console.log('  - fixChromeRefresh.diagnose() // 诊断问题');
console.log('  - fixChromeRefresh.resetRefreshCount() // 重置刷新计数');
