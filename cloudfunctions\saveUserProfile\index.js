// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: process.env.ENV_ID || 'cloud1-4g85f8xlb8166ff1'
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()

  try {
    const { userInfo } = event

    console.log('保存用户资料:', userInfo)
    console.log('用户OpenID:', wxContext.OPENID)

    if (!userInfo) {
      return {
        success: false,
        error: '用户信息不能为空'
      }
    }

    // 准备要更新到users集合的数据
    const updateData = {
      updateTime: new Date()
    }

    // 如果有真实姓名，更新name字段
    if (userInfo.name && userInfo.name.trim()) {
      updateData.name = userInfo.name.trim()
      console.log('更新真实姓名:', updateData.name)
    }

    // 如果有微信昵称，更新nickName字段
    if (userInfo.nickName) {
      updateData.nickName = userInfo.nickName
    }

    // 如果有头像，更新avatarUrl字段
    if (userInfo.avatarUrl) {
      updateData.avatarUrl = userInfo.avatarUrl
    }

    // 其他用户信息字段
    if (userInfo.school) updateData.school = userInfo.school
    if (userInfo.subject) updateData.subject = userInfo.subject
    if (userInfo.grade) updateData.grade = userInfo.grade
    if (userInfo.phone) updateData.phone = userInfo.phone
    if (userInfo.email) updateData.email = userInfo.email

    // 🔥 关键修复：同时更新users集合中的用户信息
    try {
      const usersResult = await db.collection('users').doc(wxContext.OPENID).update({
        data: updateData
      })
      console.log('users集合更新成功:', usersResult)
    } catch (usersError) {
      console.warn('users集合更新失败，可能用户不存在:', usersError)
      // 如果用户不存在，尝试创建
      try {
        await db.collection('users').doc(wxContext.OPENID).set({
          data: {
            ...updateData,
            openid: wxContext.OPENID,
            createTime: new Date(),
            status: 'active'
          }
        })
        console.log('创建新用户记录成功')
      } catch (createError) {
        console.error('创建用户记录失败:', createError)
      }
    }

    // 保存用户信息到user_profiles集合作为备份
    const result = await db.collection('user_profiles').add({
      data: {
        openid: wxContext.OPENID,
        userInfo: userInfo,
        updateTime: new Date(),
        platform: 'miniprogram',
        version: '3.0.0'
      }
    })

    return {
      success: true,
      data: result,
      openid: wxContext.OPENID,
      message: '用户资料保存成功'
    }
  } catch (error) {
    console.error('保存用户信息失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}