// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: process.env.ENV_ID || 'cloud1-4g85f8xlb8166ff1'
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()

  try {
    const { userInfo } = event

    console.log('保存用户资料:', userInfo)
    console.log('用户OpenID:', wxContext.OPENID)

    if (!userInfo) {
      return {
        success: false,
        error: '用户信息不能为空'
      }
    }

    // 准备要更新到users集合的数据
    const updateData = {
      updateTime: new Date()
    }

    // 如果有真实姓名，更新name字段
    if (userInfo.name && userInfo.name.trim()) {
      updateData.name = userInfo.name.trim()
      console.log('更新真实姓名:', updateData.name)
    }

    // 如果有微信昵称，更新nickName字段
    if (userInfo.nickName) {
      updateData.nickName = userInfo.nickName
    }

    // 如果有头像，更新avatarUrl字段
    if (userInfo.avatarUrl) {
      updateData.avatarUrl = userInfo.avatarUrl
    }

    // 其他用户信息字段
    if (userInfo.school) updateData.school = userInfo.school
    if (userInfo.subject) updateData.subject = userInfo.subject
    if (userInfo.grade) updateData.grade = userInfo.grade
    if (userInfo.phone) updateData.phone = userInfo.phone
    if (userInfo.email) updateData.email = userInfo.email

    // 🔥 关键修复：同时更新users集合中的用户信息
    try {
      // 先检查用户是否存在
      const userExists = await db.collection('users').doc(wxContext.OPENID).get()

      if (userExists.data) {
        // 用户存在，更新信息
        const usersResult = await db.collection('users').doc(wxContext.OPENID).update({
          data: updateData
        })
        console.log('users集合更新成功:', usersResult)
      } else {
        // 用户不存在，创建新用户记录
        await db.collection('users').doc(wxContext.OPENID).set({
          data: {
            openid: wxContext.OPENID,
            nickName: userInfo.nickName || '微信用户',
            avatarUrl: userInfo.avatarUrl || '',
            name: userInfo.name || '', // 添加真实姓名字段
            school: userInfo.school || '',
            subject: userInfo.subject || '',
            grade: userInfo.grade || '',
            phone: userInfo.phone || '',
            email: userInfo.email || '',
            role: 'teacher',
            status: 'active',
            createTime: new Date(),
            updateTime: new Date(),
            loginCount: 1
          }
        })
        console.log('创建新用户记录成功，包含真实姓名')
      }
    } catch (usersError) {
      console.error('users集合操作失败:', usersError)
    }

    // 保存用户信息到user_profiles集合作为备份
    const result = await db.collection('user_profiles').add({
      data: {
        openid: wxContext.OPENID,
        userInfo: userInfo,
        updateTime: new Date(),
        platform: 'miniprogram',
        version: '3.0.0'
      }
    })

    return {
      success: true,
      data: result,
      openid: wxContext.OPENID,
      message: '用户资料保存成功'
    }
  } catch (error) {
    console.error('保存用户信息失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}