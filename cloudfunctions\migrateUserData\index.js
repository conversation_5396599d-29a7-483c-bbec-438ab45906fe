// 数据迁移云函数 - 为现有用户添加name字段
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    console.log('开始数据迁移：为现有用户添加name字段')
    
    // 获取所有用户
    const usersResult = await db.collection('users').get()
    console.log(`找到 ${usersResult.data.length} 个用户需要迁移`)
    
    let migratedCount = 0
    let errorCount = 0
    const errors = []
    
    // 批量处理用户数据
    for (const user of usersResult.data) {
      try {
        // 检查用户是否已经有name字段
        if (user.name !== undefined) {
          console.log(`用户 ${user._id} 已有name字段，跳过`)
          continue
        }
        
        // 准备更新数据
        const updateData = {
          name: '', // 添加空的name字段
          updateTime: new Date()
        }
        
        // 如果用户没有其他必要字段，也一并添加
        if (!user.school) updateData.school = ''
        if (!user.subject) updateData.subject = ''
        if (!user.grade) updateData.grade = ''
        if (!user.phone) updateData.phone = ''
        if (!user.email) updateData.email = ''
        
        // 更新用户记录
        await db.collection('users').doc(user._id).update({
          data: updateData
        })
        
        migratedCount++
        console.log(`用户 ${user._id} 迁移成功`)
        
        // 尝试从user_profiles集合获取真实姓名
        try {
          const profileResult = await db.collection('user_profiles')
            .where({
              openid: user._id
            })
            .orderBy('updateTime', 'desc')
            .limit(1)
            .get()
          
          if (profileResult.data.length > 0) {
            const profile = profileResult.data[0]
            if (profile.userInfo && profile.userInfo.name) {
              // 找到真实姓名，更新到users集合
              await db.collection('users').doc(user._id).update({
                data: {
                  name: profile.userInfo.name,
                  updateTime: new Date()
                }
              })
              console.log(`用户 ${user._id} 真实姓名同步成功: ${profile.userInfo.name}`)
            }
          }
        } catch (profileError) {
          console.warn(`获取用户 ${user._id} 的profile失败:`, profileError)
        }
        
      } catch (error) {
        errorCount++
        const errorMsg = `用户 ${user._id} 迁移失败: ${error.message}`
        console.error(errorMsg)
        errors.push(errorMsg)
      }
    }
    
    const result = {
      success: true,
      message: '数据迁移完成',
      statistics: {
        totalUsers: usersResult.data.length,
        migratedCount,
        errorCount,
        skippedCount: usersResult.data.length - migratedCount - errorCount
      },
      errors: errors.slice(0, 10) // 只返回前10个错误
    }
    
    console.log('数据迁移结果:', result)
    return result
    
  } catch (error) {
    console.error('数据迁移失败:', error)
    return {
      success: false,
      error: error.message,
      message: '数据迁移失败'
    }
  }
}
