<!--成长报告页面 - 严格按照原型图设计-->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-title">成长报告</view>
    <view class="header-action" bindtap="shareReport">分享报告</view>
  </view>

  <!-- 成长概览 -->
  <view class="growth-overview">
    <view class="overview-header">
      <view class="overview-title">本月成长概览</view>
      <view class="overview-period">{{currentTimeRange === 'week' ? '近7天' : currentTimeRange === 'month' ? '近30天' : '近3个月'}}</view>
    </view>
    
    <view class="overview-stats">
      <view class="overview-stat">
        <text class="stat-value">{{overview.totalComments || 0}}</text>
        <text class="stat-label">生成评语</text>
        <view class="stat-change" wx:if="{{overview.commentsGrowth > 0}}">↑ 比上期+{{overview.commentsGrowth}}%</view>
        <view class="stat-change negative" wx:elif="{{overview.commentsGrowth < 0}}">↓ 比上期{{overview.commentsGrowth}}%</view>
        <view class="stat-change" wx:else>- 暂无对比</view>
      </view>
      <view class="overview-stat">
        <text class="stat-value">{{overview.timeSaved || 0}}h</text>
        <text class="stat-label">节省时间</text>
        <view class="stat-change" wx:if="{{overview.timeGrowth > 0}}">↑ 比上期+{{overview.timeGrowth}}%</view>
        <view class="stat-change negative" wx:elif="{{overview.timeGrowth < 0}}">↓ 比上期{{overview.timeGrowth}}%</view>
        <view class="stat-change" wx:else>- 暂无对比</view>
      </view>
      <view class="overview-stat">
        <text class="stat-value">{{overview.avgQuality || 0}}</text>
        <text class="stat-label">平均质量</text>
        <view class="stat-change" wx:if="{{overview.qualityGrowth > 0}}">↑ 比上期+{{overview.qualityGrowth}}</view>
        <view class="stat-change negative" wx:elif="{{overview.qualityGrowth < 0}}">↓ 比上期{{overview.qualityGrowth}}</view>
        <view class="stat-change" wx:else>- 暂无对比</view>
      </view>
      <view class="overview-stat">
        <text class="stat-value">{{overview.excellentRate || 0}}%</text>
        <text class="stat-label">优秀率</text>
        <view class="stat-change" wx:if="{{overview.excellentGrowth > 0}}">↑ 比上期+{{overview.excellentGrowth}}%</view>
        <view class="stat-change negative" wx:elif="{{overview.excellentGrowth < 0}}">↓ 比上期{{overview.excellentGrowth}}%</view>
        <view class="stat-change" wx:else>- 暂无对比</view>
      </view>
    </view>
  </view>

  <!-- 效率统计 -->
  <view class="efficiency-section">
    <view class="section-title">
      <text class="section-icon">⚡</text>
      效率统计
    </view>
    
    <view class="efficiency-grid">
      <view class="efficiency-item">
        <view class="efficiency-icon">⚡</view>
        <view class="efficiency-number">{{efficiencyStats.avgTime || '0'}}</view>
        <view class="efficiency-label">平均生成时间(分钟)</view>
      </view>
      <view class="efficiency-item">
        <view class="efficiency-icon">🎯</view>
        <view class="efficiency-number">{{efficiencyStats.passRate || '0%'}}</view>
        <view class="efficiency-label">一次通过率</view>
      </view>
      <view class="efficiency-item">
        <view class="efficiency-icon">📈</view>
        <view class="efficiency-number">{{efficiencyStats.totalWords || '0'}}</view>
        <view class="efficiency-label">累计字数(千字)</view>
      </view>
      <view class="efficiency-item">
        <view class="efficiency-icon">🏆</view>
        <view class="efficiency-number">{{efficiencyStats.consecutiveDays || '0天'}}</view>
        <view class="efficiency-label">连续使用天数</view>
      </view>
    </view>
  </view>

  <!-- 每日记录统计 -->
  <view class="daily-records-section">
    <view class="section-title">
      <text class="section-icon">📊</text>
      每日记录统计
    </view>

    <view class="daily-chart-container">
      <view wx:if="{{dailyRecordsData && dailyRecordsData.length > 0}}" class="daily-chart">
        <view class="chart-header">
          <text class="chart-title">每日记录数量 ({{currentTimeRange === 'week' ? '近7天' : currentTimeRange === 'month' ? '近30天' : '近3个月'}})</text>
          <text class="chart-subtitle">记录创建趋势分析</text>
        </view>

        <view class="chart-content">
          <view class="chart-y-axis">
            <text class="y-label">{{maxDailyRecords}}</text>
            <text class="y-label">{{Math.round(maxDailyRecords * 0.8)}}</text>
            <text class="y-label">{{Math.round(maxDailyRecords * 0.6)}}</text>
            <text class="y-label">{{Math.round(maxDailyRecords * 0.4)}}</text>
            <text class="y-label">{{Math.round(maxDailyRecords * 0.2)}}</text>
            <text class="y-label">0</text>
          </view>
          <view class="chart-area">
            <view class="chart-grid">
              <view class="grid-line" wx:for="{{5}}" wx:key="*this"></view>
            </view>
            <view class="chart-bars">
              <view
                wx:for="{{dailyRecordsData}}"
                wx:key="date"
                class="chart-bar-container"
                style="width: {{100 / dailyRecordsData.length}}%"
              >
                <view
                  class="chart-bar daily-bar"
                  style="height: {{maxDailyRecords > 0 ? (item.count / maxDailyRecords) * 100 : 0}}%"
                ></view>
                <text class="bar-value">{{item.count}}</text>
                <text class="bar-label">{{item.date}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view wx:else class="chart-placeholder">
        <text style="margin-right: 16rpx;">📊</text>
        暂无每日记录数据
      </view>
    </view>
    
    <view class="quality-metrics">
      <view class="quality-metric">
        <view class="metric-score">{{qualityDetails.professional || '0'}}</view>
        <view class="metric-label">专业度</view>
      </view>
      <view class="quality-metric">
        <view class="metric-score">{{qualityDetails.personalized || '0'}}</view>
        <view class="metric-label">个性化</view>
      </view>
      <view class="quality-metric">
        <view class="metric-score">{{qualityDetails.completeness || '0'}}</view>
        <view class="metric-label">完整性</view>
      </view>
    </view>
  </view>

  <!-- 成就展示 -->
  <view class="achievement-section">
    <view class="section-title">
      <text class="section-icon">🏆</text>
      成就徽章
    </view>
    
    <view class="achievement-grid">
      <view
        wx:for="{{achievements}}"
        wx:key="id"
        class="achievement-item {{item.isUnlocked ? 'unlocked' : 'locked'}}"
        bindtap="onAchievementTap"
        data-index="{{index}}"
      >
        <view class="achievement-icon">{{item.icon}}</view>
        <view class="achievement-title">{{item.name}}</view>
        <view class="achievement-desc">{{item.description}}</view>
        <view class="achievement-status" wx:if="{{item.isUnlocked}}">
          <text class="status-unlocked">✅ 已解锁</text>
        </view>
        <view class="achievement-status" wx:else>
          <text class="status-progress">{{item.current}}/{{item.target}}</text>
        </view>
        <view class="achievement-progress">
          <view class="progress-fill" style="width: {{item.progress}}%;"></view>
        </view>
      </view>
      
      <!-- 如果没有徽章数据，显示默认提示 -->
      <view wx:if="{{achievements.length === 0}}" class="achievement-empty">
        <view class="empty-icon">🏆</view>
        <view class="empty-text">正在加载徽章系统...</view>
      </view>
    </view>
  </view>

  <!-- 隐藏的Canvas用于生成分享图片 -->
  <canvas canvas-id="reportShareCanvas" style="position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1334px;"></canvas>
</view>