<!--学生详情页面-->
<view class="container">
  
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text>加载中...</text>
  </view>

  <view wx:else>
    <!-- 学生基本信息卡片 -->
    <view class="student-info-card">
      <view class="info-header">
        <view class="avatar-section">
          <view wx:if="{{studentInfo.avatar}}" class="student-avatar">
            <image src="{{studentInfo.avatar}}" mode="aspectFill"></image>
          </view>
          <view wx:else class="student-avatar-placeholder">
            <text>{{studentInfo.surname || '学'}}</text>
          </view>
        </view>
        <view class="basic-info">
          <text class="student-name">{{studentInfo.name}}</text>
          <text class="student-class">{{studentInfo.className}}</text>
          <text class="student-number">学号：{{studentInfo.studentNumber || '无'}}</text>
        </view>
        <view class="action-btn" bindtap="showActions">
          <text class="action-icon">⚙️</text>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="statistics-section">
      <view class="stats-title">📊 记录统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{statistics.totalRecords}}</text>
          <text class="stat-label">总记录</text>
        </view>
        <view class="stat-item positive">
          <text class="stat-number">{{statistics.positiveCount}}</text>
          <text class="stat-label">积极</text>
        </view>
        <view class="stat-item negative">
          <text class="stat-number">{{statistics.negativeCount}}</text>
          <text class="stat-label">改进</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{statistics.thisWeekCount}}</text>
          <text class="stat-label">本周</text>
        </view>
      </view>
    </view>

    <!-- 记录筛选 -->
    <view class="filter-section">
      <text class="filter-label">记录筛选：</text>
      <picker range="{{filterTypes}}" range-key="label" bindchange="onFilterChange">
        <view class="filter-picker">
          <text>{{filterTypes[0].label}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>

    <!-- 记录列表 -->
    <view class="records-section">
      <view class="section-title">
        <text>📝 行为记录 ({{recordList.length}}条)</text>
      </view>
      
      <view wx:if="{{recordList.length === 0}}" class="empty-state">
        <text class="empty-text">暂无记录</text>
        <text class="empty-hint">点击右上角菜单添加记录</text>
      </view>

      <view wx:else class="record-list">
        <view 
          wx:for="{{recordList}}" 
          wx:key="id" 
          class="record-item {{item.behaviorType}}"
          bindtap="onRecordTap"
          data-record="{{item}}"
        >
          <view class="record-header">
            <view class="record-type-icon">
              <text wx:if="{{item.behaviorType === 'positive'}}">👍</text>
              <text wx:elif="{{item.behaviorType === 'negative'}}">👎</text>
              <text wx:elif="{{item.behaviorType === 'academic'}}">📚</text>
              <text wx:elif="{{item.behaviorType === 'social'}}">👥</text>
              <text wx:elif="{{item.behaviorType === 'creative'}}">💡</text>
              <text wx:else>📋</text>
            </view>
            <view class="record-content">
              <view class="record-action-row">
                <text class="record-action">{{item.content || item.action || '无内容'}}</text>
                <view wx:if="{{item.images && item.images.length > 0}}" class="image-indicator">
                  <text class="image-icon">📷</text>
                  <text class="image-count">{{item.images.length}}</text>
                </view>
              </view>
              <text class="record-time">{{item.createTime}}</text>
            </view>
          </view>
          
          <view wx:if="{{item.description}}" class="record-description">
            <text>{{item.description}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button class="action-button primary" bindtap="navigateToAddRecord">
        添加记录
      </button>
      <button class="action-button secondary" bindtap="navigateToGenerateComment">
        生成评语
      </button>
    </view>

  </view>

  <!-- 操作菜单 -->
  <view wx:if="{{showActionSheet}}" class="action-sheet-mask" bindtap="hideActionSheet">
    <view class="action-sheet" catchtap="">
      <view class="action-sheet-header">
        <text>选择操作</text>
      </view>
      <view class="action-options">
        <view 
          wx:for="{{actionOptions}}" 
          wx:key="value" 
          class="action-option"
          bindtap="onActionSelect"
          data-value="{{item.value}}"
        >
          <text>{{item.name}}</text>
        </view>
      </view>
      <view class="action-cancel" bindtap="hideActionSheet">
        <text>取消</text>
      </view>
    </view>
  </view>

</view>