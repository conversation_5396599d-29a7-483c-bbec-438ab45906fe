import React from 'react'
import { But<PERSON>, Tooltip } from 'antd'
import { BulbOutlined, BulbFilled } from '@ant-design/icons'
import { useThemeStore } from '@/stores/themeStore'

interface ThemeToggleProps {
  size?: 'small' | 'middle' | 'large'
  shape?: 'default' | 'circle' | 'round'
  type?: 'default' | 'primary' | 'ghost' | 'dashed' | 'link' | 'text'
  className?: string
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  size = 'middle',
  shape = 'circle',
  type = 'text',
  className = ''
}) => {
  const { mode, isDark, toggleTheme } = useThemeStore()

  const handleToggle = () => {
    toggleTheme()
  }

  return (
    <Tooltip 
      title={isDark ? '切换到日间模式' : '切换到夜间模式'} 
      placement="bottom"
    >
      <Button
        type={type}
        shape={shape}
        size={size}
        icon={isDark ? <BulbFilled /> : <BulbOutlined />}
        onClick={handleToggle}
        className={`theme-toggle ${className} ${isDark ? 'theme-dark' : 'theme-light'}`}
        style={{
          color: isDark ? '#fbbf24' : '#64748b',
          borderColor: isDark ? '#374151' : '#e2e8f0',
          transition: 'all 0.3s ease'
        }}
      />
    </Tooltip>
  )
}

export default ThemeToggle