/**
 * 用户登录云函数
 * 处理微信小程序用户登录和身份验证
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('用户登录请求:', event)
  
  try {
    // 获取微信用户信息
    const { OPENID } = cloud.getWXContext()
    const { userInfo, role = 'teacher' } = event

    if (!OPENID) {
      throw new Error('无法获取用户OpenID')
    }

    // 🔒 输入验证：验证role参数的合法性
    const validRoles = ['teacher', 'admin', 'student'];
    if (!validRoles.includes(role)) {
      console.warn(`⚠️ 无效的role参数: ${role}, 使用默认值: teacher`);
      role = 'teacher';
    }

    // 🔒 输入验证：验证userInfo结构
    if (userInfo && typeof userInfo !== 'object') {
      console.warn('⚠️ userInfo参数格式无效');
      userInfo = null;
    }
    
    // 检查用户是否已存在
    const userResult = await db.collection('users').where({
      openid: OPENID
    }).get()
    
    let user
    const currentTime = new Date()
    
    if (userResult.data.length === 0) {
      // 新用户，创建用户记录
      const userData = {
        openid: OPENID,
        role: role,
        profile: userInfo || {},
        // 提取微信用户信息到顶层字段
        nickName: userInfo?.nickName || '微信用户',
        avatarUrl: userInfo?.avatarUrl || '',
        name: '', // 添加真实姓名字段，初始为空
        gender: userInfo?.gender || 0,
        city: userInfo?.city || '',
        province: userInfo?.province || '',
        country: userInfo?.country || '',
        school: '', // 添加学校字段
        subject: '', // 添加学科字段
        grade: '', // 添加年级字段
        phone: '', // 添加电话字段
        email: '', // 添加邮箱字段
        createTime: currentTime,
        lastLoginTime: currentTime,
        status: 'active',
        loginCount: 1
      }

      const createResult = await db.collection('users').add({
        data: userData
      })

      user = {
        _id: createResult._id,
        ...userData
      }

      console.log('新用户注册成功:', user)
    } else {
      // 老用户，更新登录信息
      user = userResult.data[0]

      const updateData = {
        lastLoginTime: currentTime,
        loginCount: db.command.inc(1)
      }

      // 如果提供了新的用户信息，更新微信信息
      if (userInfo) {
        updateData.profile = db.command.set(userInfo)
        updateData.nickName = userInfo.nickName || user.nickName
        updateData.avatarUrl = userInfo.avatarUrl || user.avatarUrl
        updateData.gender = userInfo.gender !== undefined ? userInfo.gender : user.gender
        updateData.city = userInfo.city || user.city
        updateData.province = userInfo.province || user.province
        updateData.country = userInfo.country || user.country
      }

      await db.collection('users').doc(user._id).update({
        data: updateData
      })

      // 更新本地用户对象
      user.lastLoginTime = currentTime
      user.loginCount = (user.loginCount || 0) + 1
      if (userInfo) {
        user.profile = userInfo
        user.nickName = userInfo.nickName || user.nickName
        user.avatarUrl = userInfo.avatarUrl || user.avatarUrl
        user.gender = userInfo.gender !== undefined ? userInfo.gender : user.gender
        user.city = userInfo.city || user.city
        user.province = userInfo.province || user.province
        user.country = userInfo.country || user.country
      }

      console.log('用户登录成功，微信信息已更新:', user.openid)
    }
    
    // 返回用户信息
    return {
      code: 200,
      message: '登录成功',
      data: {
        userId: user._id,
        openid: user.openid,
        role: user.role,
        profile: user.profile,
        isNewUser: userResult.data.length === 0,
        lastLoginTime: user.lastLoginTime,
        loginCount: user.loginCount
      }
    }
    
  } catch (error) {
    // 🔒 安全处理：记录详细错误到服务器日志，但不返回给客户端
    console.error('用户登录失败:', {
      error: error.message,
      stack: error.stack,
      openid: cloud.getWXContext().OPENID,
      timestamp: new Date().toISOString()
    });

    // 🔒 安全处理：只返回通用错误信息给客户端
    let userMessage = '登录失败，请稍后重试';

    // 只有特定的已知错误才返回具体信息
    if (error.message === '无法获取用户OpenID') {
      userMessage = '微信授权失败，请重新登录';
    }

    return {
      code: 500,
      message: userMessage,
      data: null,
      success: false
    }
  }
}