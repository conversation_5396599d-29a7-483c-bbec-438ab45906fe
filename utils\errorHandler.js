/**
 * 全局错误处理和网络监控
 * 提供统一的错误处理、日志记录和用户反馈
 */

const { ERROR_CODES, APP_INFO, TIMING } = require('./constants.js');

class ErrorHandler {
  constructor() {
    this.errorLog = [];
    this.maxLogSize = 100;
    this.networkStatus = 'unknown';
    this.retryQueue = [];
    
    this.init();
  }

  /**
   * 初始化错误处理器
   */
  init() {
    // 只在微信小程序环境中初始化
    if (typeof wx !== 'undefined') {
      // 监听网络状态
      this.setupNetworkMonitor();

      // 设置全局错误监听
      this.setupGlobalErrorHandler();

      // 从本地存储恢复错误日志
      this.loadErrorLog();
    } else {
      console.log('非微信小程序环境，跳过wx API初始化');
    }
  }

  /**
   * 设置网络监控
   */
  setupNetworkMonitor() {
    // 检查是否在微信小程序环境中
    if (typeof wx === 'undefined') {
      console.log('非微信环境，跳过网络监控设置');
      return;
    }

    // 获取初始网络状态
    wx.getNetworkType({
      success: (res) => {
        this.networkStatus = res.networkType;
      }
    });

    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      const oldStatus = this.networkStatus;
      this.networkStatus = res.isConnected ? res.networkType : 'none';

      if (res.isConnected && oldStatus === 'none') {
        // 网络恢复，处理重试队列
        this.processRetryQueue();
        
        wx.showToast({
          title: '网络已恢复',
          icon: 'success',
          duration: 1500
        });
      } else if (!res.isConnected) {
        wx.showToast({
          title: '网络连接断开',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }

  /**
   * 设置全局错误处理
   */
  setupGlobalErrorHandler() {
    // 监听小程序错误
    wx.onError((error) => {
      this.handleError({
        type: 'runtime',
        message: error,
        timestamp: new Date().getTime(),
        page: getCurrentPages().pop()?.route || 'unknown'
      });
    });

    // 监听未处理的Promise拒绝
    wx.onUnhandledRejection((res) => {
      this.handleError({
        type: 'promise',
        message: res.reason,
        timestamp: new Date().getTime(),
        page: getCurrentPages().pop()?.route || 'unknown'
      });
    });
  }

  /**
   * 处理错误
   */
  handleError(error) {
    // 记录错误日志
    this.logError(error);
    
    // 根据错误类型提供不同的处理策略
    switch (error.type) {
      case 'network':
        this.handleNetworkError(error);
        break;
      case 'api':
        this.handleAPIError(error);
        break;
      case 'validation':
        this.handleValidationError(error);
        break;
      case 'runtime':
        this.handleRuntimeError(error);
        break;
      default:
        this.handleGenericError(error);
    }
  }

  /**
   * 记录错误日志
   */
  logError(error) {
    const logEntry = {
      ...error,
      id: this.generateErrorId(),
      userAgent: wx.getSystemInfoSync(),
      timestamp: error.timestamp || new Date().getTime()
    };

    this.errorLog.unshift(logEntry);
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }
    
    // 保存到本地存储
    this.saveErrorLog();
    
    console.error('错误记录:', logEntry);
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(error) {
    if (this.networkStatus === 'none') {
      wx.showToast({
        title: '网络连接失败',
        icon: 'none',
        duration: 2000
      });
    } else {
      // 添加到重试队列
      if (error.retryable !== false) {
        this.addToRetryQueue(error);
      }
      
      wx.showToast({
        title: '网络请求失败',
        icon: 'none',
        duration: 2000
      });
    }
  }

  /**
   * 处理API错误
   */
  handleAPIError(error) {
    let message = '服务异常，请稍后重试';
    
    if (error.code) {
      switch (error.code) {
        case 401:
          message = '登录已过期，请重新登录';
          // 可以在这里触发重新登录
          break;
        case 403:
          message = '权限不足';
          break;
        case 404:
          message = '请求的资源不存在';
          break;
        case 500:
          message = '服务器内部错误';
          break;
        default:
          message = error.message || message;
      }
    }
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * 处理验证错误
   */
  handleValidationError(error) {
    wx.showToast({
      title: error.message || '输入数据有误',
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * 处理运行时错误
   */
  handleRuntimeError(error) {
    console.error('运行时错误:', error);
    
    // 在开发环境显示详细错误信息
    if (wx.getAccountInfoSync().miniProgram.envVersion === 'develop') {
      wx.showModal({
        title: '运行时错误',
        content: error.message,
        showCancel: false
      });
    } else {
      wx.showToast({
        title: '应用异常，请重启',
        icon: 'none',
        duration: 2000
      });
    }
  }

  /**
   * 处理通用错误
   */
  handleGenericError(error) {
    wx.showToast({
      title: error.message || '操作失败',
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * 添加到重试队列
   */
  addToRetryQueue(error) {
    if (error.retryFunction && typeof error.retryFunction === 'function') {
      this.retryQueue.push({
        id: this.generateErrorId(),
        error,
        retryCount: 0,
        maxRetries: error.maxRetries || 3,
        retryFunction: error.retryFunction
      });
    }
  }

  /**
   * 处理重试队列
   */
  async processRetryQueue() {
    if (this.retryQueue.length === 0) return;

    const queue = [...this.retryQueue];
    this.retryQueue = [];
    
    for (const item of queue) {
      try {
        await item.retryFunction();

      } catch (error) {
        item.retryCount++;
        
        if (item.retryCount < item.maxRetries) {
          this.retryQueue.push(item);

        } else {

        }
      }
    }
  }

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 保存错误日志
   */
  saveErrorLog() {
    try {
      wx.setStorageSync('errorLog', this.errorLog);
    } catch (error) {
      console.error('保存错误日志失败:', error);
    }
  }

  /**
   * 加载错误日志
   */
  loadErrorLog() {
    try {
      const savedLog = wx.getStorageSync('errorLog');
      if (savedLog && Array.isArray(savedLog)) {
        this.errorLog = savedLog;
      }
    } catch (error) {
      console.error('加载错误日志失败:', error);
    }
  }

  /**
   * 获取错误日志
   */
  getErrorLog() {
    return this.errorLog;
  }

  /**
   * 清除错误日志
   */
  clearErrorLog() {
    this.errorLog = [];
    wx.removeStorageSync('errorLog');
  }

  /**
   * 获取网络状态
   */
  getNetworkStatus() {
    return this.networkStatus;
  }

  /**
   * 检查网络连接
   */
  isOnline() {
    return this.networkStatus !== 'none';
  }
}

// 创建全局实例
const errorHandler = new ErrorHandler();

module.exports = errorHandler;
