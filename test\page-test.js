/**
 * 🔒 页面环境安全测试脚本
 * 在小程序页面的 onLoad 或按钮点击事件中调用
 */

// 安全测试页面方法
const runPageSecurityTest = () => {
  console.log('🔒 开始页面环境安全测试...');

  // 测试结果数组
  const testResults = [];

  // 添加测试结果的辅助函数
  const addTestResult = (category, testName, passed, details) => {
    testResults.push({
      category,
      testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} [${category}] ${testName}: ${details}`);
  };

  // 用户ID验证函数
  const validateUserId = (userId) => {
    if (!userId || typeof userId !== 'string') return false;
    if (userId.startsWith('test_user_')) return false;
    if (['admin', 'root', 'system'].includes(userId.toLowerCase())) return false;
    if (userId.length < 10) return false;
    return true;
  };

  // 1. 测试云服务访问
  console.log('🔍 测试云服务访问...');
  try {
    if (typeof wx !== 'undefined' && wx.cloud) {
      addTestResult('CLOUD', 'CloudServiceAccess', true, '云服务访问正常');
    } else {
      addTestResult('CLOUD', 'CloudServiceAccess', false, '云服务无法访问');
    }
  } catch (error) {
    addTestResult('CLOUD', 'CloudServiceAccess', false, `云服务错误: ${error.message}`);
  }

  // 2. 测试存储安全
  console.log('🔍 测试存储安全...');
  try {
    // 测试用户ID存储和验证
    const testUserId = 'oXwLu5G2Fj8PtPbIq7ZdFwYxHkLm';
    wx.setStorageSync('test_user_id', testUserId);
    const storedId = wx.getStorageSync('test_user_id');
    const isValidStored = validateUserId(storedId);
    
    addTestResult('STORAGE', 'UserIdValidation', isValidStored, '用户ID存储验证正常');
    
    // 清理测试数据
    wx.removeStorageSync('test_user_id');
  } catch (error) {
    addTestResult('STORAGE', 'UserIdValidation', false, `存储测试失败: ${error.message}`);
  }

  // 3. 测试数据隔离
  console.log('🔍 测试数据隔离...');
  try {
    // 模拟多用户数据
    const mockData = [
      { id: 1, userId: 'user1', content: 'data1' },
      { id: 2, userId: 'user2', content: 'data2' }
    ];
    
    // 数据过滤函数
    const filterByUser = (data, userId) => {
      return data.filter(item => item.userId === userId);
    };
    
    const user1Data = filterByUser(mockData, 'user1');
    const isolationWorking = user1Data.length === 1 && user1Data[0].userId === 'user1';
    
    addTestResult('ISOLATION', 'DataFiltering', isolationWorking, '数据隔离机制正常');
  } catch (error) {
    addTestResult('ISOLATION', 'DataFiltering', false, `数据隔离测试失败: ${error.message}`);
  }

  // 4. 测试页面权限
  console.log('🔍 测试页面权限...');
  try {
    // 获取当前页面信息
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const hasPageAccess = currentPage && currentPage.route;
    
    addTestResult('PAGE', 'PageAccess', hasPageAccess, `当前页面: ${currentPage ? currentPage.route : 'unknown'}`);
  } catch (error) {
    addTestResult('PAGE', 'PageAccess', false, `页面访问测试失败: ${error.message}`);
  }

  // 5. 生成报告
  const totalTests = testResults.length;
  const passedTests = testResults.filter(r => r.passed).length;
  const passRate = Math.round((passedTests / totalTests) * 100);

  console.log('\n📊 页面安全测试报告:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过: ${passedTests}`);
  console.log(`失败: ${totalTests - passedTests}`);
  console.log(`通过率: ${passRate}%`);

  if (passRate >= 90) {
    console.log('🎉 页面安全性: 优秀');
  } else if (passRate >= 70) {
    console.log('⚠️ 页面安全性: 良好');
  } else {
    console.log('🚨 页面安全性: 需要改进');
  }

  console.log('━━━━━━━━━━━━━━━━━━━━━━━━\n');

  return {
    totalTests,
    passedTests,
    passRate,
    results: testResults
  };
};

// 导出函数供页面使用
module.exports = {
  runPageSecurityTest
};