/**
 * 云函数相关类型定义
 */

// 云函数响应基础接口
export interface CloudFunctionResponse<T = any> {
  code: number
  message: string
  data?: T
  timestamp?: number
  error?: string
}

// 云函数请求基础接口
export interface CloudFunctionRequest {
  action: string
  timestamp?: number
  [key: string]: any
}

// 管理员登录相关类型
export interface AdminLoginRequest {
  username: string
  password: string
  loginTime?: string
}

export interface AdminLoginResponse {
  userId: string
  username: string
  role: string
  token: string
  refreshToken: string
  expiresAt: number
  profile?: {
    nickName: string
    avatar?: string
  }
}

// Token验证相关类型
export interface TokenValidationRequest {
  token: string
}

export interface TokenValidationResponse {
  valid: boolean
  token?: string
  userId?: string
  role?: string
  expiresAt?: number
}

// 仪表盘统计数据相关类型
export interface DashboardStatsRequest {
  includeRealtime?: boolean
  includeGrowth?: boolean
  timeRange?: string
}

export interface DashboardStatsResponse {
  totalUsers: number
  todayComments: number
  aiCalls: number
  satisfaction: number
  activeUsers: number
  systemStatus: 'healthy' | 'warning' | 'error'
  lastUpdated: string
  growth?: {
    userGrowth: number
    commentGrowth: number
    aiUsageGrowth: number
  }
}

// 实时数据相关类型
export interface RealtimeDataRequest {
  dataTypes: string[]
}

export interface RealtimeDataResponse {
  activities: ActivityRecord[]
  onlineUsers: {
    count: number
    users: OnlineUser[]
  }
  systemMetrics: {
    cpu: number
    memory: number
    storage: number
    apiResponseTime: number
    activeConnections: number
  }
  alerts: SystemAlert[]
}

export interface ActivityRecord {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  action: string
  actionType: 'comment_generate' | 'data_import' | 'config_update' | 'report_export' | 'batch_operation'
  timestamp: string
  metadata?: Record<string, any>
}

export interface OnlineUser {
  id: string
  username?: string
  nickName: string
  avatar?: string
  lastActivity: string
  sessionDuration: number
}

export interface SystemAlert {
  id: string
  type: 'info' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  resolved: boolean
}

// 用户管理相关类型
export interface GetUsersRequest {
  page: number
  limit: number
  includeStats?: boolean
  filters?: {
    role?: string
    status?: string
    dateRange?: [string, string]
  }
}

export interface GetUsersResponse {
  users: User[]
  total: number
  page: number
  limit: number
  totalPages: number
  stats?: {
    total: number
    active: number
    newToday: number
    growthRate: number
  }
}

export interface User {
  id: string
  openid?: string
  username?: string
  nickName: string
  avatar?: string
  role: 'teacher' | 'admin'
  status: 'active' | 'inactive' | 'banned'
  createdAt: string
  lastLoginAt?: string
  stats?: {
    commentsGenerated: number
    aiCallsUsed: number
    lastActivity: string
  }
}

// 评语管理相关类型
export interface GetCommentsRequest {
  page: number
  limit: number
  includeUser?: boolean
  filters?: {
    userId?: string
    dateRange?: [string, string]
    status?: string
    aiGenerated?: boolean
  }
}

export interface GetCommentsResponse {
  comments: Comment[]
  total: number
  page: number
  limit: number
  totalPages: number
  stats?: {
    total: number
    todayCount: number
    averageLength: number
    popularTemplates: string[]
  }
}

export interface Comment {
  id: string
  userId: string
  studentId: string
  studentName: string
  content: string
  isAiGenerated: boolean
  template?: string
  satisfaction?: number
  createdAt: string
  updatedAt?: string
  user?: {
    nickName: string
    avatar?: string
  }
}

// 批量操作相关类型
export interface BatchOperationRequest {
  operation: 'delete' | 'export' | 'update' | 'approve'
  items: string[]
  batchSize?: number
  parameters?: Record<string, any>
}

export interface BatchOperationResponse {
  success: boolean
  processed: number
  failed: number
  errors?: string[]
  results?: any[]
}

// 系统配置相关类型
export interface SystemConfigRequest {
  module?: string
}

export interface SystemConfigResponse {
  aiConfig: {
    provider: string
    model: string
    maxTokens: number
    temperature: number
    timeout: number
  }
  userLimits: {
    dailyAiCalls: number
    monthlyAiCalls: number
    maxStudents: number
  }
  systemSettings: {
    maintenanceMode: boolean
    registrationOpen: boolean
    version: string
    environment: string
  }
  status: 'healthy' | 'warning' | 'error'
  uptime: number
  lastUpdated: string
}

export interface UpdateSystemConfigRequest {
  config: Partial<SystemConfigResponse>
}

// AI使用统计相关类型
export interface AIUsageStatsRequest {
  timeRange: '1d' | '7d' | '30d' | '90d'
  userId?: string
}

export interface AIUsageStatsResponse {
  totalCalls: number
  todayCalls: number
  successfulCalls: number
  failedCalls: number
  averageResponseTime: number
  successRate: number
  tokenUsage: {
    total: number
    average: number
    peak: number
  }
  timeline: {
    date: string
    calls: number
    tokens: number
    avgResponseTime: number
  }[]
  topUsers: {
    userId: string
    userName: string
    calls: number
    tokens: number
  }[]
}

// 数据导出相关类型
export interface DataExportRequest {
  dataType: 'dashboard' | 'users' | 'comments' | 'ai_usage' | 'system_logs'
  format: 'excel' | 'csv' | 'json'
  filters?: Record<string, any>
  dateRange?: [string, string]
}

export interface DataExportResponse {
  success: boolean
  downloadUrl?: string
  filename?: string
  fileSize?: number
  expiresAt?: string
}

// 云开发环境配置类型
export interface CloudbaseEnvironment {
  envId: string
  region: string
  appId?: string
}

// 云函数调用选项类型
export interface CloudFunctionCallOptions {
  timeout?: number
  retries?: number
  fallbackToHttp?: boolean
}

// 错误类型定义
export interface CloudFunctionError {
  code: string
  message: string
  requestId?: string
  timestamp: number
  stack?: string
}

// 统一的云函数服务接口
export interface ICloudFunctionService {
  // 认证相关
  adminLogin(username: string, password: string): Promise<CloudFunctionResponse<AdminLoginResponse>>
  validateToken(token: string): Promise<CloudFunctionResponse<TokenValidationResponse>>
  
  // 数据获取
  getDashboardStats(): Promise<CloudFunctionResponse<DashboardStatsResponse>>
  getRealtimeData(): Promise<CloudFunctionResponse<RealtimeDataResponse>>
  getUsers(page: number, limit: number): Promise<CloudFunctionResponse<GetUsersResponse>>
  getComments(page: number, limit: number, filters?: any): Promise<CloudFunctionResponse<GetCommentsResponse>>
  
  // 系统管理
  getSystemConfig(): Promise<CloudFunctionResponse<SystemConfigResponse>>
  updateSystemConfig(config: any): Promise<CloudFunctionResponse<boolean>>
  
  // AI相关
  getAIUsageStats(timeRange: string): Promise<CloudFunctionResponse<AIUsageStatsResponse>>
  
  // 批量操作
  batchOperation(operation: string, items: any[]): Promise<CloudFunctionResponse<BatchOperationResponse>>
  exportData(dataType: string, filters?: any): Promise<CloudFunctionResponse<DataExportResponse>>
  
  // 直接云函数调用
  callFunction<T = any>(functionName: string, data?: any): Promise<CloudFunctionResponse<T>>
}

export default ICloudFunctionService